Stack trace:
Frame         Function      Args
0007FFFFB740  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFB740, 0007FFFFA640) msys-2.0.dll+0x1FE8E
0007FFFFB740  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA18) msys-2.0.dll+0x67F9
0007FFFFB740  000210046832 (000210286019, 0007FFFFB5F8, 0007FFFFB740, 000000000000) msys-2.0.dll+0x6832
0007FFFFB740  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFB740  000210068E24 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBA20  00021006A225 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFBA9730000 ntdll.dll
7FFBA8320000 KERNEL32.DLL
7FFBA6900000 KERNELBASE.dll
7FFBA84A0000 USER32.dll
7FFBA6850000 win32u.dll
7FFBA8660000 GDI32.dll
7FFBA7010000 gdi32full.dll
7FFBA6F70000 msvcp_win.dll
7FFBA6E50000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFBA76B0000 advapi32.dll
7FFBA9150000 msvcrt.dll
7FFBA8F40000 sechost.dll
7FFBA6820000 bcrypt.dll
7FFBA77C0000 RPCRT4.dll
7FFBA5FB0000 CRYPTBASE.DLL
7FFBA7340000 bcryptPrimitives.dll
7FFBA7770000 IMM32.DLL
