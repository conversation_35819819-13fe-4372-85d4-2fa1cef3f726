part of 'api_bloc.dart';

@immutable
abstract class ApiEvent {}

class CallBarCodeApi extends ApiEvent {
  final String barcodeNumber;
  final bool? isTimedService;
  CallBarCodeApi({required this.barcodeNumber,required this.isTimedService});
}

class CheckBarCodeApi extends ApiEvent {
  final String barcodeNumber;
  CheckBarCodeApi({required this.barcodeNumber});
}

class ResetCheckBarCodeApi extends ApiEvent {}

class GetEquipment extends ApiEvent {
  final String barcodeNumber;
  GetEquipment({required this.barcodeNumber});
}

class ResetBarCodeApi extends ApiEvent {}

class GetCustomizationData extends ApiEvent {}

class InitialTerminalData extends ApiEvent {}

class FetchTerminalData extends ApiEvent {
  final String locationId;
  FetchTerminalData({required this.locationId});
}

class FetchSingleServiceDetail extends ApiEvent {
  final int requestId;
  final bool? isFromAudit;
  final bool? isRestricted;
  final bool? isTimedService;
  FetchSingleServiceDetail(this.isFromAudit,this.isRestricted, this.isTimedService, {required this.requestId});
}

class FetchEquipmentListForAudit extends ApiEvent {}

class CreateNewAudit extends ApiEvent {
  final String locationId;
  final String comment;
  String? serviceType;
  CreateNewAudit({required this.locationId, required this.comment,this.serviceType});
}

class ValidateAuditBarcodes extends ApiEvent {
  final List<AuditEquipment> equipmentList;
  final int auditId;
  ValidateAuditBarcodes({required this.equipmentList, required this.auditId});
}

class UpdateValidateAuditBarcodes extends ApiEvent {
  final List<AuditEquipment> equipmentList;
  final AuditEquipment auditEquipment;
  UpdateValidateAuditBarcodes(this.equipmentList, this.auditEquipment);
}

class RefreshValidatedAuditData extends ApiEvent {}

class GetServiceRequestCount extends ApiEvent {}

class GetAirCraftFleet extends ApiEvent {}

class GetAirCraftTails extends ApiEvent {
  final String fleetName;
  final String? subFleetName;
  GetAirCraftTails({required this.fleetName, this.subFleetName});
}

class CancelServiceRequest extends ApiEvent {
  final int serviceRequestId;
  final String comment;
  CancelServiceRequest({required this.serviceRequestId, required this.comment});
}
