part of 'audit_bloc.dart';

@immutable
abstract class AuditEvent {}

class FetchAuditList extends AuditEvent {
  final bool refresh;
  final String query;
  int? auditId;
  final String? location;
  final String? serviceType;
  final String? description;

  FetchAuditList(this.location, this.auditId,this.serviceType, this.description, {required this.refresh, this.query = ''});
}

class ResetAuditListData extends AuditEvent {}

class NewEquipmentListData extends AuditEvent {
  final List<String> equipmentList;

  NewEquipmentListData({required this.equipmentList});
}

class SubmitAuditList extends AuditEvent {
  final List<AuditEquipment>? equipmentList;
  final int auditId;
  final LocationData? locationData;
  final bool isConditionAudit;
  final bool? isSaveAudit;

  SubmitAuditList({this.equipmentList, required this.auditId, this.locationData, required this.isConditionAudit,this.isSaveAudit});
}
