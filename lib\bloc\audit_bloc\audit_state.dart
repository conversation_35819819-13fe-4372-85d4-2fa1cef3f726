part of 'audit_bloc.dart';

@immutable
abstract class AuditState {}

class AuditListDataInitial extends AuditState {}

class FetchingAuditListData extends AuditState {
  final List<Audit> oldList;
  final bool isFirstFetch;

  FetchingAuditListData(this.oldList, {this.isFirstFetch = false});
}

class FetchedAuditListData extends AuditState {
  final AuditResponse auditResponse;
  FetchedAuditListData(this.auditResponse);
}

class FetchAuditListDataError extends AuditState {
  final String errorMessage;
  FetchAuditListDataError({required this.errorMessage});
}

class SubmittingAuditListData extends AuditState {
   bool isSaveAudit = false;
  SubmittingAuditListData({this.isSaveAudit=false});
}

class SubmittedAuditListData extends AuditState {
  final int auditId;
  bool isSaveAudit = false;
  SubmittedAuditListData({required this.auditId,this.isSaveAudit=false});
}

class SubmitAuditListDataError extends AuditState {
  final String errorMessage;
  SubmitAuditListDataError({required this.errorMessage});
}

class SubmittingAirlineAuditListData extends AuditState {}

class SubmittedAirlineAuditListData extends AuditState {
  final int auditId;
  SubmittedAirlineAuditListData({required this.auditId});
}

class SubmitAirlineAuditListDataError extends AuditState {
  final String errorMessage;
  SubmitAirlineAuditListDataError({required this.errorMessage});
}
