import 'package:equatable/equatable.dart';

abstract class OpenServiceRequestEvent extends Equatable {
  const OpenServiceRequestEvent();

  @override
  List<Object> get props => [];
}

class FetchOpenServiceRequestCount extends OpenServiceRequestEvent {
  final List<int> requestIds;
  final bool isRestricted;// List of integers to pass to the API

  const FetchOpenServiceRequestCount(this.requestIds,this.isRestricted);

  @override
  List<Object> get props => [requestIds];
}
