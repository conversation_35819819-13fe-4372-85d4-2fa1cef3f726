import 'dart:async';

import 'package:alink/data/dao/repair_dao.dart';
import 'package:alink/database/database.dart';
import 'package:alink/isolate/http_isolate_repair.dart';
import 'package:alink/service_locator.dart';
import 'package:alink/util/application_util.dart';
import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../logger/logger.dart';

part 'repair_event.dart';
part 'repair_state.dart';

class RepairBloc extends Bloc<RepairEvent, RepairState> {
  final RepairDao repairDao;

  RepairBloc({required this.repairDao}) : super(RepairInitial());

  @override
  Stream<RepairState> mapEventToState(
    RepairEvent event,
  ) async* {
    if (event is SaveRepairRequest) {
      yield RepairRequestSaving();
      try {
        final response = await repairDao.insertRepair(event.repairCompanion);
        if (response is RepairData) {
          yield RepairSaved(response);
        } else {
          yield RepairError(error: response);
        }
      } catch (e) {
        yield RepairError(error: "Error occur while fetching," + e.toString());
      }
    }

    if (event is SendPendingRepairToServer) {
      yield SendingPendingRepairToServer();
      try {
        //Caliing isolate now
        print('calling isolate now');
        IsolateRepairResponse response =
            await HttpIsolateRepairService().sendRepairThroughIsolate();
        print('Service Request Send:' + response.count.toString());
        if (response.count > 0) {
          ApplicationUtil.printInfoLog(methodName: "Calling ${response.apiUrl} api", statusCode: response.responseCode, contentLength: response.responseLength, conversationId: response.conversationId);
          if (response.token != null) {
            getIt<SharedPreferences>()
                .setString('token', 'Bearer ' + response.token!);
          }
          yield SentPendingRepairToServer(count: response.count);
        } else if (response.hasError) {
          Logger.e(
              '${ApplicationUtil.getFormattedCurrentDateAndTime()} ERROR IN SERVICE REQUEST: ' +
                  response.error);
          yield NetworkErrorInRepair(
              pendingCount: response.pendingRequest, error: response.error);
        } else {
          yield NoPendingRepairInDatabase(error: 'NO_DATA');
        }
      } catch (e) {
        print(e.toString());
        yield NetworkErrorInRepair(error: "Unknown", pendingCount: 0);
      }
    }
    if (event is ResetRepairUI) {
      yield RepairInitial();
    }
  }
}
