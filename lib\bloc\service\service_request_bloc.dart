import 'package:alink/data/dao/audit_status_dao.dart';
import 'package:alink/data/dao/service_request_dao.dart';
import 'package:alink/data/model/audit_response.dart';
import 'package:alink/data/repository/api_repository.dart';
import 'package:alink/database/database.dart';
import 'package:alink/isolate/http_isolate_service.dart';
import 'package:alink/service_locator.dart';
import 'package:alink/util/application_util.dart';
import 'package:alink/util/enums/app_enum.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../logger/logger.dart';

part 'service_request_event.dart';
part 'service_request_state.dart';

class ServiceRequestBloc extends Bloc<ServiceRequestEvent, ServiceRequestState> {
  final ServiceRequestDao serviceRequestDao;
  final AuditStatusDao auditStatusDao;
  final ApiRepository apiRepository;
  static List<AuditEquipment> _equipmentList = [];

  static List<AuditEquipment> get equipmentList => _equipmentList;

  static set equipmentList(List<AuditEquipment> value) {
    _equipmentList = value;
  }

  ServiceRequestBloc({required this.serviceRequestDao, required this.auditStatusDao, required this.apiRepository}) : super(ServiceRequestInitial());
  Logger logger = Logger();
  @override
  Stream<ServiceRequestState> mapEventToState(
    ServiceRequestEvent event,
  ) async* {
    if (event is FetchServiceRequest) {
      yield ServiceRequestLoading();
      try {
        final response = await serviceRequestDao.getAllServiceRequest();
        if (response is List<ServiceRequestData>) {
          Logger.i('Fetch service request called and received data');
          yield ServiceRequestLoaded(response);
        }
        if (response is String) {
          Logger.i('Fetch service request called and received error');
          yield ServiceRequestError(error: response);
        }
      } catch (_) {
        Logger.e('Fetch service request called and received exception');
        yield ServiceRequestError(error: "error occur while fetching");
      }
    }

    if (event is SaveServiceRequest) {
      yield ServiceRequestSaving();
      try {
        final response = await serviceRequestDao.insertServiceRequest(event.serviceRequestCompanion);
        if (response is ServiceRequestData) {
          Logger.i('Save service request called and received data');
          yield ServiceRequestSaved(response);
        } else {
          Logger.i('Save service request called and received error');
          yield ServiceRequestError(error: response);
        }
      } catch (e) {
        Logger.e('Save service request called and received exception');
        yield ServiceRequestError(error: "Error occur while fetching," + e.toString());
      }
    }

    if (event is UpdateServiceRequest) {
      yield ServiceRequestUpdating();
      try {
        dynamic response = await serviceRequestDao.updateServiceRequest(event.serviceRequestData);
        if (response is bool) {
          Logger.i('Update service request called and successfully updated');
          yield ServiceRequestUpdated();
        } else {
          Logger.i('Update service request called and failed to update');
          yield ServiceRequestError(error: "Some error occur while updating");
        }
      } catch (_) {
        Logger.i('Update service request called and received exception');
        yield ServiceRequestError(error: "error occur while updating");
      }
    }
    if (event is DeleteServiceRequest) {
      yield ServiceRequestDeleting();
      try {
        dynamic response = await serviceRequestDao.deleteServiceRequest(event.serviceRequestCompanion);
        if (response is int) {
          Logger.i('Delete service request called and successfully deleted');
          yield ServiceRequestDeleted();
        } else {
          Logger.i('Delete service request called and failed to delete');
          yield ServiceRequestError(error: response);
        }
      } catch (_) {
        Logger.i('Delete service request called and received exception');
        yield ServiceRequestError(error: "error occur while deleting");
      }
    }
    if (event is ServiceRequestSendNotification) {
      try {
        yield ServiceRequestNotificationSent(count: event.count, message: event.message);
      } catch (_) {
        yield ServiceRequestError(error: "Could not send notification");
      }
    }
    if (event is ServiceRequestSendErrorNotification) {
      yield ServiceRequestErrorNotificationSent(message: event.message);
    }
    if (event is DismissServiceRequestNotification) {
      yield ServiceNotificationDismissed();
    }
    if (event is DismissServiceRequestErrorNotification) {
      yield ServiceRequestErrorNotificationDismissed();
    }

    if (event is SendPendingServiceRequestToServer) {
      yield SendingPendingServiceRequestToServer();
      try {
        //Caliing isolate now
        Logger.i('calling isolate now');
        IsolateServiceResponse response = await HttpIsolateService().sendServiceRequestThroughIsolate();
        if (response.sentServiceRequestCount > 0 || response.sentRepairCount > 0) {
          ApplicationUtil.printInfoLog(methodName: "Calling ${response.apiUrl} api", statusCode: response.responseCode, contentLength: response.responseLength, conversationId: response.conversationId);
          Logger.i('Service Request Send Count:' + response.sentServiceRequestCount.toString());
          Logger.i('Repair  Send Count:' + response.sentRepairCount.toString());
          if (response.token != null) {
            getIt<SharedPreferences>().setString('token', 'Bearer ' + response.token!);
          }
          yield SentPendingServiceRequestToServer(count: response.sentServiceRequestCount, requestType: response.requestType);
        } else if (response.hasError) {
          Logger.e('${ApplicationUtil.getFormattedCurrentDateAndTime()} ERROR IN SERVICE REQUEST: ' + response.error);
          yield NetworkErrorInPendingServiceRequest(
            errorMessage: response.error,
            pendingRequestCount: response.pendingServiceRequest + response.pendingRepairCount,
          );
        } else {
          yield NoPendingServiceRequestInDatabase(error: 'NO_DATA');
        }
      } catch (e) {
        Logger.e(e.toString());
        Logger.e('Network error');
        yield NetworkErrorInPendingServiceRequest(errorMessage: "Unknown");
      }
    }
    if (event is UpdateBarcodeDataInAudit) {
      yield BarcodeDataUpdating();
      try {
        dynamic response =
            await auditStatusDao.updateAuditData(event.auditId, event.barcodeNumber, event.isActiveEquipment, event.oldLocationId, event.newLocationId, event.auditEquipmentList);
        if (response is bool || response is int) {
          yield BarcodeDataUpdated();
        } else {
          Logger.e(('error occur'));
          yield BarcodeDataError(error: "Some error occur while updating");
        }
      } catch (e) {
        Logger.e(('error occur'));
        Logger.e((e.toString()));
        yield BarcodeDataError(error: "error occur while updating");
      }
    }
    /*if (event is UpdateEquipmentList) {
      try {
        dynamic response = await auditStatusDao.updateFullEquipmentList(event.auditId, event.auditList);
        print(response);
      } catch (e) {
        print((e.toString()));
      }
    }*/
    if (event is GetEquipmentDataFromAuditStatus) {
      yield FetchingEquipmentFromAuditTable();
      try {
        dynamic response = await auditStatusDao.getEquipmentDataFromAudit(event.auditId);
        if (response is AuditStatusData) {
          List<AuditEquipment> equipmentList = response.equipmentData!.map((e) => AuditEquipment.fromJson(e)).toList();
          yield FetchedEquipmentFromAuditTable(auditId: response.auditId, equipmentList: equipmentList);
        } else {
          yield EquipmentErrorFromAuditTable(error: "Some error occur while updating");
        }
      } catch (e) {
        Logger.e(e.toString());
        yield EquipmentErrorFromAuditTable(error: "error occur while updating");
      }
    }
    if (event is UpdateEquipmentDataFromAuditStatus) {
      yield UpdatingEquipmentFromAuditTable();
      try {
        dynamic response = await auditStatusDao.updateEquipment(event.auditId, event.barcode, event.isEquipmentFromConfirmAuditPage);
        if (response is bool) {
          yield UpdatedEquipmentFromAuditTable();
        } else {
          yield UpdatedErrorFromAuditTable(error: "Some error occur while updating");
        }
      } catch (_) {
        yield UpdatedErrorFromAuditTable(error: "error occur while updating");
      }
    }
    if (event is UpdateEquipmentDataFromAuditStatusByName) {
      yield UpdatingEquipmentFromAuditTableByName();
      try {
        dynamic response = await auditStatusDao.updateEquipmentByName(
            event.auditId, event.equipmentName, event.auditEquipment, event.auditEquipmentList, event.isActiveEquipment, event.oldLocationId);
        if (response is bool || (response is int && response != -1)) {
          yield UpdatedEquipmentFromAuditTableByName();
        } else {
          yield UpdatedErrorFromAuditTableByName(error: "Some error occur while updating");
        }
      } catch (_) {
        yield UpdatedErrorFromAuditTableByName(error: "error occur while updating");
      }
    }
    if (event is DeleteAuditFromTable) {
      yield DeletingAuditFromTable();
      try {
        dynamic response = await auditStatusDao.deleteAuditEquipmentByAuditId(event.auditId);
        if (response is int) {
          yield DeletedAuditFromTable();
        } else {
          yield DeleteErrorAuditFromTable(error: "Some error occur while updating");
        }
      } catch (_) {
        yield UpdatedErrorFromAuditTable(error: "error occur while updating");
      }
    }
    if (event is SaveEquipmentDataFromAuditStatus) {
      yield SavingEquipmentData();
      try {
        dynamic response = await auditStatusDao.saveEquipmentData(event.auditId, event.auditEquipment);
        if (response is bool || response is int) {
          yield SavedEquipmentData();
        } else {
          Logger.e(('error occur'));
          yield SaveEquipmentError(error: "Some error occur while updating");
        }
      } catch (e) {
        Logger.e(('error occur'));
        Logger.e((e.toString()));
        yield SaveEquipmentError(error: "error occur while updating");
      }
    }
    if (event is ResetServiceRequestBloc) {
      yield ServiceRequestInitial();
    }
  }
}
