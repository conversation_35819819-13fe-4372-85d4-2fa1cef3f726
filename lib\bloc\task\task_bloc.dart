
import 'package:alink/data/dao/tasks_dao.dart';
import 'package:alink/data/model/task.dart';
import 'package:alink/isolate/http_isolate_task.dart';
import 'package:flutter/cupertino.dart';

import '../../data/model/barcode_response.dart';

import 'package:flutter_bloc/flutter_bloc.dart';

import '../../data/repository/api_repository.dart';
import '../../database/database.dart';
import '../../util/moor_web_exception.dart';

part 'task_event.dart';
part 'task_state.dart';

class TaskBloc extends Bloc<TaskEvent, TaskState> {
  final ApiRepository apiRepository;
  final Database database;
  final TaskDao taskDao ;
  TaskBloc({required this.apiRepository,required this.database,required this.taskDao}) : super(BarCodeApiInitialForTask());
  int offset = 0;
  static int? serviceCount;
  static int? serviceCompletedCount;
  List<Equipment> validatedEquipmentList = [];



  @override
  Stream<TaskState> mapEventToState(
      TaskEvent event,
      ) async* {
    if (event is CallBarCodeApiForTask) {
      yield BarcodeApiCallingForTask();
      try {
        var response = await apiRepository.fetchDatafromBarcodeNumber(event.barcodeNumber,true);
        if (response is BarcodeResponse) {
          yield BarcodeApiCalledForTask(response);
        } else {
          yield BarcodeApiErrorForTask(errorMessage: response, barcodeNumber: event.barcodeNumber);
        }
      } catch (_) {}
    }
    if (event is ResetBarCodeApiForTask) {
      yield BarCodeApiInitialForTask();
    }
    if(event is SaveTaskinDataBase){
      yield SavingTaskInDatabase();
      try{
        await taskDao.insertTask(event.task);

          yield SavedTaskInDatabase();

      }on SqliteException catch (error) {
        //Logger.e(error.message);
        yield SaveTaskInDatabaseError(errorMessage: error.message);
      }


    }
    if (event is ResetSaveTask) {
      yield BarCodeApiInitialForTask();
    }



  }
}