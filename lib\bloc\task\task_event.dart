part of 'task_bloc.dart';

@immutable
abstract class TaskEvent {}
class CallBar<PERSON>ode<PERSON>piForTask extends TaskEvent {
  final String barcodeNumber;
  final bool? isTimedService;
  CallBarCodeApiForTask({required this.barcodeNumber,required this.isTimedService});
}
class ResetBarCodeApiForTask extends TaskEvent {}

class SaveTaskinDataBase extends TaskEvent {
  final Task task;
  SaveTaskinDataBase({required this.task});

}

class ResetSaveTask extends TaskEvent{}
