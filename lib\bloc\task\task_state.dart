part of 'task_bloc.dart';


@immutable
abstract class TaskState {}
class BarcodeApiCallingForTask extends TaskState {}

class BarCodeApiInitialForTask extends TaskState {}

class BarcodeApiCalledForTask extends TaskState {
  final BarcodeResponse barcodeResponse;
  BarcodeApiCalledForTask(this.barcodeResponse);
}

class BarcodeApiErrorForTask extends TaskState {
  final String errorMessage;
  final String barcodeNumber;
  BarcodeApiErrorForTask({
    required this.errorMessage,
    required this.barcodeNumber,
  });
}

class SaveTaskInitial extends TaskState {}
class SavingTaskInDatabase extends TaskState {}
class SavedTaskInDatabase extends TaskState{

}
class SaveTaskInDatabaseError extends TaskState{
  final String errorMessage;
  SaveTaskInDatabaseError({required this.errorMessage,});
}



