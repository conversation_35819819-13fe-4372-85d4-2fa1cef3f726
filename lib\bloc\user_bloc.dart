import 'dart:async';

import 'package:alink/data/model/customer.dart';
import 'package:alink/data/repository/user_repository.dart';
import 'package:alink/database/database.dart';
import 'package:alink/util/error.dart';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';

part 'user_event.dart';
part 'user_state.dart';

class UserBloc extends Bloc<UserEvent, UserState> {
  final UserRepository repository;
  UserBloc({required this.repository}) : super(UserInitial());

  UserState get initialState => UserInitial();

  @override
  Stream<UserState> mapEventToState(
    UserEvent event,
  ) async* {
    if (event is FetchUser) {
      yield UserFetching();
      try {
        if (event.password.isEmpty || event.email.isEmpty) {
          yield UserError(error: 'Email/Password is empty');
        } else if (!isValidEmail(event.email.trim())) {
          yield UserError(error: 'Email Id is not valid,Please check');
        } else {
          final dynamic response = await repository.loginAndFetchUser(
              email: event.email.trim(),
              password: event.password,
              customerId: event.customerId);
          print(response);
          if (response is UserData) yield UserFetched(userData: response);
          if (response is List<Customer>) yield UserFetched(userData: response);
          if (response is HttpError) yield UserError(error: response.errorMessage!);
        }
      } catch (_) {
        yield UserError(error: "error occur while fetching");
      }
    }

    if (event is CheckUserLoggedIn) {
      yield UserFetching();
      try {
        final dynamic response = await repository.isUserLoggedIn();
        if (response is UserData) yield UserLoggedIn(userData: response);
        if (response == null) yield UserNotLogIn();
        if (response is HttpError) yield UserError(error: response.errorMessage!);
      } catch (_) {
        yield UserError(error: "error occur while fetching");
      }
    }
    if (event is GetLoggedInUser) {
      yield UserFetching();
      try {
        final dynamic response = await repository.getLoggedInUser();
        if (response is UserData) yield LoginUserFetched(userData: response);
        if (response == null) yield UserNotLogIn();
        if (response is HttpError) yield UserError(error: response.errorMessage!);
      } catch (_) {
        yield UserError(error: "error occur while fetching");
      }
    }
    if (event is LogoutUser) {
      yield UserLoggingOut();
      try {
        final dynamic response = await repository.logoutUser();
        if (response is int) yield UserLoggedOut();
        if (response is HttpError) yield UserError(error: response.errorMessage!);
      } catch (_) {
        yield UserError(error: "error occur while fetching");
      }
    }
  }

  bool isValidEmail(String input) {
    final RegExp regex = RegExp(
        r"^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,253}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,253}[a-zA-Z0-9])?)*$");
    return regex.hasMatch(input);
  }
}
