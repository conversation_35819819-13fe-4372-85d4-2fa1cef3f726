import 'package:alink/data/model/filter_data.dart';
import 'package:alink/data/model/location_detail.dart';
import 'package:alink/data/repository/api_repository.dart';
import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';

part 'filter_state.dart';

class FilterCubit extends Cubit<FilterState> {
  final ApiRepository apiRepository;
  FilterCubit({required this.apiRepository}) : super(FilterInitial());

  getFilterData() async {
    emit(FetchingFilterData());
    try {
      var response = await apiRepository.getFilterData();
      if (response is FilterData) {
        emit(FetchedFilterData(response));
      } else {
        emit(FetchFilterDataError(errorMessage: response));
      }
    } catch (error) {
      emit(FetchFilterDataError(errorMessage: error.toString()));
    }
  }
}
