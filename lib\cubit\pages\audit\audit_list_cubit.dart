import 'package:alink/data/model/audit_response.dart';
import 'package:alink/data/repository/api_repository.dart';
import 'package:bloc/bloc.dart';
import 'package:location_platform_interface/location_platform_interface.dart';
import 'package:meta/meta.dart';

part 'audit_list_state.dart';

class AuditListCubit extends Cubit<AuditListState> {
  final ApiRepository apiRepository;
  static int equipmentTotalCount = 0;
  static int equipmentCount = 0;
  AuditListCubit({required this.apiRepository}) : super(AuditListInitial());

  getAuditListData(int offset, String locationId, int? refAuditId,String serviceType) async {
    emit(FetchingAuditData());
    try {
      var response = await apiRepository.getAuditListByLocationId(
          offset, locationId, refAuditId,serviceType,"");
      if (response is AuditResponse) {
        emit(FetchedAuditData(response));
      } else {
        emit(FetchAuditDataError(errorMessage: response));
      }
    } catch (error) {
      emit(FetchAuditDataError(errorMessage: error.toString()));
    }
  }

  void resetAuditList() {
    emit(AuditListInitial());
  }

  submitAudit(List<AuditEquipment>? equipmentList, int auditId,
      LocationData? locationData, bool isConditionAudit,bool? isSaveAudit) async {
    emit(SubmittingAuditData());
    try {
      var response = await apiRepository.submitAudit(
          equipmentList, auditId, locationData, isConditionAudit,isSaveAudit);
      if (response is String) {
        if (response == "SAVED") {
          emit(SubmittedAuditData(auditId: auditId));
        } else {
          emit(SubmitAuditDataError(errorMessage: response));
        }
      } else {
        emit(SubmitAuditDataError(errorMessage: response));
      }
    } catch (error) {
      emit(FetchAuditDataError(errorMessage: error.toString()));
    }
  }

  submitAirLineAudit(
      Map<String, List<Map<String, dynamic>>> mapData,
      Map<String, List<Map<String, dynamic>>> imageData,
      int auditId,
      LocationData? locationData) async {
    emit(SubmittingAirlineAuditData());
    try {
      var response = await apiRepository.submitAirlineAudit(
          mapData, imageData, auditId, locationData);
      if (response is String) {
        if (response == "SAVED") {
          emit(SubmittedAirlineAuditData(auditId: auditId));
        } else {
          emit(SubmitAirlineAuditDataError(errorMessage: response));
        }
      } else {
        emit(SubmitAuditDataError(errorMessage: response));
      }
    } catch (error) {
      emit(FetchAuditDataError(errorMessage: error.toString()));
    }
  }
}
