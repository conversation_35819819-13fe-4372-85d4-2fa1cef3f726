part of 'repair_detail_api_cubit.dart';

@immutable
abstract class RepairDetailState {}

class RepairDetailInitial extends RepairDetailState {}

class ResetSingleRepairServiceDetail extends RepairDetailState {
  List<ServiceRequestDetail> serviceRequestDetailList = [];
  ResetSingleRepairServiceDetail(this.serviceRequestDetailList);
}

class FetchingSingleRepairServiceDetail extends RepairDetailState {}

class FetchedSingleRepairServiceDetail extends RepairDetailState {
  final List<ServiceRequestDetail> serviceRequestDetail;
  FetchedSingleRepairServiceDetail(this.serviceRequestDetail);
}

class SingleRepairServiceDetailError extends RepairDetailState {
  final String? errorMessage;
  SingleRepairServiceDetailError({this.errorMessage});
}

class FetchingRepairsListForEquipment extends RepairDetailState {}

class FetchedRepairsListForEquipment extends RepairDetailState {
  final List<ServiceRequestDetail> serviceRequestDetailList;
  FetchedRepairsListForEquipment(this.serviceRequestDetailList);
}

class FetchRepairsListError extends RepairDetailState {
  final String? errorMessage;
  FetchRepairsListError({this.errorMessage});
}

class FetchingSingleRepairsDetailByEquipmentId extends RepairDetailState {}

class FetchedSingleRepairsDetailByEquipmentId extends RepairDetailState {
  final List<ServiceRequestDetail> serviceRequestDetailList;
  FetchedSingleRepairsDetailByEquipmentId(this.serviceRequestDetailList);
}

class FetchSingleRepairsDetailEquipmentIdError extends RepairDetailState {
  final String? errorMessage;
  FetchSingleRepairsDetailEquipmentIdError({this.errorMessage});
}
