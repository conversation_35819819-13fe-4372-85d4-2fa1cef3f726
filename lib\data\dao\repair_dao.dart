import 'package:alink/data/model/repair.dart';
import 'package:alink/database/database.dart';
import 'package:alink/service_locator.dart';

import 'package:drift/drift.dart';
import 'package:drift/native.dart'
    if (dart.library.html) 'package:alink/util/moor_web_exception.dart';

part 'repair_dao.g.dart';

@DriftAccessor(
  tables: [Repair],
)
class RepairDao extends DatabaseAccessor<Database> with _$RepairDaoMixin {
  final Database db;
  //Logger logger = Logger();

  RepairDao(this.db) : super(db);

  Future<dynamic> insertRepair(Insertable<RepairData> repairData) async {
    try {
      int id = await into(repair).insert(repairData);

      var insertedData =
          (select(repair)..where((t) => t.repairId.equals(id))).getSingle();
      return insertedData;
    } on SqliteException catch (error) {
      //Logger.e(error.message);
      return error.message;
    }
  }

  getPendingRepairFromDb() {
    return select(repair).get();
  }

  Stream<List<RepairData>> getPendingRepairFromDbAsStream() {
    return select(repair).get().asStream();
  }

  Future deleteServiceRequest(Insertable<RepairData> repairData) =>
      delete(repair).delete(repairData);
}
