import 'dart:convert';

import 'package:alink/data/model/user.dart';
import 'package:drift/drift.dart';

@DataClassName('AuditStatusData')
class AuditStatus extends Table {
  @override
  String get tableName => 'AUDIT_STATUS';
  IntColumn get auditId => integer().named('AUDIT_ID')();
  TextColumn get equipmentData => text()
      .map(const AuditJsonConverter())
      .nullable()
      .named('EQUIPMENT_DATA')();
/*  DateTimeColumn get scannedDateTime => dateTime()
      .nullable()
      .named('SCANNED_DATETIME')();*/
  @override
  Set<Column> get primaryKey => {auditId};
}

class AuditJsonConverter
    extends TypeConverter<List<Map<String, dynamic>>, String> {
  const AuditJsonConverter();

  @override
  List<Map<String, dynamic>> fromSql(String? fromDb) {
    return (json.decode(fromDb!) as List).cast<Map<String, dynamic>>();
  }

  @override
  String toSql(List<Map<String, dynamic>>? value) {
    return json.encode(value);
  }
}
