import 'package:alink/data/model/customization.dart';
import 'package:alink/service_locator.dart';

class AvailablePart {
  final String partId;
  final String partName;
  final bool completed;
  final int quantity;
  final String partLabel;

  const AvailablePart({required this.partId, required this.partName, required this.completed, required this.quantity, required this.partLabel});

  Map<String, dynamic> toMap() {
    return {'PartId': partId, 'Name': partName, 'Completed': completed, 'Qty': quantity, 'PartLabel': partLabel};
  }

  factory AvailablePart.fromMap(Map<String, dynamic> partMap) {
    String label = '';
    String partId = '';
    Map<String, dynamic>? data = getIt<Customization>().customizationData;
    if (data != null) {
      Map<String, dynamic> customizationPartLabelMap = data['PART_LABELS'];
      partMap.forEach((partKey, partValue) {
        for (String key in customizationPartLabelMap.keys) {
          if (key.toLowerCase() == partKey.toLowerCase()) {
            label = customizationPartLabelMap[key];
            partId = partValue;
            break;
          }
        }
      });
    }
    return AvailablePart(partId: partId, partName: partMap['Name'] as String, completed: partMap['Completed'] as bool, quantity: partMap['Qty'] as int, partLabel: label);
  }
}
