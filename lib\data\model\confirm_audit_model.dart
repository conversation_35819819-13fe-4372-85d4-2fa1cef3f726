class ConfirmAuditModel {
  final bool? _isEquipmentsValidated;
  final int? _refAuditId;
  final String? _auditName;
  final bool? _isConditionAudit;
  final int? _scannedUnscannedCount;
  final String? _serviceType;

  ConfirmAuditModel(
      {required bool? isEquipmentsValidated,
      required int? refAuditId,
      required String? auditName,
      required bool? isConditionAudit,
      required int? scannedUnscannedCount,
      required String? serviceType})
      : _isEquipmentsValidated = isEquipmentsValidated,
        _refAuditId = refAuditId,
        _auditName = auditName,
        _isConditionAudit = isConditionAudit,
        _scannedUnscannedCount = scannedUnscannedCount,
        _serviceType = serviceType;

  int? get refAuditId => _refAuditId;

  bool? get isEquipmentsValidated => _isEquipmentsValidated;

  String? get auditName => _auditName;

  bool? get isConditionAudit => _isConditionAudit;

  int? get scannedUnscannedCount => _scannedUnscannedCount;

  String? get serviceType => _serviceType;
}
