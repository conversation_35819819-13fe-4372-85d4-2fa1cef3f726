import 'package:equatable/equatable.dart';

class RepairServiceRequestResponse {
  int serviceRequestCount = 0;
  List<RepairServiceRequest>? repairServiceRequestList = [];

  RepairServiceRequestResponse({required this.serviceRequestCount, this.repairServiceRequestList});

  RepairServiceRequestResponse.fromJson(Map<String, dynamic> json) {
    serviceRequestCount = json['SERVICE_REQUESTS_COUNT'];
    if (json['SERVICE_REQUESTS'] != null) {
      repairServiceRequestList = [];
      json['SERVICE_REQUESTS'].forEach((v) {
        repairServiceRequestList!.add(RepairServiceRequest.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['SERVICE_REQUESTS_COUNT'] = serviceRequestCount;
    if (repairServiceRequestList!.isNotEmpty) {
      data['SERVICE_REQUESTS'] = repairServiceRequestList!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class RepairServiceRequest extends Equatable {
  int requestId;
  int? customerId;
  int? equipmentId;
  String? requestType;
  String? currentLocationId;
  String? newLocationId;
  String? taskType;
  String? taskTypeDesc;
  String? description;
  int? createdBy;
  String? createdAt;
  String? status;
  int? remainingRequestTime;
  String? equipmentName;
  List<Map<String, dynamic>>? location;
  List<Map<String, dynamic>>? newLocation;
  String? equipmentCategoryName;
  String? tag;
  Map<String, dynamic>? partsMap;
  Map<String, dynamic>? imageListMap;
  List<Map<String, dynamic>>? repairImageList;
  bool isPresentInDb;
  String? equipmentLocationId;
  String? bomId;
  int? documentCount;
  String? safetyIssue;

  RepairServiceRequest(
      {required this.requestId,
      this.customerId,
      this.equipmentId,
      this.requestType,
      this.currentLocationId,
      this.newLocationId,
      this.taskType,
      this.taskTypeDesc,
      this.description,
      this.createdBy,
      this.createdAt,
      this.status,
      this.remainingRequestTime,
      this.equipmentName,
      this.equipmentCategoryName,
      this.location,
      this.tag,
      this.partsMap,
      this.imageListMap,
      this.newLocation,
      this.isPresentInDb = false,
      this.equipmentLocationId,
      this.bomId,
      this.repairImageList,
      this.documentCount,
        this.safetyIssue,
      });

  factory RepairServiceRequest.fromJson(Map<String, dynamic> json) {
    return RepairServiceRequest(
      requestId: json['REQUEST_ID'],
      customerId: json['CUSTOMER_ID'],
      equipmentId: json['EQUIPMENT_ID'],
      requestType: json['REQUEST_TYPE'],
      currentLocationId: json['CURRENT_LOCATION_ID'],
      newLocationId: json['NEW_LOCATION_ID'],
      taskType: json['TASK_TYPE'],
      taskTypeDesc: json['TASK_TYPE_DESC'],
      description: json['DESCRIPTION'],
      createdBy: json['CREATED_BY'],
      createdAt: json['CREATED_AT'],
      status: json['STATUS'],
      remainingRequestTime: json['REMAINING_REQUEST_TIME'],
      equipmentName: json['EQUIPMENT_NAME'],
      equipmentCategoryName: json['EQUIPMENT_CATEGORY_NAME'],
      location: json['LOCATION'] is List ? List<Map<String, dynamic>>.from(json['LOCATION']) : null,
      newLocation: json['NEW_LOCATION'] is List ? List<Map<String, dynamic>>.from(json['NEW_LOCATION']) : null,
      tag: json['TAG'],
      partsMap: json['PARTS'] is Map ? json['PARTS'] : null,
      documentCount: json['DOCUMENT_COUNT'],
      imageListMap: json['DOCUMENT'] is Map ? json['DOCUMENT'] : null,
      repairImageList: json['DOCUMENT'] is List ? List<Map<String, dynamic>>.from(json['DOCUMENT']) : null,
      equipmentLocationId: json['EQUIPMENT_LOCATION_ID'],
      bomId: json['EQUIPMENT_BOM_ID'],
      safetyIssue: json['SAFETY'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['REQUEST_ID'] = requestId;
    data['CUSTOMER_ID'] = customerId;
    data['EQUIPMENT_ID'] = equipmentId;
    data['REQUEST_TYPE'] = requestType;
    data['CURRENT_LOCATION_ID'] = currentLocationId;
    data['NEW_LOCATION_ID'] = newLocationId;
    data['TASK_TYPE'] = taskType;
    data['TASK_TYPE_DESC'] = taskTypeDesc;
    data['DESCRIPTION'] = description;
    data['CREATED_BY'] = createdBy;
    data['CREATED_AT'] = createdAt;
    data['STATUS'] = status;
    data['EQUIPMENT_CATEGORY_NAME'] = equipmentCategoryName;
    data['REMAINING_REQUEST_TIME'] = remainingRequestTime;
    if (location != null) {
      data['LOCATION'] = location;
    }
    if (location != null) {
      data['NEW_LOCATION'] = newLocation;
    }
    data['TAG'] = tag;
    if (partsMap != null) {
      data['PARTS'] = partsMap;
    }
    if (imageListMap != null) {
      data['DOCUMENT'] = imageListMap;
    }
    if (equipmentLocationId != null) {
      data['EQUIPMENT_LOCATION_ID'] = equipmentLocationId;
    }
    if (bomId != null) {
      data['EQUIPMENT_BOM_ID'] = bomId;
    }
    if (repairImageList != null) {
      data['DOCUMENT'] = repairImageList;
    }
    if (safetyIssue != null) {
      data['SAFETY'] = safetyIssue;
    }
    return data;
  }

  @override
  List<Object?> get props => [requestId];
}

RepairServiceRequest defaultRepairServiceRequest() {
  return RepairServiceRequest(requestId: -1, description: 'All Service requests',requestType: "All");
}

class RepairServiceRequestCount {
  int repairListCount;

  RepairServiceRequestCount(this.repairListCount);

  getCount() {
    return repairListCount;
  }
}
