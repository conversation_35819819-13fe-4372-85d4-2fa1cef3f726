import 'package:drift/drift.dart';

class ServiceRequest extends Table {
  @override
  String get tableName => 'SERVICE_REQUEST';
  IntColumn get requestId => integer().autoIncrement().named('REQUEST_ID')();
  IntColumn get customerId => integer().nullable().named('CUSTOMER_ID')();
  TextColumn get equipmentId => text().nullable().named('EQUIPMENT_ID')();
  TextColumn get requestType => text().nullable().named('REQUEST_TYPE')();
  TextColumn get locationId => text().nullable().named('CURRENT_LOCATION_ID')();
  TextColumn get newLocationId => text().nullable().named('NEW_LOCATION_ID')();
  TextColumn get requestChoiceType =>
      text().nullable().named('REQUEST_CHOICE_TYPE')();
  TextColumn get description => text().nullable().named('DESCRIPTION')();
  IntColumn get createdBy => integer().nullable().named('CREATED_BY')();
  DateTimeColumn get createdAt => dateTime().nullable().named('CREATED_AT')();
  IntColumn get status => integer().nullable().named('STATUS')();
  TextColumn get document => text().nullable().named('DOCUMENT')();
  TextColumn get extn => text().nullable().named('EXTN')();
  TextColumn get parts => text().nullable().named('PARTS')();
  TextColumn get newBarcode => text().nullable().named('NEW_BARCODE')();
  TextColumn get equipmentName => text().nullable().named('EQUIPMENT_NAME')();
  TextColumn get equipmentCategory =>
      text().nullable().named('EQUIPMENT_CATEGORY')();

  TextColumn get equipmentBarcodeNumber =>
      text().nullable().named('EQUIPMENT_BARCODE_NUMBER')();
  IntColumn get clusterId => integer().nullable().named('CLUSTER_ID')();
  IntColumn get choiceId => integer().nullable().named('CHOICE_ID')();
  TextColumn get repairDocument => text().nullable().named('REPAIR_DOCUMENT')();
  IntColumn get auditId => integer().nullable().named('AUDIT_ID')();
  RealColumn get latitude => real().nullable().named('LATITUDE')();
  RealColumn get longitude => real().nullable().named('LONGITUDE')();
  TextColumn get refId => text().nullable().named('REF_ID')();
  BoolColumn get safety => boolean().nullable().named('SAFETY')();
  BoolColumn get convertToSR => boolean().nullable().named('CONVERT_TO_SR')();
}
