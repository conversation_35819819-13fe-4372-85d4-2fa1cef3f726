import 'package:alink/data/model/part_item_response.dart';
import 'package:alink/data/model/repair_service_request.dart';

import 'audit_id_model.dart';

class SearchItemParam {
  final bool _isSingleSelection;
  final String? _bomId;
  final List<PartItemResponse?>? _partsUsedList;

  SearchItemParam(
      {bool isSingleSelection = false,
      String? bomId,
      List<PartItemResponse?>? partsUsedList})
      : _isSingleSelection = isSingleSelection,
        _bomId = bomId,
        _partsUsedList = partsUsedList;

  String? get bomId => _bomId;

  bool get isSingleSelection => _isSingleSelection;

  List<PartItemResponse?>? get partsUsedList => _partsUsedList;
}

class FetchSingleServiceRequestInAudit {
  final bool? _isCalledFromAudit;
  final int _id;
  final bool? _fromIdentifyConcern;
  final bool? _isTimedService;

  FetchSingleServiceRequestInAudit({bool isCalledFromAudit = false, required int id,required bool isTimedService,bool fromIdentifyConcern=false})
      : _isCalledFromAudit = isCalledFromAudit,
        _id = id,
        _fromIdentifyConcern=fromIdentifyConcern,
  _isTimedService=isTimedService
  ;

  int get id => _id;

  bool? get isCalledFromAudit => _isCalledFromAudit;

  bool? get fromIdentifyConcern => _fromIdentifyConcern;

  bool? get isTimedService => _isTimedService;
}
class RepairDetailPageParams{
  final RepairServiceRequest? _repairServiceRequest;
  final bool? _isTimedService;

  RepairDetailPageParams({
    required RepairServiceRequest? repairServiceRequest,
    required bool? isTimedService,
  })  : _repairServiceRequest = repairServiceRequest,
        _isTimedService = isTimedService;

  bool? get isTimedService => _isTimedService;

  RepairServiceRequest? get repairServiceRequest => _repairServiceRequest;
}

class LoactionHeirarchy{
  final String? _fleet;
  final String? _subFleet;
  final String? _tail;

  LoactionHeirarchy(
      {required String? fleet,
        required String? subFleet,
        required String? tail})
      : _fleet = fleet,
        _subFleet = subFleet,
        _tail = tail;

  String? get tail => _tail;

  String? get subFleet => _subFleet;

  String? get fleet => _fleet;
}