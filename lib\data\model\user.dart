import 'dart:convert';

import 'package:drift/drift.dart';

class User extends Table {
  @override
  String get tableName => 'USER';
  IntColumn get USER_ID => integer().autoIncrement().named('USER_ID')();
  TextColumn get FIRST_NAME => text().nullable().named('FIRST_NAME')();
  TextColumn get LAST_NAME => text().nullable().named('LAST_NAME')();
  TextColumn get EMAIL => text().nullable().named('EMAIL')();
  TextColumn get PHONE => text().nullable().named('PHONE')();
  TextColumn get MODULE =>
      text().map(const JsonConverter()).nullable().named('MODULE')();
  BoolColumn get AFS_USER => boolean().nullable().named('AFS_USER')();
  TextColumn get CUSTOMER_TYPE => text().nullable().named('CUSTOMER_TYPE')();
}

class JsonConverter extends TypeConverter<List<dynamic>, String> {
  const JsonConverter();

  @override
  List<String> fromSql(String? fromDb) {
    return (json.decode(fromDb!) as List).cast<String>();
  }

  @override
  String toSql(List<dynamic>? value) {
    return json.encode(value);
  }

}
