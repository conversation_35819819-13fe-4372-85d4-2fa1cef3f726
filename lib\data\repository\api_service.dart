import 'dart:convert';
import 'dart:developer';

import 'package:alink/bloc/api/api_bloc.dart';
import 'package:alink/data/model/audit_response.dart';
import 'package:alink/data/model/available_part.dart';
import 'package:alink/data/model/barcode_response.dart';
import 'package:alink/data/model/customization.dart';
import 'package:alink/data/model/equipment.dart' as eq;
import 'package:alink/data/model/filter_data.dart';
import 'package:alink/data/model/location_detail.dart';
import 'package:alink/data/model/message_data.dart';
import 'package:alink/data/model/part_item_response.dart';
import 'package:alink/data/model/repair_service_request.dart';
import 'package:alink/data/model/service_request_detail.dart';
import 'package:alink/data/model/terminal.dart';
import 'package:alink/service_locator.dart';
import 'package:alink/util/app_constant.dart';
import 'package:alink/util/application_util.dart';
import 'package:http/http.dart' as http;
import 'package:location_platform_interface/location_platform_interface.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../logger/logger.dart';
import '../../util/enums/app_enum.dart';
import '../model/audit_schedule.dart';
import '../model/singleton_model.dart';

class ApiService {
  var metadata;

  dynamic getEquipmentDetailByBarcodeNumber(barcodeNumber, bool? status) async {
    var statusParam = "";
    if (status!) {
      statusParam = "&status=OPEN,PAUSE";
    }
    try {
      Logger.i('${ApplicationUtil.getFormattedCurrentDateAndTime()} getEquipmentDetailByBarcodeNumber API = > '
          "${AppConstant.BASE_URL}/equipment?TAG=$barcodeNumber$statusParam&isRestricted=true");
      var basicAuth = await _getBasicAuth();

      final response = await http.get(Uri.parse("${AppConstant.BASE_URL}/equipment?TAG=$barcodeNumber$statusParam&isRestricted=true"),
          headers: <String, String>{'Authorization': basicAuth});

      printInfoLog(
          methodName: 'getEquipmentDetailByBarcodeNumber',
          statusCode: response.statusCode,
          contentLength: response.contentLength ?? 0,
          conversationId: response.headers['conversation-id'].toString());

      if (response.statusCode == 200) {
        updateToken(response.headers);
        BarcodeResponse barcodeResponse = BarcodeResponse.fromJson(jsonDecode(response.body));
        return barcodeResponse;
      } else if (response.statusCode == 401) {
        return ApiResponse.INVALID_AUTH;
      } else if (response.statusCode == 400) {
        return ApiResponse.BAR_CODE_NOT_FOUND;
      } else {
        return ApiResponse.BAR_CODE_NOT_FOUND;
      }
    } catch (e) {
      Logger.e('${ApplicationUtil.getFormattedCurrentDateAndTime()} $e');
      return "Network error,try again later. $e";
    }
  }

  Future<dynamic> getCustomizationData() async {
    try {
      Logger.i('${ApplicationUtil.getFormattedCurrentDateAndTime()} getCustomizationData API = > ' + "${AppConstant.BASE_URL}/customization");
      var basicAuth = await _getBasicAuth();
      final response = await http.get(Uri.parse("${AppConstant.BASE_URL}/customization"), headers: <String, String>{'Authorization': basicAuth});

      printInfoLog(
          methodName: 'getCustomizationData',
          statusCode: response.statusCode,
          contentLength: response.contentLength ?? 0,
          conversationId: response.headers['conversation-id'].toString());

      if (response.statusCode == 200) {
        updateToken(response.headers);
        getIt<Customization>().customizationData = (jsonDecode(response.body));

        String query = "locationcategory?client=app";
        Logger.i('${ApplicationUtil.getFormattedCurrentDateAndTime()} getLocationhierarchy API = > ' + "${AppConstant.BASE_URL}/$query");
        final locationResponse = await http.get(Uri.parse("${AppConstant.BASE_URL}/$query"),
            //https://jsonkeeper.com/b/14TS
            headers: <String, String>{'authorization': getIt<SharedPreferences>().getString('token')!});
        String fleet = "";
        String subFleet = "";
        String tail = "";
        print("locationhierarchy status code: ${locationResponse.statusCode}\n locationhierarchy body: ${locationResponse.body}");
        if (locationResponse.statusCode == 200) {
          List res = jsonDecode(locationResponse.body) as List<dynamic>;
          print("location hierarchy res: $res");
          res.forEach((element) {
            if (element["CATEGORY_ID"] == "F") {
              fleet = element["NAME"];
            } else if (element["CATEGORY_ID"] == "SL") {
              subFleet = element["NAME"];
            } else if (element["CATEGORY_ID"] == "TL") {
              tail = element["NAME"];
            }
          });
        }
        LoactionHeirarchy loactionHeirarchy = LoactionHeirarchy(fleet: fleet, subFleet: subFleet, tail: tail);
        if (locationResponse.statusCode == 200) {
          SharedPreferences prefs = getIt<SharedPreferences>();
          await prefs.setString('fleet', loactionHeirarchy.fleet ?? 'Fleet');
          await prefs.setString('subFleet', loactionHeirarchy.subFleet ?? 'Sub Fleet');
          await prefs.setString('tail', loactionHeirarchy.tail ?? 'Tail');
        }

        return getIt<Customization>().customizationData!.isEmpty;
      } else if (response.statusCode == 401) {
        return ApiResponse.INVALID_AUTH;
      } else if (response.statusCode == 400) {
        return response.body;
      } else {
        return ApiResponse.INTER_SERVER_ERROR;
      }
    } catch (e) {
      Logger.e('${ApplicationUtil.getFormattedCurrentDateAndTime()} $e');
      return "Network error,try again later$e";
    }
  }

  Future<dynamic> getTerminalChangeData(String locationId) async {
    try {
      Logger.i('${ApplicationUtil.getFormattedCurrentDateAndTime()} getTerminalChangeData API = > ' +
          "${AppConstant.BASE_URL}/location?LOCATION_ID=$locationId");
      var basicAuth = await _getBasicAuth();
      final response = await http
          .get(Uri.parse("${AppConstant.BASE_URL}/location?LOCATION_ID=$locationId"), headers: <String, String>{'authorization': basicAuth});

      printInfoLog(
          methodName: 'getTerminalChangeData',
          statusCode: response.statusCode,
          contentLength: response.contentLength ?? 0,
          conversationId: response.headers['conversation-id'].toString());

      if (response.statusCode == 200) {
        updateToken(response.headers);
        List<dynamic> dynamicList = jsonDecode(response.body);
        List<Terminal> terminalList = [];
        for (var element in dynamicList) {
          terminalList.add(Terminal.fromJson(element));
        }
        getIt<Terminal>().terminalLists = terminalList;
        return terminalList;
      } else if (response.statusCode == 401) {
        return ApiResponse.INVALID_AUTH;
      } else if (response.statusCode == 400) {
        return response.body;
      } else {
        return ApiResponse.INTER_SERVER_ERROR;
      }
    } catch (e) {
      Logger.e('${ApplicationUtil.getFormattedCurrentDateAndTime()} $e');
      return FetchTerminalError(
        errorMessage: "Network error,try again later$e",
      );
    }
  }

/*  saveServiceRequestToServer(ServiceRequestData serviceRequestCompanion) async {
    print('saveServiceRequestToServer');
    try {
      var basicAuth = await _getBasicAuth();
      dynamic body = {};
      body = {
        'EQUIPMENT_ID': serviceRequestCompanion.equipmentId,
        'CURRENT_LOCATION_ID': serviceRequestCompanion.locationId,
        'NEW_LOCATION_ID': serviceRequestCompanion.newLocationId,
        'TASK_TYPE': serviceRequestCompanion.requestChoiceType,
        'DESCRIPTION': serviceRequestCompanion.description,
        'EXTN': json.decode(serviceRequestCompanion.extn!),
        'DOCUMENT': json.decode(serviceRequestCompanion.document!),
      };
      Map<String, String> requestHeaders = {
        'Content-type': 'application/json',
        'Accept': 'application/json',
        'Authorization': basicAuth
      };
      final response = await http.post(
          Uri.parse("${AppConstant.BASE_URL}/servicerequest"),
          headers: requestHeaders,
          body: json.encode(body));
      if (response.statusCode == 204) {
        return "SAVED";
      } else if (response.statusCode == 401)
        return ApiResponse.INVALID_AUTH;
      else if (response.statusCode == 400)
        return response.body;
      else
        return ApiResponse.INTER_SERVER_ERROR;
    } catch (e) {
      Logger.e(e.toString());
      return (e.toString());
    }
  }*/

  Future<dynamic> getServiceRequestDetailById(int serviceRequestId, bool isTimedService, [bool? isFromAudit, bool? isRestricted]) async {
    var serviceTypeParams = "NOTIFICATION,SERVICE_REQUEST";
    if (isTimedService) {
      serviceTypeParams = "SCHEDULED";
      isRestricted = false;
    }
    try {
      if (isFromAudit == true) {
        Logger.i('${ApplicationUtil.getFormattedCurrentDateAndTime()} getServiceRequestDetailById API = > ' +
            "${AppConstant.BASE_URL}/servicerequests/$serviceRequestId");
        var basicAuth = await _getBasicAuth();
        final response = await http
            .get(Uri.parse("${AppConstant.BASE_URL}/servicerequests/$serviceRequestId"), headers: <String, String>{'authorization': basicAuth});
        printInfoLog(
            methodName: 'getServiceRequestDetailById',
            statusCode: response.statusCode,
            contentLength: response.contentLength ?? 0,
            conversationId: response.headers['conversation-id'].toString());
        if (response.statusCode == 200) {
          updateToken(response.headers);
          ServiceRequestDetail serviceRequestDetail = ServiceRequestDetail.fromJson(jsonDecode(response.body));
          List<ServiceRequestDetail> serviceRequestDetailList = [];
          serviceRequestDetailList.add(serviceRequestDetail);
          return serviceRequestDetailList;
        } else if (response.statusCode == 401) {
          return ApiResponse.INVALID_AUTH;
        } else if (response.statusCode == 400) {
          return response.body;
        } else {
          return ApiResponse.INTER_SERVER_ERROR;
        }
      } else {
        if (isRestricted == true) {
          Logger.i('${ApplicationUtil.getFormattedCurrentDateAndTime()} getServiceRequestDetailById API = > ' +
              "${AppConstant.BASE_URL}/servicerequests?equipmentId=$serviceRequestId&status=OPEN,PAUSE&requestType=$serviceTypeParams&isRestricted=$isRestricted");
          var basicAuth = await _getBasicAuth();
          final response = await http.get(
              Uri.parse(
                  "${AppConstant.BASE_URL}/servicerequests?equipmentId=$serviceRequestId&status=OPEN,PAUSE&requestType=$serviceTypeParams&isRestricted=$isRestricted"),
              headers: <String, String>{'authorization': basicAuth});
          printInfoLog(
              methodName: 'getServiceRequestDetailById',
              statusCode: response.statusCode,
              contentLength: response.contentLength ?? 0,
              conversationId: response.headers['conversation-id'].toString());

          if (response.statusCode == 200) {
            updateToken(response.headers);
            var res = jsonDecode(response.body) as List<dynamic>;
            List<ServiceRequestDetail> serviceRequestDetail = res.map((e) => ServiceRequestDetail.fromJson(e)).toList();
            return serviceRequestDetail;
          } else if (response.statusCode == 401) {
            return ApiResponse.INVALID_AUTH;
          } else if (response.statusCode == 400) {
            return response.body;
          } else {
            return ApiResponse.INTER_SERVER_ERROR;
          }
        } else {
          Logger.i('${ApplicationUtil.getFormattedCurrentDateAndTime()} getServiceRequestDetailById API = > ' +
              "${AppConstant.BASE_URL}/servicerequests?equipmentId=$serviceRequestId&status=OPEN,PAUSE&requestType=$serviceTypeParams&isRestricted=$isRestricted");
          var basicAuth = await _getBasicAuth();
          final response = await http.get(
              Uri.parse(
                  "${AppConstant.BASE_URL}/servicerequests?equipmentId=$serviceRequestId&status=OPEN,PAUSE&requestType=$serviceTypeParams&isRestricted=$isRestricted"),
              headers: <String, String>{'authorization': basicAuth});
          printInfoLog(
              methodName: 'getServiceRequestDetailById',
              statusCode: response.statusCode,
              contentLength: response.contentLength ?? 0,
              conversationId: response.headers['conversation-id'].toString());

          if (response.statusCode == 200) {
            updateToken(response.headers);
            var res = jsonDecode(response.body) as List<dynamic>;
            List<ServiceRequestDetail> serviceRequestDetail = res.map((e) => ServiceRequestDetail.fromJson(e)).toList();
            return serviceRequestDetail;
          } else if (response.statusCode == 401) {
            return ApiResponse.INVALID_AUTH;
          } else if (response.statusCode == 400) {
            return response.body;
          } else {
            return ApiResponse.INTER_SERVER_ERROR;
          }
        }
      }
    } catch (e) {
      Logger.e('${ApplicationUtil.getFormattedCurrentDateAndTime()} $e');
      return SingleServiceDetailError(
        errorMessage: "Network error,try again later$e",
      );
    }
  }

  Future<dynamic> getServiceRequestListByEquipmentId(int equipmentId, bool bothRequestType, bool isTimedService) async {
    try {
      var basicAuth = await _getBasicAuth();
      final response;
      if (bothRequestType && !isTimedService) {
        Logger.i('${ApplicationUtil.getFormattedCurrentDateAndTime()} getServiceRequestListByEquipmentId API = > ' +
            "${AppConstant.BASE_URL}/servicerequests?equipmentId=$equipmentId&status=OPEN,PAUSE&requestType=NOTIFICATION,SERVICE_REQUEST&isRestricted=true");
        response = await http.get(
            Uri.parse(
                "${AppConstant.BASE_URL}/servicerequests?equipmentId=$equipmentId&status=OPEN,PAUSE&requestType=NOTIFICATION,SERVICE_REQUEST&isRestricted=true"),
            headers: <String, String>{'authorization': basicAuth});
      } else if (isTimedService) {
        Logger.i('${ApplicationUtil.getFormattedCurrentDateAndTime()} getServiceRequestListByEquipmentId API = > ' +
            "${AppConstant.BASE_URL}/servicerequests?equipmentId=$equipmentId&requestType=SCHEDULED");
        response = await http.get(Uri.parse("${AppConstant.BASE_URL}/servicerequests?equipmentId=$equipmentId&requestType=SCHEDULED"),
            headers: <String, String>{'authorization': basicAuth});
      } else {
        Logger.i('${ApplicationUtil.getFormattedCurrentDateAndTime()} getServiceRequestListByEquipmentId API = > ' +
            "${AppConstant.BASE_URL}/servicerequests?equipmentId=$equipmentId&requestType=SERVICE_REQUEST");
        response = await http.get(Uri.parse("${AppConstant.BASE_URL}/servicerequests?equipmentId=$equipmentId&requestType=SERVICE_REQUEST"),
            headers: <String, String>{'authorization': basicAuth});
      }

      printInfoLog(
          methodName: 'getServiceRequestListByEquipmentId',
          statusCode: response.statusCode,
          contentLength: response.contentLength ?? 0,
          conversationId: response.headers['conversation-id'].toString());

      if (response.statusCode == 200) {
        updateToken(response.headers);
        var res = jsonDecode(response.body) as List<dynamic>;
        List<ServiceRequestDetail> serviceRequestDetail = res.map((e) => ServiceRequestDetail.fromJson(e)).toList();
        return serviceRequestDetail;
      } else if (response.statusCode == 401) {
        return ApiResponse.INVALID_AUTH;
      } else if (response.statusCode == 400) {
        return response.body;
      } else {
        return ApiResponse.INTER_SERVER_ERROR;
      }
    } catch (e) {
      Logger.e('${ApplicationUtil.getFormattedCurrentDateAndTime()} $e');
      return SingleServiceDetailError(
        errorMessage: "Network error,try again later$e",
      );
    }
  }

  _getBasicAuth() {
    return getIt<SharedPreferences>().getString('token');
    //return base64Encode(utf8.encode('$token'));
  }

  Future<dynamic> fetchRepairServiceRequestList(int offset, String query) async {
    try {
      Logger.i('${ApplicationUtil.getFormattedCurrentDateAndTime()} fetchRepairServiceRequestList API = > ' +
          "${AppConstant.BASE_URL}/servicerequests?limit=${AppConstant.LIMIT}&offset=$offset$query");
      print('${AppConstant.BASE_URL}/servicerequests?limit=${AppConstant.LIMIT}&offset=$offset$query');
      var basicAuth = await _getBasicAuth();
      final response = await http.get(
          Uri.parse('${AppConstant.BASE_URL}/servicerequests?limit=${AppConstant.LIMIT}&offset=$offset$query&client=admin'),
          headers: <String, String>{'authorization': basicAuth});
      printInfoLog(
          methodName: 'fetchRepairServiceRequestList',
          statusCode: response.statusCode,
          contentLength: response.contentLength ?? 0,
          conversationId: response.headers['conversation-id'].toString());
      if (response.statusCode == 200) {
        updateToken(response.headers);
        var res = jsonDecode(response.body);
        var serviceRequestList = res['SERVICE_REQUESTS'] as List<dynamic>;
        //var serviceRequestCount = res['SERVICE_REQUESTS_COUNT'] as int;
        //RepairServiceRequestCount(serviceRequestCount);

        RepairServiceRequestResponse repairServiceRequestResponse = RepairServiceRequestResponse.fromJson(jsonDecode(response.body));
        return repairServiceRequestResponse;

        return serviceRequestList.map((e) => RepairServiceRequest.fromJson(e)).toList();
      } else if (response.statusCode == 401) {
        return ApiResponse.INVALID_AUTH;
      } else if (response.statusCode == 400) {
        return response.body;
      } else {
        return ApiResponse.INTER_SERVER_ERROR;
      }
    } catch (e) {
      Logger.e('${ApplicationUtil.getFormattedCurrentDateAndTime()} $e');
      return 'Error occur while fetching service request';
    }
  }

  Future<dynamic> fetchLopaRepairServiceRequestList(String query) async {
    try {
      Logger.i('${ApplicationUtil.getFormattedCurrentDateAndTime()} fetchLopaRepairServiceRequestList API = > ' +
          "${AppConstant.BASE_URL}/servicerequests?limit=4294967296&offset=0$query");
      print('${AppConstant.BASE_URL}/servicerequests?limit=4294967296&offset=0$query');
      var basicAuth = await _getBasicAuth();
      final response = await http.get(Uri.parse('${AppConstant.BASE_URL}/servicerequests?limit=4294967296&offset=0$query'),
          headers: <String, String>{'authorization': basicAuth});
      printInfoLog(
          methodName: 'fetchLopaRepairServiceRequestList',
          statusCode: response.statusCode,
          contentLength: response.contentLength ?? 0,
          conversationId: response.headers['conversation-id'].toString());
      if (response.statusCode == 200) {
        updateToken(response.headers);
        var res = jsonDecode(response.body);
        var serviceRequestList = res['SERVICE_REQUESTS'] as List<dynamic>;
        return serviceRequestList.map((e) => RepairServiceRequest.fromJson(e)).toList();
      } else if (response.statusCode == 401) {
        return ApiResponse.INVALID_AUTH;
      } else if (response.statusCode == 400) {
        return response.body;
      } else {
        return ApiResponse.INTER_SERVER_ERROR;
      }
    } catch (e) {
      Logger.e('${ApplicationUtil.getFormattedCurrentDateAndTime()} $e');
      return 'Error occur while fetching service request';
    }
  }

  Future<dynamic> fetchPartItemList(int offset, String searchValue, String? partId) async {
    if (partId == null || partId == "") {
      try {
        Logger.i('${ApplicationUtil.getFormattedCurrentDateAndTime()} fetchPartItemList API = > ' +
            "${AppConstant.BASE_URL}/parts?app=app&type=PART&search=$searchValue&limit=${AppConstant.PART_LIST_LIMIT}&offset=$offset&status=active");
        // "${AppConstant.BASE_URL}/parts/partId");
        var basicAuth = await _getBasicAuth();
        final response = await http.get(
            Uri.parse(
                '${AppConstant.BASE_URL}/parts?&app=app&type=PART&search=$searchValue&limit=${AppConstant.PART_LIST_LIMIT}&offset=$offset&status=active'),
            headers: <String, String>{'authorization': basicAuth});
        updateToken(response.headers);
        printInfoLog(
            methodName: 'fetchPartItemList',
            statusCode: response.statusCode,
            contentLength: response.contentLength ?? 0,
            conversationId: response.headers['conversation-id'].toString());
        var body = jsonDecode(response.body);
        var resList = body['PARTS'] as List<dynamic>;
        return resList.map((e) => PartItemResponse.fromMap(e)).toList();
      } catch (e) {
        Logger.e('${ApplicationUtil.getFormattedCurrentDateAndTime()} $e');
        return 'Error occur while fetching part items';
      }
    } else {
      try {
        Logger.i('${ApplicationUtil.getFormattedCurrentDateAndTime()} fetchPartItemList API = > ' +
            "${AppConstant.BASE_URL}/parts/$partId?&type=PART&search=$searchValue&limit=${AppConstant.PART_LIST_LIMIT}&offset=$offset&status=active");
        var basicAuth = await _getBasicAuth();
        final response = await http.get(
            Uri.parse(
                "${AppConstant.BASE_URL}/parts/$partId?&type=PART&search=$searchValue&limit=${AppConstant.PART_LIST_LIMIT}&offset=$offset&status=active"),
            headers: <String, String>{'authorization': basicAuth});
        updateToken(response.headers);
        printInfoLog(
            methodName: 'fetchPartItemList',
            statusCode: response.statusCode,
            contentLength: response.contentLength ?? 0,
            conversationId: response.headers['conversation-id'].toString());
        var body = jsonDecode(response.body);
        var resList = body['PART_BOM'] as List<dynamic>;
        return resList.map((e) => PartItemResponse.fromMap(e)).toList();
      } catch (e) {
        Logger.e('${ApplicationUtil.getFormattedCurrentDateAndTime()} $e');
        return 'Error occur while fetching part items';
      }
    }
  }

  Future<dynamic> fetchBOMPartItemList() async {
    try {
      Logger.i('${ApplicationUtil.getFormattedCurrentDateAndTime()} fetchBOMPartItemList API = > ' + "${AppConstant.BASE_URL}/parts?type=BOM");
      var basicAuth = await _getBasicAuth();
      final response = await http.get(Uri.parse('${AppConstant.BASE_URL}/parts?type=BOM'), headers: <String, String>{'authorization': basicAuth});

      printInfoLog(
          methodName: 'fetchBOMPartItemList',
          statusCode: response.statusCode,
          contentLength: response.contentLength ?? 0,
          conversationId: response.headers['conversation-id'].toString());

      if (response.statusCode == 200) {
        updateToken(response.headers);
        var body = jsonDecode(response.body);
        var resList = body['PARTS'] as List<dynamic>;
        return resList.map((e) => PartItemResponse.fromMap(e)).toList();
      } else if (response.statusCode == 401) {
        return ApiResponse.INVALID_AUTH;
      } else if (response.statusCode == 400) {
        return response.body;
      } else {
        return ApiResponse.INTER_SERVER_ERROR;
      }
    } catch (e) {
      Logger.e('${ApplicationUtil.getFormattedCurrentDateAndTime()} $e');
      return 'Error occur while fetching service request';
    }
  }

  Future<dynamic> fetchAvailablePartList(String query) async {
    try {
      Logger.i('${ApplicationUtil.getFormattedCurrentDateAndTime()} fetchAvailablePartList API = > ' +
          "${AppConstant.BASE_URL}/lopapartsdetails?" +
          query);
      var basicAuth = await _getBasicAuth();
      final response =
          await http.get(Uri.parse('${AppConstant.BASE_URL}/lopapartsdetails?$query'), headers: <String, String>{'authorization': basicAuth});

      printInfoLog(
          methodName: 'fetchAvailablePartList',
          statusCode: response.statusCode,
          contentLength: response.contentLength ?? 0,
          conversationId: response.headers['conversation-id'].toString());

      if (response.statusCode == 200) {
        updateToken(response.headers);
        var respond = jsonDecode(response.body);
        return List<AvailablePart>.from(respond.map((e) => AvailablePart.fromMap(e)).toList());
      } else if (response.statusCode == 401) {
        return ApiResponse.INVALID_AUTH;
      } else if (response.statusCode == 400) {
        return response.body;
      } else {
        return ApiResponse.INTER_SERVER_ERROR;
      }
    } catch (e) {
      Logger.e('${ApplicationUtil.getFormattedCurrentDateAndTime()} $e');
      return 'Error occur while fetching part list';
    }
  }

  Future<dynamic> getFilterData() async {
    try {
      Logger.i('${ApplicationUtil.getFormattedCurrentDateAndTime()} getFilterData API = > ' + "${AppConstant.BASE_URL}/filterdata");
      var basicAuth = await _getBasicAuth();
      final filterResponse = await http.get(Uri.parse("${AppConstant.BASE_URL}/filterdata"), headers: <String, String>{'authorization': basicAuth});
      List<LocationDetail> locationList = await getLocationData() as List<LocationDetail>;
      printInfoLog(
          methodName: 'getFilterData',
          statusCode: filterResponse.statusCode,
          contentLength: filterResponse.contentLength ?? 0,
          conversationId: filterResponse.headers['conversation-id'].toString());
      if (filterResponse.statusCode == 200) {
        updateToken(filterResponse.headers);
        FilterData filterData = FilterData.fromJson(jsonDecode(filterResponse.body), locationList);
        return filterData;
      } else if (filterResponse.statusCode == 401) {
        return ApiResponse.INVALID_AUTH;
      } else if (filterResponse.statusCode == 400) {
        return filterResponse.body;
      } else {
        return ApiResponse.INTER_SERVER_ERROR;
      }
    } catch (e) {
      Logger.e('${ApplicationUtil.getFormattedCurrentDateAndTime()} $e');
      return SingleServiceDetailError(
        errorMessage: "Network error,try again later$e",
      );
    }
  }

  Future<dynamic> getLocationData() async {
    try {
      Logger.i('${ApplicationUtil.getFormattedCurrentDateAndTime()} getFilterData API = > ' + "${AppConstant.BASE_URL}/locationheirarchy");
      var basicAuth = await _getBasicAuth();
      final response = await http.get(Uri.parse("${AppConstant.BASE_URL}/locationheirarchy"), headers: <String, String>{'authorization': basicAuth});
      printInfoLog(
          methodName: 'getFilterData',
          statusCode: response.statusCode,
          contentLength: response.contentLength ?? 0,
          conversationId: response.headers['conversation-id'].toString());
      if (response.statusCode == 200) {
        updateToken(response.headers);
        List<dynamic> dataList = jsonDecode(response.body);
        List<LocationDetail> locationList = dataList.map((v) => LocationDetail.fromMap(v)).toList();
        locationList.insert(
          0,
          LocationDetail(
            name: 'Select Location',
            id: "-1",
            categoryId: "-1",
          ),
        );

        return locationList;
      } else if (response.statusCode == 401) {
        return ApiResponse.INVALID_AUTH;
      } else if (response.statusCode == 400) {
        return response.body;
      } else {
        return ApiResponse.INTER_SERVER_ERROR;
      }
    } catch (e) {
      Logger.e('${ApplicationUtil.getFormattedCurrentDateAndTime()} $e');
      return SingleServiceDetailError(
        errorMessage: "Network error,try again later$e",
      );
    }
  }

  Future<dynamic> getMessageDataByRequestId(int requestId) async {
    try {
      Logger.i('${ApplicationUtil.getFormattedCurrentDateAndTime()} getMessageDataByRequestId API = > ' +
          "${AppConstant.BASE_URL}/message?requestid=$requestId");
      var basicAuth = await _getBasicAuth();
      final response =
          await http.get(Uri.parse("${AppConstant.BASE_URL}/message?requestid=$requestId"), headers: <String, String>{'authorization': basicAuth});
      printInfoLog(
          methodName: 'getMessageDataByRequestId',
          statusCode: response.statusCode,
          contentLength: response.contentLength ?? 0,
          conversationId: response.headers['conversation-id'].toString());
      if (response.statusCode == 200) {
        updateToken(response.headers);
        var res = jsonDecode(response.body) as List<dynamic>;
        return res.map((e) => MessageData.fromMap(e)).toList();
      } else if (response.statusCode == 401) {
        return ApiResponse.INVALID_AUTH;
      } else if (response.statusCode == 400) {
        return response.body;
      } else {
        return ApiResponse.INTER_SERVER_ERROR;
      }
    } catch (e) {
      Logger.e('${ApplicationUtil.getFormattedCurrentDateAndTime()} $e');
      return SingleServiceDetailError(
        errorMessage: "Network error,try again later$e",
      );
    }
  }

  Future<dynamic> getAuditListByLocationId(int offset, String locationId, int? refAuditId, String serviceType, String description) async {
    String auditSchIdString = "";
    String auditServiceTypeString = "";
    print("value of service type: ${serviceType.length}\nvalue of description: ${description.length}");
    if (description != "" &&
        description != null &&
        (description != "All Audits and Tasks" && description != "All Audits" && description != "All Tasks")) {
      auditSchIdString = "&description=$description";
    }

    if (serviceType != "") {
      auditServiceTypeString = "&serviceType=${serviceType.toUpperCase()}";
    }

    if (refAuditId == null) {
      try {
        Logger.i('${ApplicationUtil.getFormattedCurrentDateAndTime()} getAuditListByLocationId API = > ' +
            "${AppConstant.BASE_URL}/audits?locationId=$locationId&limit=10&offset=$offset$auditServiceTypeString$auditSchIdString");
        var basicAuth = await _getBasicAuth();
        final response = await http.get(
            Uri.parse('${AppConstant.BASE_URL}/audits?locationId=$locationId&limit=10&offset=$offset$auditServiceTypeString$auditSchIdString'),
            headers: <String, String>{'authorization': basicAuth});
        printInfoLog(
            methodName: 'getAuditListByLocationId',
            statusCode: response.statusCode,
            contentLength: response.contentLength ?? 0,
            conversationId: response.headers['conversation-id'].toString());
        if (response.statusCode == 200) {
          updateToken(response.headers);
          AuditResponse auditResponse = AuditResponse.fromJson(jsonDecode(response.body));
          return auditResponse;
        } else if (response.statusCode == 401) {
          return ApiResponse.INVALID_AUTH;
        } else if (response.statusCode == 400) {
          return response.body;
        } else {
          return ApiResponse.INTER_SERVER_ERROR;
        }
      } catch (e) {
        Logger.e('${ApplicationUtil.getFormattedCurrentDateAndTime()} $e');
        return 'Error occur while fetching service request';
      }
    } else {
      try {
        Logger.i("${ApplicationUtil.getFormattedCurrentDateAndTime()} getAuditByLocationIdRefAuditId");
        Logger.i('${ApplicationUtil.getFormattedCurrentDateAndTime()} getAuditListByLocationId API = > ' +
            "${AppConstant.BASE_URL}/audits?locationId=$locationId&refAuditId=$refAuditId&limit=10&offset=$offset");
        var basicAuth = await _getBasicAuth();
        final response = await http.get(
            Uri.parse('${AppConstant.BASE_URL}/audits?locationId=$locationId&refAuditId=$refAuditId&limit=10&offset=$offset'),
            headers: <String, String>{'authorization': basicAuth});
        printInfoLog(
            methodName: 'getAuditListByLocationId',
            statusCode: response.statusCode,
            contentLength: response.contentLength ?? 0,
            conversationId: response.headers['conversation-id'].toString());
        if (response.statusCode == 200) {
          updateToken(response.headers);
          AuditResponse auditResponse = AuditResponse.fromJson(jsonDecode(response.body));
          return auditResponse;
        } else if (response.statusCode == 401) {
          return ApiResponse.INVALID_AUTH;
        } else if (response.statusCode == 400) {
          return response.body;
        } else {
          return ApiResponse.INTER_SERVER_ERROR;
        }
      } catch (e) {
        Logger.e('${ApplicationUtil.getFormattedCurrentDateAndTime()} $e');
        return 'Error occur while fetching audit';
      }
    }
  }

  Future<dynamic> fetchEquipmentListForAudit() async {
    try {
      var basicAuth = await _getBasicAuth();
      final response = await http.get(Uri.parse("https://jsonkeeper.com/b/VF8M"),
          //Uri.parse("${AppConstant.BASE_URL}/servicerequests/$equipmentId"),
          headers: <String, String>{'authorization': basicAuth});
      printInfoLog(
          methodName: 'fetchEquipmentListForAudit',
          statusCode: response.statusCode,
          contentLength: response.contentLength ?? 0,
          conversationId: response.headers['conversation-id'].toString());
      if (response.statusCode == 200) {
        updateToken(response.headers);
        var res = jsonDecode(response.body) as List<dynamic>;
        List<eq.Equipment> equipmentList = res.map((e) => eq.Equipment.fromMap(e)).toList();
        return equipmentList;
      } else if (response.statusCode == 401) {
        return ApiResponse.INVALID_AUTH;
      } else if (response.statusCode == 400) {
        return response.body;
      } else {
        return ApiResponse.INTER_SERVER_ERROR;
      }
    } catch (e) {
      Logger.e('${ApplicationUtil.getFormattedCurrentDateAndTime()} $e');
      return "Network error,try again later$e";
    }
  }

  Future<dynamic> fetchAuditScheduleDescription() async {
    try {
      Logger.i('${ApplicationUtil.getFormattedCurrentDateAndTime()} getAuditListByLocationId API = > ' + "${AppConstant.BASE_URL}/auditdescriptions");
      var basicAuth = await _getBasicAuth();
      final response = await http.get(Uri.parse('${AppConstant.BASE_URL}/auditdescriptions'), headers: <String, String>{'authorization': basicAuth});
      printInfoLog(
          methodName: 'fetchAuditScheduleDescription',
          statusCode: response.statusCode,
          contentLength: response.contentLength ?? 0,
          conversationId: response.headers['conversation-id'].toString());
      if (response.statusCode == 200) {
        updateToken(response.headers);
        var resList = jsonDecode(response.body) as List<dynamic>;

        resList = resList.map((e) => AuditSchedules.fromJson(e)).toList();
        resList.add(AuditSchedules(
          description: 'All Audits and Tasks',
          serviceType: "ALL",
        ));
        return resList;

        /*List<AuditSchedules> auditSchedulesResponseList = AuditSchedules.fromJson(jsonDecode(response.body)['AUDIT_SCHEDULES']);
        return auditSchedulesResponseList;*/
      } else if (response.statusCode == 401) {
        return ApiResponse.INVALID_AUTH;
      } else if (response.statusCode == 400) {
        return response.body;
      } else {
        return ApiResponse.INTER_SERVER_ERROR;
      }
    } catch (e) {
      Logger.e('${ApplicationUtil.getFormattedCurrentDateAndTime()} $e');
      return 'Error occur while fetching audit description';
    }
  }

  createNewAudit(String locationId, String comment, String serviceType) async {
    try {
      Logger.i('${ApplicationUtil.getFormattedCurrentDateAndTime()} createNewAudit API = > ' +
          "${AppConstant.BASE_URL}/audit \nSERVICE_TYPE===>$serviceType");
      var basicAuth = await _getBasicAuth();
      dynamic body = {};
      body = {'LOCATION_ID': locationId, 'DESCRIPTION': comment, 'SERVICE_TYPE': serviceType};
      Map<String, String> requestHeaders = {'Content-type': 'application/json', 'Accept': 'application/json', 'Authorization': basicAuth};
      final response = await http.post(Uri.parse("${AppConstant.BASE_URL}/audit"), headers: requestHeaders, body: json.encode(body));
      printInfoLog(
          methodName: 'submitValidatedAudit',
          statusCode: response.statusCode,
          contentLength: response.contentLength ?? 0,
          conversationId: response.headers['conversation-id'].toString());
      if (response.statusCode == 204) {
        updateToken(response.headers);
        return "SAVED";
      } else if (response.statusCode == 401) {
        return ApiResponse.INVALID_AUTH;
      } else if (response.statusCode == 400) {
        return response.body;
      } else {
        return ApiResponse.INTER_SERVER_ERROR;
      }
    } catch (e) {
      Logger.e('${ApplicationUtil.getFormattedCurrentDateAndTime()} $e');
      return (e.toString());
    }
  }

  submitValidatedAudit(List<AuditEquipment>? equipmentList, int auditId, LocationData? locationData, bool isConditionAudit,bool? isSaveAudit) async {
    print('submitValidatedAudit body');
    print(equipmentList.toString());
    List<Map<String, dynamic>> bodyData = [];
    if (equipmentList!.isNotEmpty) {
      equipmentList.forEach((element) {
        if (element.equipmentId != null) {
          Map<String, dynamic> equipmentMap = {};
          //equipmentMap['STATUS'] = element.isScanned ? 'Scanned' : 'Unscanned';
          equipmentMap['STATUS'] = element.status;
          equipmentMap['NAME'] = element.name;
          equipmentMap['EQUIPMENT_ID'] = element.equipmentId;
          bodyData.add(equipmentMap);
        }
      });
    }
    try {
      var basicAuth = await _getBasicAuth();
      dynamic body = {};
      if (locationData != null) {
        if (isConditionAudit) {
          body = {
            'AUDIT_ID': auditId,
            'EQUIPMENTS': bodyData,
            'LATITUDE': locationData.latitude,
            'LONGITUDE': locationData.longitude,
            'IS_CONDITION_AUDIT': true,
            'SAVE' : isSaveAudit,


          };
        } else {
          body = {
            'AUDIT_ID': auditId,
            'EQUIPMENTS': bodyData,
            'LATITUDE': locationData.latitude,
            'LONGITUDE': locationData.longitude,
            'SAVE' : isSaveAudit,

          };
        }
      } else {
        if (isConditionAudit) {
          body = {
            'AUDIT_ID': auditId,
            'EQUIPMENTS': bodyData,
            'IS_CONDITION_AUDIT': true,
            'SAVE' : isSaveAudit,

          };
        } else {
          body = {
            'AUDIT_ID': auditId,
            'EQUIPMENTS': bodyData,
            'SAVE' : isSaveAudit,

          };
        }
      }

      Map<String, String> requestHeaders = {'Content-type': 'application/json', 'Accept': 'application/json', 'Authorization': basicAuth};
      //print(json.encode(body));
      Logger.i('${ApplicationUtil.getFormattedCurrentDateAndTime()} submitValidatedAudit API = > ' + "${AppConstant.BASE_URL}/submitaudit");
      final response = await http.post(Uri.parse("${AppConstant.BASE_URL}/submitaudit"), headers: requestHeaders, body: json.encode(body));
      printInfoLog(
          methodName: 'submitValidatedAudit',
          statusCode: response.statusCode,
          contentLength: response.contentLength ?? 0,
          conversationId: response.headers['conversation-id'].toString());
      if (response.statusCode == 204) {
        updateToken(response.headers);
        return "SAVED";
      } else if (response.statusCode == 401) {
        return ApiResponse.INVALID_AUTH;
      } else if (response.statusCode == 400) {
        return response.body;
      } else {
        return ApiResponse.INTER_SERVER_ERROR;
      }
    } catch (e) {
      print(e.toString());
      Logger.e('${ApplicationUtil.getFormattedCurrentDateAndTime()} $e');
      return (e.toString());
    }
  }

  submitValidatedAirlineAudit(Map<String, List<Map<String, dynamic>>> mapData, Map<String, List<Map<String, dynamic>>> imageData, int auditId,
      LocationData? locationData) async {
    print('submitValidatedAudit body');
    imageData.removeWhere((key, value) => value.isEmpty);
    List<Map<String, dynamic>> bodyData = [];
    try {
      var basicAuth = await _getBasicAuth();
      dynamic body = {};
      body = {
        'AUDIT_ID': auditId,
        'PARTS': mapData,
      };
      if (imageData.isNotEmpty) {
        body['DOCUMENTS'] = imageData;
      }
      if (locationData != null) {
        body['LATITUDE'] = locationData.latitude;
        body['LONGITUDE'] = locationData.longitude;
      }

      Map<String, String> requestHeaders = {'Content-type': 'application/json', 'Accept': 'application/json', 'Authorization': basicAuth};
      //log(json.encode(body));
      Logger.i('${ApplicationUtil.getFormattedCurrentDateAndTime()} submitValidatedAudit API = > ' + "${AppConstant.BASE_URL}/submitaudit");
      final response = await http.post(Uri.parse("${AppConstant.BASE_URL}/submitaudit"), headers: requestHeaders, body: json.encode(body));
      printInfoLog(
          methodName: 'submitValidatedAudit',
          statusCode: response.statusCode,
          contentLength: response.contentLength ?? 0,
          conversationId: response.headers['conversation-id'].toString());
      if (response.statusCode == 204) {
        updateToken(response.headers);
        return "SAVED";
      } else if (response.statusCode == 401) {
        return ApiResponse.INVALID_AUTH;
      } else if (response.statusCode == 400) {
        return response.body;
      } else {
        return ApiResponse.INTER_SERVER_ERROR;
      }
    } catch (e) {
      print(e.toString());
      Logger.e('${ApplicationUtil.getFormattedCurrentDateAndTime()} $e');
      return (e.toString());
    }
  }

  validateBarcodes(List<AuditEquipment> equipmentList, int auditId) async {
    print('validateBarcodes');
    print(equipmentList.length);
    try {
      Logger.i('${ApplicationUtil.getFormattedCurrentDateAndTime()} validateBarcodes API = > ' + "${AppConstant.BASE_URL}/validateaudit");
      var basicAuth = await _getBasicAuth();
      dynamic body = {};
      List<String> barcodeList = [];
      equipmentList.forEach((element) {
        barcodeList.add(element.tag!);
      });
      body = {
        'AUDIT_ID': auditId,
        'BARCODES': barcodeList,
      };
      Map<String, String> requestHeaders = {'Content-type': 'application/json', 'Accept': 'application/json', 'Authorization': basicAuth};
      //print(json.encode(body));
      final response = await http.post(Uri.parse("${AppConstant.BASE_URL}/validateaudit"), headers: requestHeaders, body: json.encode(body));
      printInfoLog(
          methodName: 'validateBarcodes',
          statusCode: response.statusCode,
          contentLength: response.contentLength ?? 0,
          conversationId: response.headers['conversation-id'].toString());

      if (response.statusCode == 200) {
        updateToken(response.headers);
        var res = jsonDecode(response.body) as List<dynamic>;
        print('resres');
        print(json.encode(res));
        List<Equipment> equipmentList = res.map((e) => Equipment.fromJson(e)).toList();
        return equipmentList;
      } else if (response.statusCode == 401) {
        return ApiResponse.INVALID_AUTH;
      } else if (response.statusCode == 400) {
        return response.body;
      } else {
        return ApiResponse.INTER_SERVER_ERROR;
      }
    } catch (e) {
      print(e.toString());
      Logger.e('${ApplicationUtil.getFormattedCurrentDateAndTime()} $e');
      return (e.toString());
    }
  }

  Future<dynamic> getServiceRequestCount() async {
    try {
      Logger.i(
          '${ApplicationUtil.getFormattedCurrentDateAndTime()} getServiceRequestCount API = > ' + "${AppConstant.BASE_URL}/servicerequestscount");
      var basicAuth = await _getBasicAuth();
      final response =
          await http.get(Uri.parse("${AppConstant.BASE_URL}/servicerequestscount"), headers: <String, String>{'authorization': basicAuth});
      printInfoLog(
          methodName: 'getServiceRequestCount',
          statusCode: response.statusCode,
          contentLength: response.contentLength ?? 0,
          conversationId: response.headers['conversation-id'].toString());
      print("============================================");
      print("response.headers: ${response.headers}");
      print(json.encode(response.headers));
      print("============================================");
      if (response.statusCode == 200) {
        updateToken(response.headers);
        Map<String, dynamic> res = jsonDecode(response.body) as Map<String, dynamic>;

        return res;
      } else if (response.statusCode == 401) {
        return ApiResponse.INVALID_AUTH;
      } else if (response.statusCode == 400) {
        return response.body;
      } else {
        return ApiResponse.INTER_SERVER_ERROR;
      }
    } catch (e) {
      Logger.e('${ApplicationUtil.getFormattedCurrentDateAndTime()} $e');
      return "Network error,try again later$e";
    }
  }

  Future<dynamic> resetPassword(String email) async {
    var body = {
      'client': 'app',
      'email': email,
    };
    Map<String, String> requestHeaders = {
      'Content-type': 'application/json',
      'Accept': 'application/json',
    };
    Logger.i('${ApplicationUtil.getFormattedCurrentDateAndTime()} resetPassword API = > ' + "${AppConstant.BASE_URL}/forgotpassword");
    try {
      final response = await http.post(Uri.parse('${AppConstant.BASE_URL}/forgotpassword'), headers: requestHeaders, body: json.encode(body));
      printInfoLog(
        methodName: 'resetPassword',
        statusCode: response.statusCode,
        contentLength: response.contentLength ?? 0,
        conversationId: response.headers['conversation-id'].toString(),
      );
      if (response.statusCode == 204) {
        updateToken(response.headers);
        return response.statusCode;
      } else if (response.statusCode == 401) {
        return ApiResponse.INVALID_AUTH;
      } else if (response.statusCode == 400) {
        return response.body;
      } else {
        return ApiResponse.INTER_SERVER_ERROR;
      }
    } catch (e) {
      Logger.e('${ApplicationUtil.getFormattedCurrentDateAndTime()} $e');
      return 'Error occur while reseting password';
    }
  }

  Future<dynamic> getFleetNameAndSubFleet() async {
    try {
      Logger.i('${ApplicationUtil.getFormattedCurrentDateAndTime()} getFleetNameAndSubFleet API = > ' + "${AppConstant.BASE_URL}/fleets");
      var basicAuth = await _getBasicAuth();
      final response = await http.get(Uri.parse("${AppConstant.BASE_URL}/fleets"), //https://jsonkeeper.com/b/9F6A
          headers: <String, String>{'authorization': basicAuth});
      printInfoLog(
          methodName: 'getFleetNameAndSubFleet',
          statusCode: response.statusCode,
          contentLength: response.contentLength ?? 0,
          conversationId: response.headers['conversation-id'].toString());
      Map<String, List<String>> returnedMap = {};
      if (response.statusCode == 200) {
        updateToken(response.headers);
        Map<String, dynamic> res = jsonDecode(response.body) as Map<String, dynamic>;
        returnedMap['Select'] = ['Select'];
        res.forEach((key, value) {
          List<String> subFleetList = List<String>.from(value);
          subFleetList = ['Select', ...subFleetList];
          returnedMap[key] = subFleetList;
        });
        return returnedMap;

        //return res;
      } else if (response.statusCode == 401) {
        return ApiResponse.INVALID_AUTH;
      } else if (response.statusCode == 400 || response.statusCode == 403) {
        return response.body;
      } else {
        return ApiResponse.INTER_SERVER_ERROR;
      }
    } catch (e) {
      Logger.e('${ApplicationUtil.getFormattedCurrentDateAndTime()} $e');
      return "Network error,try again later$e";
    }
  }

  Future<dynamic> getTailsByFleetNameAndSubFleet({required String fleetName, String? subFleetName}) async {
    bool firstParam = true;
    try {
      String query = "fleets/$fleetName";
      if (subFleetName != null) {
        query += "?subfleet=$subFleetName";
        firstParam = false;
      }
      if (getIt<ServiceType>().type == ServiceRequestType.SCHEDULED) {
        if (firstParam) {
          query += '?requestType=SCHEDULED';
          firstParam = false;
        } else {
          query += '&requestType=SCHEDULED';
        }
      }
      Logger.i('${ApplicationUtil.getFormattedCurrentDateAndTime()} getTailsByFleetNameAndSubFleet API = > ' + "${AppConstant.BASE_URL}/$query");
      var basicAuth = await _getBasicAuth();
      final response = await http.get(Uri.parse("${AppConstant.BASE_URL}/$query"), //https://jsonkeeper.com/b/14TS
          headers: <String, String>{'authorization': basicAuth});
      printInfoLog(
        methodName: 'getTailsByFleetNameAndSubFleet',
        statusCode: response.statusCode,
        contentLength: response.contentLength ?? 0,
        conversationId: response.body,
      );
      if (response.statusCode == 200) {
        updateToken(response.headers);
        List res = jsonDecode(response.body) as List<dynamic>;

        return res;
      } else if (response.statusCode == 401) {
        return ApiResponse.INVALID_AUTH;
      } else if (response.statusCode == 400) {
        return response.body;
      } else {
        return ApiResponse.INTER_SERVER_ERROR;
      }
    } catch (e) {
      Logger.e('${ApplicationUtil.getFormattedCurrentDateAndTime()} $e');
      return "Network error,try again later$e";
    }
  }

  Future<dynamic> cancelServiceRequest({required int requestId, required String comment}) async {
    try {
      Logger.i('${ApplicationUtil.getFormattedCurrentDateAndTime()} cancelServiceRequest API = > ' + "${AppConstant.BASE_URL}/servicerequest");
      var basicAuth = await _getBasicAuth();
      dynamic body = {
        'SERVICE_REQUESTS': [
          {
            'REQUEST_ID': requestId,
            'STATUS': 'CANCEL',
            'COMMENT': comment,
          }
        ]
      };
      log(json.encode(body));
      final response = await http.put(
        Uri.parse("${AppConstant.BASE_URL}/servicerequest"),
        //https://jsonkeeper.com/b/14TS
        headers: <String, String>{
          'authorization': basicAuth,
          'Content-Type': 'application/json',
        },
        body: json.encode(body),
      );
      printInfoLog(
          methodName: 'cancelServiceRequest',
          statusCode: response.statusCode,
          contentLength: response.contentLength ?? 0,
          conversationId: response.headers['conversation-id'].toString());
      if (response.statusCode == 204 || response.statusCode == 200) {
        updateToken(response.headers);
        // List res = jsonDecode(response.body) as List<dynamic>;

        return 204;
      } else if (response.statusCode == 401) {
        return ApiResponse.INVALID_AUTH;
      } else if (response.statusCode == 400) {
        return response.body;
      } else {
        return ApiResponse.INTER_SERVER_ERROR;
      }
    } catch (e) {
      Logger.e('${ApplicationUtil.getFormattedCurrentDateAndTime()} $e');
      return "Network error,try again later$e";
    }
  }

  Future<dynamic> convertToServiceRequest({required int requestId}) async {
    try {
      Logger.i('${ApplicationUtil.getFormattedCurrentDateAndTime()} convertToServiceRequest API = > ' +
          "${AppConstant.BASE_URL}/servicerequest?client=admin request ID:$requestId");
      var basicAuth = await _getBasicAuth();
      dynamic body = {
        'REQUEST_ID': requestId,
        'STATUS': 'CLOSED',
      };
      log(json.encode(body));
      final response = await http.put(
        Uri.parse("${AppConstant.BASE_URL}/servicerequest?client=app"),
        headers: <String, String>{
          'authorization': basicAuth,
          'Content-Type': 'application/json',
        },
        body: json.encode(body),
      );
      printInfoLog(
          methodName: 'convertNotificationToServiceRequest',
          statusCode: response.statusCode,
          contentLength: response.contentLength ?? 0,
          conversationId: response.headers['conversation-id'].toString());
      if (response.statusCode == 204 || response.statusCode == 200) {
        updateToken(response.headers);
        // List res = jsonDecode(response.body) as List<dynamic>;
        return response;
      } else if (response.statusCode == 401) {
        return ApiResponse.INVALID_AUTH;
      } else if (response.statusCode == 400) {
        return response.body;
      } else {
        return ApiResponse.INTER_SERVER_ERROR;
      }
    } catch (e) {
      Logger.e('${ApplicationUtil.getFormattedCurrentDateAndTime()} $e');
      return "Network error,try again later$e";
    }
  }

  /*--------------------------------------------------------------------*/

  void updateToken(Map<String, String> headers) {
    String? token = headers['authorization'];
    if (token != null) {
      getIt<SharedPreferences>().setString('token', 'Bearer $token');
    }
  }

  void printInfoLog({required String methodName, required int statusCode, required int contentLength, required String conversationId}) {
    Logger.i(' ${ApplicationUtil.getFormattedCurrentDateAndTime()} $methodName RESPOND = > ' +
        "STATUS CODE: $statusCode, LENGTH: $contentLength, CONVERSATION ID: "
            "$conversationId");
    print(' ${ApplicationUtil.getFormattedCurrentDateAndTime()} $methodName RESPOND = > ' +
        "STATUS CODE: $statusCode, LENGTH: $contentLength, CONVERSATION ID: "
            "$conversationId");
  }

  Future<dynamic> getOpenServiceRequestCount( {required List<int> equipmentIds, required bool isRestricted}) async {
    String listAsString = equipmentIds.join(',');
    try {
      Logger.i(
          '${ApplicationUtil.getFormattedCurrentDateAndTime()} getOpenServiceRequestCount API = > ' + "${AppConstant.BASE_URL}/openservicerequestscount");
      var basicAuth = await _getBasicAuth();
      final url = Uri.parse("${AppConstant.BASE_URL}/openservicerequestscount?equipmentIds=$listAsString&isRestricted=$isRestricted");
      final response =
      await http.get(url, headers: <String, String>{'authorization': basicAuth});
      printInfoLog(
          methodName: 'getOpenServiceRequestCount',
          statusCode: response.statusCode,
          contentLength: response.contentLength ?? 0,
          conversationId: response.headers['conversation-id'].toString());
      print("============================================");
      print("response.headers: ${response.headers}");
      print(json.encode(response.headers));
      print("============================================");
      if (response.statusCode == 200) {
        updateToken(response.headers);
        var  res = jsonDecode(response.body) ;
        return res;
      } else if (response.statusCode == 401) {
        return ApiResponse.INVALID_AUTH;
      } else if (response.statusCode == 400) {
        return response.body;
      } else {
        return ApiResponse.INTER_SERVER_ERROR;
      }
    } catch (e) {
      Logger.e('${ApplicationUtil.getFormattedCurrentDateAndTime()} $e');
      return "Network error,try again later$e";
    }
  }
  Future<dynamic> getTaskTypeList() async {

    try {
      Logger.i('${ApplicationUtil.getFormattedCurrentDateAndTime()} getTaskTypes API = > '
          "${AppConstant.BASE_URL}/eventtasktypes");
      var basicAuth = await _getBasicAuth();

      final response = await http.get(Uri.parse("${AppConstant.BASE_URL}/eventtasktypes"),
          headers: <String, String>{'Authorization': basicAuth});

      printInfoLog(
          methodName: 'getTaskTypeList',
          statusCode: response.statusCode,
          contentLength: response.contentLength ?? 0,
          conversationId: response.headers['conversation-id'].toString());

      if (response.statusCode == 200) {
        updateToken(response.headers);
        return jsonDecode(response.body);
      } else if (response.statusCode == 401) {
        return ApiResponse.INVALID_AUTH;
      } else if (response.statusCode == 400) {
        return jsonDecode(response.body);
      } else {
        return ApiResponse.INTER_SERVER_ERROR;

      }
    } catch (e) {
      Logger.e('${ApplicationUtil.getFormattedCurrentDateAndTime()} $e');
      return {"error" : "Network error,try again later. $e"};
    }
  }
  Future<dynamic> submitTask({required String taskType,required int equipment_id})async{
    try {
      Logger.i('${ApplicationUtil.getFormattedCurrentDateAndTime()} submitTask API = > '
          "${AppConstant.BASE_URL}/eventtask");
      var basicAuth = await _getBasicAuth();
      final body ={
        'TASK_TYPE': taskType,
        'EQUIPMENT_ID': equipment_id,
      };
      Map<String, String> requestHeaders = {'Content-type': 'application/json', 'Accept': 'application/json', 'Authorization': basicAuth};
      final response = await http.post(Uri.parse("${AppConstant.BASE_URL}/eventtask"),
          headers:requestHeaders,
          body: json.encode(body)
      );

      printInfoLog(
          methodName: 'submitTask',
          statusCode: response.statusCode,
          contentLength: response.contentLength ?? 0,
          conversationId: response.headers['conversation-id'].toString());

      if (response.statusCode == 204) {
        updateToken(response.headers);
        return "SUBMITTED";
      } else if (response.statusCode == 401) {
        return ApiResponse.INVALID_AUTH;
      } else if (response.statusCode == 400) {
        return response.body;
      } else {
        return ApiResponse.INTER_SERVER_ERROR;
      }
    } catch (e) {
      Logger.e('${ApplicationUtil.getFormattedCurrentDateAndTime()} $e');
      return "Network error,try again later. $e";
    }

  }

}

class AppVersion {
  final String iosVersion;
  final String iosBuildNumber;
  final String androidVersion;
  final String androidBuildNumber;

  AppVersion({
    required this.iosVersion,
    required this.iosBuildNumber,
    required this.androidVersion,
    required this.androidBuildNumber,
  });

  factory AppVersion.fromJson(Map<String, dynamic> json) {
    return AppVersion(
      iosVersion: json['ios']['version_number'],
      iosBuildNumber: json['ios']['build_number'],
      androidVersion: json['android']['version_number'],
      androidBuildNumber: json['android']['build_number'],
    );
  }
}

 Future<AppVersion> fetchAppVersion({required bool isSandbox}) async {
  final url = isSandbox ? 'https://alink-api-sandbox.onrender.com/appversion' : 'https://alink-api.onrender.com/appversion';

  final response = await http.get(Uri.parse(url));
  Logger.i(' ${ApplicationUtil.getFormattedCurrentDateAndTime()}\nResponse from $url\n${response.body}');
  if (response.statusCode == 200) {
    return AppVersion.fromJson(jsonDecode(response.body));
  } else {
    throw Exception('Failed to load app version');
  }
}
