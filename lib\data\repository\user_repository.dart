import 'package:alink/data/model/user.dart';
import 'package:alink/data/repository/user_service.dart';
import 'package:alink/database/database.dart';
import 'package:flutter/material.dart';

class UserRepository {
  final UserService userService;

  UserRepository({required this.userService}) : assert(userService != null);

  Future<dynamic> loginAndFetchUser(
      {required String email,
      required String password,
      required int customerId}) async {
    return await userService.loginUserWIthEmailAndPassword(
        email: email, password: password, customerId: customerId);
  }

  Future<dynamic> isUserLoggedIn() {
    return userService.checkUserLoggedIn();
  }

  Future<dynamic> getLoggedInUser() {
    return userService.checkUserLoggedIn();
  }

  Future<dynamic> logoutUser() {
    return userService.logoutUser();
  }
}
