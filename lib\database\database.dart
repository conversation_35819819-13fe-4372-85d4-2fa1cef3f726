import 'package:alink/data/dao/audit_status_dao.dart';
import 'package:alink/data/dao/repair_dao.dart';
import 'package:alink/data/dao/service_request_dao.dart';
import 'package:alink/data/dao/user_dao.dart';
import 'package:alink/data/model/audit_status.dart';
import 'package:alink/data/model/config.dart';
import 'package:alink/data/model/repair.dart';
import 'package:alink/data/model/service_request.dart';
import 'package:alink/data/model/user.dart';
import 'package:drift/drift.dart';

import '../../logger/logger.dart';

import '../data/dao/tasks_dao.dart';
import '../data/model/task.dart';
import '../util/application_util.dart';

part 'database.g.dart';

@DriftDatabase(
  tables: [Config, User, ServiceRequest, Repair, AuditStatus,Tasks],
  daos: [ServiceRequestDao, UserDao, RepairDao, AuditStatusDao,TaskDao],
  queries: {
    'pendingServiceRequestCount': 'SELECT COUNT(*) FROM SERVICE_REQUEST WHERE IS_QUEUED = true',
  },
)
class Database extends _$Database {
  var logger = Logger();
  Database(QueryExecutor e) : super(e);
  Database.connect(DatabaseConnection connection) : super.connect(connection);
  @override
  int get schemaVersion => 3;

  @override
  MigrationStrategy get migration {
    Logger.i("New Database version is: $schemaVersion");
    return MigrationStrategy(
      onCreate: (Migrator m) {
        return m.createAll();
      },
      onUpgrade: (Migrator m, int from, int to) async {
        Logger.i("On Database Updgrade\nExisting Database version is: $from\nNew Database version is: $schemaVersion");
        if (from < 2) {
          await m.addColumn(serviceRequest, serviceRequest.safety as GeneratedColumn<Object>);
          await m.addColumn(repair, repair.safety as GeneratedColumn<Object>);
        }
        if (from < 3) {
          await m.createTable(tasks);
        }
      },
      beforeOpen: (details) async {
        if (details.wasCreated) {
          insertDefaultDat();
        }
      },
    );
  }

  Future<void> insertDefaultDat() async {
    var data = ConfigData(
      id: 1,
      key: ('This is an example of moor item table'),
      value: ('{"name":"Delton","age":5}'),
    );
    var data2 = ConfigData(
      id: 2,
      key: ('This application will work in all device'),
      value: ('{"name":"John","age":10}'),
    );
    var data3 = ConfigData(
      id: 3,
      key: ('This application will work in all device'),
      value: ('{"name":"Smith","age":15}'),
    );

    Logger.i("${ApplicationUtil.getFormattedCurrentDateAndTime()} Database initiated");
  }

  Future<void> deleteEverything() {
    return transaction(() async {
      // you only need this if you've manually enabled foreign keys
      // await customStatement('PRAGMA foreign_keys = OFF');
      for (final table in allTables) {
        Logger.i('Deleting table ${table.actualTableName}');
        try{
          await delete(table).go();
          Logger.i('Deleted table ${table.actualTableName}');

        }catch(e){
          Logger.e("error deleting table ${table.actualTableName} . ${e.toString()}");
        }
      }
    });
  }

  Future<void> truncateTables() {
    return transaction(() async {
      // you only need this if you've manually enabled foreign keys
      // await customStatement('PRAGMA foreign_keys = OFF');
      await delete(repair).go();
      await delete(serviceRequest).go();
      await delete(auditStatus).go();
      await delete(tasks).go();
    });
  }

  Future<void> deleteAuditDAO() {
    return transaction(() async {
      // you only need this if you've manually enabled foreign keys
      // await customStatement('PRAGMA foreign_keys = OFF');
      await delete(auditStatus).go();
    });
  }

  Future deleteAll() async {
    final m = createMigrator();
    // Going through tables in reverse because they are sorted for foreign keys
    for (final table in allTables.toList().reversed) {
      await m.deleteTable(table.actualTableName);
    }
    await m.createAll();
  }
}
