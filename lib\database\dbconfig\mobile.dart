import 'dart:io';
import 'package:alink/isolate/http_isolate_repair.dart';
import 'package:alink/isolate/http_isolate_service.dart';
import 'package:alink/logger/logger.dart';
import 'package:alink/service_locator.dart';
import 'package:alink/util/app_constant.dart';
import 'package:drift/native.dart';
import 'package:drift/drift.dart';
import 'package:path/path.dart' as p;
import 'package:shared_preferences/shared_preferences.dart';
import '../../isolate/http_isolate_task.dart';
import '../database.dart';
import 'dart:isolate';
import 'package:drift/isolate.dart';
import 'package:path_provider/path_provider.dart';

DriftIsolate? moorIsolate;
ReceivePort? receivePort;

Database constructDb({bool logStatements = false}) {
  //return Database(WebDatabase('db', logStatements: logStatements));
  return Database.connect(createMoorIsolateAndConnect());
}

DatabaseConnection createMoorIsolateAndConnect() {
  return DatabaseConnection.delayed(() async {
    moorIsolate = await _createMoorIsolate();
    return await moorIsolate!.connect();
  }());
}

Future<DriftIsolate> _createMoorIsolate() async {
  final dir = await getApplicationDocumentsDirectory();
  final path = p.join(dir.path, 'db.sqlite');
  final receivePort = ReceivePort();

  await Isolate.spawn(
    _startBackground,
    _IsolateStartRequest(receivePort.sendPort, path),
  );

  // _startBackground will send the MoorIsolate to this ReceivePort
  return await receivePort.first as DriftIsolate;
}

void _startBackground(_IsolateStartRequest request) {
  final executor = LazyDatabase(() async {
    final dbFile = File(request.targetPath);
    return NativeDatabase(
      dbFile,
      logStatements: false,
    );
  });

  //FfiCheckOutBox.setupSqlcipher();
  DriftIsolate moorIsolate = DriftIsolate.inCurrent(
    () => DatabaseConnection.fromExecutor(executor),
  );
  // inform the starting isolate about this, so that it can call .connect()
  request.sendMoorIsolate.send(moorIsolate);
}

class _IsolateStartRequest {
  final SendPort sendMoorIsolate;
  final String targetPath;

  _IsolateStartRequest(this.sendMoorIsolate, this.targetPath);
}

Map<String, dynamic> getMapData() {
  receivePort = ReceivePort();
  String token = _getBasicAuth();
/*  receivePort.listen((message) {
    log(message);
  });*/
  return {
    'sendPort': receivePort!.sendPort,
    'moorSendPort': moorIsolate!.connectPort,
    'token': token,
    'logPath': Logger.logPath,
    'apiUrlPath': AppConstant.BASE_URL
  };
}

Future<dynamic> httpRequestRespond() async {
  /*receivePort.listen((message) {
    log('respond');
    log(message);
    return message;
  });*/
  return await receivePort!.first;
}

Future<Database> getWorkerDatabase(Map<String, dynamic> map) async {
  SendPort moorSendPort = map['moorSendPort'];
  DriftIsolate moorIsolate = DriftIsolate.fromConnectPort(moorSendPort);
  return Database.connect(await moorIsolate.connect());
}

getReturnServiceResponse(
    Map<String, dynamic> map, IsolateServiceResponse isolateServiceResponse) {
  SendPort mainSendPort = map['sendPort'];
  mainSendPort.send(isolateServiceResponse);
}
getReturnTaskeResponse(
    Map<String, dynamic> map, IsolateTaskResponse isolateTaskResponse) {
  SendPort mainSendPort = map['sendPort'];
  mainSendPort.send(isolateTaskResponse);
}


getReturnRepairResponse(
    Map<String, dynamic> map, IsolateRepairResponse isolateServiceResponse) {
  SendPort mainSendPort = map['sendPort'];
  mainSendPort.send(isolateServiceResponse);
}

_getBasicAuth() {
  return getIt<SharedPreferences>().getString('token');
  //return base64Encode(utf8.encode('$token'));
}

/*Database constructDb({bool logStatements = false}) {
  if (Platform.isIOS || Platform.isAndroid) {
    final executor = LazyDatabase(() async {
      final dataDir = await paths.getApplicationDocumentsDirectory();
      final dbFile = File(p.join(dataDir.path, 'db.sqlite'));
      return VmDatabase(dbFile, logStatements: logStatements);
    });
    return Database(executor);
  }
  if (Platform.isMacOS || Platform.isLinux) {
    final file = File('db.sqlite');
    return Database(VmDatabase(file, logStatements: logStatements));
  }
  return Database(VmDatabase.memory(logStatements: logStatements));
}*/
