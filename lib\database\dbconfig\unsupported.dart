import 'package:alink/database/database.dart';
import 'package:alink/isolate/http_isolate_repair.dart';
import 'package:alink/isolate/http_isolate_service.dart';
import 'package:alink/isolate/http_isolate_task.dart';

Database constructDb({bool logStatements = false}) {
  throw 'Platform not supported';
}

Map<String, dynamic> getMapData() {
  throw 'Platform not supported';
}

httpRequestRespond() async {
  throw 'Platform not supported';
}

Future<Database> getWorkerDatabase(Map<String, dynamic> map) async {
  throw 'Platform not supported';
}

getReturnServiceResponse(
    Map<String, dynamic> map, IsolateServiceResponse isolateServiceResponse) {
  throw 'Platform not supported';
}

getReturnRepairResponse(
    Map<String, dynamic> map, IsolateRepairResponse isolateServiceResponse) {
  throw 'Platform not supported';
}

getReturnTaskResponse(
    Map<String, dynamic> map, IsolateTaskResponse isolateServiceResponse) {
  throw 'Platform not supported';
}
