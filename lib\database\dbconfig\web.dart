import 'dart:html';

import 'package:alink/database/database.dart';
import 'package:alink/isolate/http_isolate_repair.dart';
import 'package:alink/isolate/http_isolate_service.dart';
import 'package:alink/service_locator.dart';
import 'package:alink/util/app_constant.dart';
import 'package:drift/drift.dart';
import 'package:drift/web.dart';
import 'package:drift/remote.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../isolate/http_isolate_task.dart';

QueryExecutor executor = WebDatabase('db', logStatements: true);
Database constructDb({bool logStatements = false}) {
  return Database(executor);
  //return Database.connect(connectToWorker());
}

DatabaseConnection connectToWorker() {
  final worker = SharedWorker('db.dart.js');
  return remote(worker.port!.channel());
}

Map<String, dynamic> getMapData() {
  String token = _getBasicAuth();
  return {
    'sendPort': null,
    'moorSendPort': null,
    'token': token,
    'apiUrlPath': AppConstant.BASE_URL
  };
}

httpRequestRespond() async {
  return null;
}

Future<Database> getWorkerDatabase(Map<String, dynamic> map) async {
  return constructDb();
  //return Database.connect(connectToWorker());
}

getReturnServiceResponse(
    Map<String, dynamic> map, IsolateServiceResponse isolateServiceResponse) {
  return isolateServiceResponse;
}

getReturnTaskResponse(
    Map<String, dynamic> map, IsolateTaskResponse isolateTaskResponse) {
  return isolateTaskResponse;
}


getReturnRepairResponse(
    Map<String, dynamic> map, IsolateRepairResponse isolateServiceResponse) {
  return isolateServiceResponse;
}

_getBasicAuth() {
  return getIt<SharedPreferences>().getString('token');
  //return base64Encode(getReturnServiceResponseutf8.encode('$token'));
}
