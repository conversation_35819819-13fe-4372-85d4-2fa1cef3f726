{"enterCustomer": "Enter customer ", "nameAndEmail": "name and email ", "below": "below", "enterCustomerName": "Enter customer name", "enterCustomerEmail": "Enter customer email", "enterYourPassword": "Enter your password", "password": "Password", "login": "<PERSON><PERSON>", "skip": "<PERSON><PERSON>", "skipToHome": "Skip to Home", "goodMorning": "Good Morning", "unsent": "Unsent", "syncing": "Syncing", "allDataSync": "All data synced", "serviceRequestThisMonth": "Service request reported this month", "serviceRequestToday": "Service request reported today", "serviceRequestByYou": "Service request reported by you", "fireEmergencyAtTerminalHint": "Fire emergency at terminal T1 ,Gate G3. Be On Alert", "broadcast": "Broadcast", "scanToReport": "Scan to report", "confirmTerminalAndGate": "Confirm Terminal and gate", "confirmLocationMessage": "If this equipment is installed at a different location please do update it here.", "select": "Select", "terminal": "TERMINAL", "gate": "GATE", "selectTask": "Select Task", "enterTheDescriptionForProblem": "Enter the description of the problem", "addPhotos": "Add Photos", "finish": "Finish", "cancel": "Cancel", "serviceRequestExist": "Service Request already exists!", "viewDetails": "View Details", "createNew": "Create New", "inProgress": "In Progress", "edit": "Edit", "imageNotYetPicked": "You have not yet pick an image.", "gettingStarted": "Get Started", "innovativeSolutionsForCustomer": "Innovative solutions for customer facing textiles.", "assurance": "Assurance", "deliverWorldClass": "Delivering World Class Results To Any Size Project, Anywhere.", "feasibility": "Feasibility", "controlEverything": "Control everything on your fingertips just by pressing few button.", "security": "Security", "afsProvideFully": "AFS provide fully secure and smooth transaction so that you don't have to worry.", "alink": "<PERSON><PERSON>", "anEmailContainingInstructionSent": "An email containing instructions to reset password is sent. Please check your inbox.", "totalNumberServiceRequestClosed": "Total number of service requests closed.", "totalNumberServiceRequestCreated": "Total number of service requests created.", "requestPermission": "Request Permission", "emailRequired": "Email is required", "forgotPassword": "Forgot Password", "email": "Email", "enterPassword": "Enter your password", "enter": "Enter", "emailAndPassword": "email and password", "youAreLogInAs": "You are logged in as:", "welcomeBack": "Welcome back ", "pleaseAuthenticateToUseApp": "Please authenticate to continue using ALink App", "selectCustomer": "Select Customer", "selectAPI": "Select API", "barcodeAlreadyAssign": "Barcode already assigned to an equipment", "pleaseConnectToInternet": "Please connect to internet", "identifyConcern": "Identify Concern", "createServiceRequestIfYouNotice": "Create a service request if you notice an issue.", "repair": "Repair", "completeServiceRequesByService": "Complete a service request and record the parts used during servicing.", "checkIndividualEquipment": "Check individual equipment and create service request based on need", "serviceRequestCreated": "Service Request Created Successfully!", "repairCompleted": "Repair Completed Successfully", "barcodeReplaced": "Barcode Replaced Successfully", "equipmentMovedSuccess": "Equipment Moved Successfully", "equipmentAssigned": "Equipment Assigned Successfully", "notificationCreated": "Notification Created Successfully", "noImageSelected": "No Image Selected", "youHaveNotYetPickedImage": "You have not yet picked an image.", "selectAspectRatio": "Select aspect ratio", "square": "square", "croppedImage": "Cropped image", "backToEdit": "Back to Edit", "done": "Done", "filterServiceRequest": "Filter Service Request", "barcodeNo": "BARCODE NO.", "enterBarcodeNo": "Enter the barcode number", "partNo": "PART NO", "enterPartno": "Enter the part number", "selectPartno": "Select the part number", "unitType": "UNIT TYPE", "taskType": "TASK TYPE", "applyFilter": "Apply Filter", "clearFilter": "Clear Filter", "searchParts": "Search parts", "addParts": "Add Parts", "photos": "PHOTOS", "terminalServices": "TERMINAL SERVICES", "barcode": "Barcode", "requested": "REQUESTED", "selectTerminalAndGate": "Select Terminal and Gate", "audit": "Audits", "auditCompleteSuccessfully": "Audit Completed Successfully", "noRecordFound": "No Record Found", "auditList": "Audit List", "createAdhocAudit": "Create Adhoc Service", "provideCommentForAudit": "Provide a comment for creating adhoc audit", "addComment": "Add Comment", "creatingAnAdhoc": "Eg: Creating an Adhoc audit", "create": "Create", "commentIsRequired": "Comment is required", "asOn": "As on", "serviceRequest": "Service Request", "serviceRequests": "Service Requests", "fetching": "Fetching", "barcodeAssignedToEquipment": "Barcode already assigned to an equipment", "moveEquipment": "Move Equipment", "assignNewEquipment": "Assign New Equipment", "scanToRepair": "<PERSON>an to Repair", "noServiceFound": "No Service Request Found", "noServiceRequestDesc": "There is no service request found for this equipment. Are you sure you want to undertake the repair work?", "noServiceRequestDescForStadium": "There is no service request found for this Asset. Are you sure you want to undertake the repair work?", "continueString": "Continue", "scheduleService": "Schedule Service Request", "overdue": "OVERDUE", "processing": "Processing", "unknown": "Unknown", "errorOccurWhileFetching": "Error occur while fetching", "errorOccurWhileUpdating": "Error occur while updating", "couldNotSendNotification": "Could not send notification", "emailNotValid": "Email Id is not valid,Please check", "unScanned": "Unscanned", "allServiceRequests": "All Service requests", "netWorkErrorTryAgain": "Network error,try again later", "errorOccurWhileFetchingServiceRequest": "Error occur while fetching service request", "errorOccurWhileResettingPassword": "Error occur while resetting password", "invalidAuth": "Invalid Authentication Credentials", "userDisabled": "User Disabled", "internalServerError": "Internal server error", "goodAfternoon": "Good Afternoon", "goodEvening": "Good Evening", "noNetworkConnected": "No network connected! connect to a network", "completedSuccess": "completed successfully", "submit": "Submit", "duplicateService": "Eg. Duplicate Service Request", "unsavedChange": "Unsaved changes", "youWillLoseYourData": "You will lose your unsaved changes if you go back", "goBack": "Go back", "barcodeNotfound": "Barcode not found", "enterBarcode": "Enter Barcode number", "barcodeNumber": "Barcode number", "cameraPermission": "Camera Permission", "photosPermission": "Photos Permission", "thisAppNeedsCameraAcces": "This app needs camera access to take pictures to upload.", "thisAppNeedsPhotosAcces": "This app needs photos access to upload the pictures.", "deny": "<PERSON><PERSON>", "settings": "Settings", "messages": "Messages", "createOn": "Created on", "createdBy": "Created by", "noMessages": "No Messages Found", "fetchingMessage": "Fetching Message", "partList": "Parts List", "noPartFound": "No Part Found", "startRepair": "Start Repair", "remaining": "REMAINING", "closed": "CLOSED", "buildNo": "Build No", "resetApp": "Reset App", "save": "Save & Exit", "sendLog": "Send Log", "resetApplication": "Reset application", "thisWillDeleteAllData": "This will delete all the data associated with the app on the device and restore the application to a freshly installed state (Requires application restart).", "reset": "Reset", "restartApplication": "Restart Application", "deleteAllDataFromApp": "Deleted all data from the app. You can close this app and restart.", "setLogLevel": "Set Log Level", "logout": "Logout", "allDataNeedToSync": "All data need to be synchronized before logout", "pickImageError": "Pick image error", "confirmTerminal": "Confirm Terminal and Gate", "areYouSureYouWantToCreateService": "Are you sure you want to create this service request without any images?", "areYouSureYouWantToCompleteRepair": "Are you sure you want to complete this repair without any images?", "areYouSureYouWantToCreateNotification": "Are you sure you want to create this notification without adding any images?", "descriptionCantBeEmpty": "Description can't be empty", "egSeatIsMoved": "Eg. Seat A13 is moved from Gate 1 to Gate 2", "egBrokenChair": "Eg. Broken chair", "browse": "Browse", "terminalNotSelected": "Terminal not selected", "change": "Change", "selectAtleastOneTask": "Select at least one task", "selectAtleastOneSubTask": "Select at least one sub task", "suggestedParts": "Suggested Parts", "complete": "Complete", "forThisEquipment": "for this equipment", "cancelServiceRequestSuccess": "Cancel Service Request Successfully", "cancelServiceRequest": "Cancel Service Request", "doYouWantToCancelServiceRequest": "'Are you sure you want to cancel this service request?", "createNewServiceRequest": "Create New Service Request", "replaceBarcode": "Replace Barcode", "photosOfRepair": "Photos of repair work", "confirmBarcodeReplacement": "Confirm Barcode Replacement", "areYouSureAssociateBarcode": "Are you sure you want to associate barcode number", "equipmentName": "Equipment Name", "enterEquipmentName": "Enter equipment name", "equipmentDescription": "Equipment Description", "addSomeDescriptionEquipment": "Add some description for equipment", "equipmentCategory": "Equipment Category", "equipmentCategoryIsRequired": "Equipment Category is required.", "partItemIsRequired": "Part Item is required.", "equipmentPartIsRequired": "Equipment name is required.", "equipmentDescIsRequired": "Equipment description is required.", "equipmentPart": "Equipment Part", "equipmentList": "Equipment List", "scanToAudit": "Scan to audit", "createNewNotification": "Create New Notification", "newEquipment": "New Equipment", "validate": "Validate", "wellDoneEquipmentScanned": "Well done! All equipment scanned.", "noEquipmentsStillCompleteAudit": "There are no equipment at this location. Please continue to submit this audit to mark it as completed.", "noEquipmentsStillCompleteTask": "There are no equipment at this location. Please continue to submit this task to mark it as completed.", "confirmAudit": "Confirm Audit", "movement": "Movement", "confirm": "Confirm", "submitAudit": "Submit Audit", "mark": "<PERSON>", "asInActive": "as inactive", "thisEquipmentIsMarkedAsInactive": "This equipment will be marked as inactive when you submit the Task.", "thisEquipmentWillBeInActive": "This equipment will be marked as inactive", "reject": "Reject", "scanned": "Scanned", "equipmentMoved": "Equipment movement will be confirmed when you submit the Task.", "equipmentMovementNoticed": "Equipment movement noticed.", "equipmentPartOfAudit": "Equipment part of service", "equipmentOutsideAudit": "Equipment outside the service", "getTheBarcodeInSquare": "Get the barcode within the square", "selectFleetAndSubFleet": "Select Fleet / Sub Fleet", "selectTail": "Select Tail", "fleet": "Fleet", "subFleet": "Sub Fleet", "tail": "Tail", "descriptionIsRequired": "Description is required", "pleaseAddAtleastOnePhoto": "Please add at least one photo", "createNotification": "Create Notification", "egAllCushionsCoverDamage": "Eg. All cushions cover damage", "noteSelectAllPartsThatAreReplaced": "Note: Select all the parts that are being replaced now. If some parts in the list cannot be replaced now, uncheck the selection. You can always come back to this and mark them as replaced later. This Repair record would get closed only after all parts are replaced.", "atleastOnePhotosIsRequired": "At least one photo is required for this equipment", "repairParts": "Repair Parts", "partsSuggested": "Parts Suggested", "baseLinePhotos": "Baseline Photos", "damageParts": "Damage Parts", "serviceRequestCancelled": "Service Request Cancelled", "repairInProgress": "Repair in progress", "repairSeat": "Repair <PERSON>t", "noServiceRequestFound": "No Service Request Found", "selectServiceRequest": "Select Service Request", "completedSuccessfully": "completed successfully", "thereIsNoServiceRequestWithThisEquipment": "There is no service request associated with this equipment. However, you can continue to record the repair work done on this equipment which will be captured in the system.", "thereIsNoServiceRequestWithThisAsset": "There is no service request associated with this asset. However, you can continue to record the repair work done on this asset which will be captured in the system.", "warning": "Warning", "thereAreOpenServiceRequest": "There are open service requests for this tail number. Working on this tail number may require data to be downloaded again. Are you sure you want to go back?", "thereAreOpenServiceRequestforStadium": "There are open service requests for this location. Working on this location may require data to be downloaded again. Are you sure you want to go back?", "seat": "<PERSON><PERSON>", "location": "Location", "noteSelectAllThePartThatAreReplaced": "Note: Select all the parts that are being replaced now. If some parts in the list cannot be replaced now, leave the selection as it is. You can always come back to this and mark them as replaced later. This Repair record would get closed only after all parts are replaced.", "damagePhotos": "Damage Photos", "next": "Next", "prev": "Prev", "noAuditFound": "No Audit Found", "auditSubmitted": "Service Submitted Successfully", "noteSelectThePartWhichNeed": "Note: Select the parts which need to be replaced and for each part selected score can only be less than or same as the default score.", "partName": "Part Name", "score": "Score", "reason": "Reason", "reasonCode": "Reason Code", "other": "OTHER", "enterReason": "Enter reason", "rc": "RC", "recordedConcerns": "Recorded Concerns", "anInnovativeSoftwareSolutionDescFirstLine": "An innovative software solution that empowers our partners to deliver the safest, cleanest, highest quality customer contact environments in their industries…", "anInnovativeSoftwareSolutionDescSecondLine": "…with the data and precision that efficiently drives customer experience up and costs down.", "onePlatformToPlanYourWork": "One platform to plan your work, audit your spaces, control your inventory, and track your budget.", "okay": "Okay", "fetchingParts": "Fetching Parts", "quantity": "Quantity", "atLeastRepairAPartToContinue": "At least repair a part to continue ", "terminalOrGateNOtSelected": "Terminal/gate not selected", "enterDescription": "Enter description", "partsUsed": "Parts Used", "addRepairPhotos": "Add Repair Photos", "details": "Details", "equipmentNameIsRequired": "Equipment name is required.", "areYouSureAssignEquipmentWithoutImage": "Are you sure you want to assign new equipment without any images?", "selectAirCraft": "Select Aircraft", "selectAtLeastOnePartContinue": "Select at least one part to continue", "updateParts": "Update Parts", "selectEquipmentToRepair": "Select equipment to repair", "selectAssetToRepair": "Select asset to repair", "selectEquipment": "Select Equipment", "selectAsset": "Select Asset", "scoreEachPart": "Note: Score each part accordingly.", "selectPartsWhichNeedToReplacedYouCannotSelectOpenServiceRequest": "Select parts which need to be replaced. Some parts may not be available for selection if there are unresolved concerns.", "markAsRepaired": "<PERSON> as repaired", "alert": "<PERSON><PERSON>", "clickingOnThisResetWillClearAllTheTempData": "Clicking on this reset button clears all the temporary data recorded for this equipment, including the selected parts, damage and repair photos. Are you sure you want to continue?", "update": "Update", "selectAudit": "Select Audit", "auditCreateSuccessfully": "Audit Created Successfully", "taskCreateSuccessfully": "Task Created Successfully", "pleaseSelectLocation": "Please select location", "selectLocation": "Select Location", "getAudit": "Get Audit", "confirmLocation": "Confirm Location", "thisEmailIsAlreadyUsedInOtherLandscape": "This email id is already used with an other landscape in this device. You need to do a clear data to login with this email id", "pendingAuditsMessage": "requires that the audits at the following locations are completed by", "pendingAudit": "Pending Audits", "auditCompletedMessage": "All audits which are part of Monthly Seating Audit are complete.", "auditComplete": "Audit Completed", "auditItemNotFound": "this barcode does not exist!", "enableConditionAudit": "Enable Conditional Audit", "conditionAuditMessage": "Please scan at least one Equipment at this location.", "error": "Error", "successfulScan": "Successful Scan!", "alreadyScanned": " Barcode already scanned!", "scannedItemsRemoved": "Some scanned equipment which are not part of this audit have been removed.", "isInActiveEquipmentCannotCreateNotification": "This Equipment is marked as inactive, can not create notification.", "barcodeNotFoundDescription": "The scanned bar code cannot be found in the database. Would you like to assign any equipment to that bar code?", "pleaseWait": "Please Wait...", "assign": "Assign", "convertNotificationToServiceRequest": "Convert to service request", "convertAndStartRepair": "Convert & Start Repair", "noServiceRequestNotification": "No Service Request or Notifications found", "repairDetails": "Repair Details", "scheduledServices": "Scheduled Services", "scheduledAuditsAndTasks": "Scheduled audits and tasks.", "selectServiceType": "Select Service Type", "getServices": "Get Services", "enableConditionTask": "Enable Conditional Task", "scanEquipment": "Scan Equipment", "submitTask": "Submit Task", "confirmTask": "Confirm Task", "pleaseWaitSubmittingTask": "Please wait while submitting Task", "taskSubmittedSuccessfully": "Task submitted successfully", "creatingAnService": "Eg: Creating an Adhoc Service", "provideCommentForService": "Provide a comment for creating adhoc service", "timedServicesDashboard": "Timed services (Parts Required)", "timedServices": "Timed Services", "timedServiceDescription": "Service requests which needs to be completed within a certain duration.", "getTimedServiceRequest": "Get Timed Services", "noTimedServiceRequestWithBarcode": "There is no timed service schedule associated with this equipment.", "cancelledTimedServiceSuccess": "Cancelled timed service successfully.", "airlineSubmitAuditConfirmationDescription": "Before you submit this audit, make sure that each equipment at this location are audited.", "airlineSubmitAuditConfirmationDescriptionForStadium": "Before you submit this audit, make sure that each asset at this location are audited.", "airlineSubmitTaskConfirmationDescription": "Before you submit this task, make sure that this task is fully completed at this location.", "setCameraPermission": "Permission to access Camera is required but not enabled for this app. Go to Settings app, search for ALink and make sure that the  appropriate permissions are set.", "setPhotosPermission": "Permission to access Photos is required but not enabled for this app. Go to Settings app, search for ALink and make sure that the  appropriate permissions are set.", "setLocationPermission": "Permission to access location is required but not enabled for this app. Go to Settings app, search for ALink and make sure that the  appropriate permissions are set.", "markAsSafetyIssue": "Mark as safety issue", "safetyIssue": "Safety issue", "locationPermissionRequired": "Location permission required", "appRequiresLocationPermission": "This app requires 'Allow While Using the App' and 'Precise Location' to accurately determine your device's location.", "appRequiresLocationPermissionforweb": "This app requires location permission. You can enable this by going into this browser's address bar and click on location icon to enable permission.", "jobAlreadySubmitted": "Job is already submitted", "saveSuccess": "Saved successfully", "tasks": "Tasks", "task": "Task", "scanbarcodeandcompletetask": "Scan the barcode and complete the task", "selectTaskToBeCompletedFor": "Select the task to be completed for ", "pleaseSelectATask": "Please select the task to be completed for", "yesCancelRequest": "Yes, Cancel Request", "keepRequest": "Keep Request"}