import 'package:alink/bloc/api/api_bloc.dart';
import 'package:alink/bloc/audit/audit_equipment_cubit.dart';
import 'package:alink/bloc/open_serviceRequest_bloc/open_service_request_bloc.dart';
import 'package:alink/bloc/repair_db_bloc/repair_bloc.dart';
import 'package:alink/bloc/service/service_request_bloc.dart';
import 'package:alink/bloc/task/task_bloc.dart';
import 'package:alink/bloc/user_bloc.dart';
import 'package:alink/cubit/filter/filter_cubit.dart';
import 'package:alink/cubit/internet/internet_cubit.dart';
import 'package:alink/cubit/location/location_cubit.dart';
import 'package:alink/cubit/pages/audit/audit_list_cubit.dart';
import 'package:alink/cubit/pages/repair_detail/repair_detail_api_cubit.dart';
import 'package:alink/data/dao/audit_status_dao.dart';
import 'package:alink/data/dao/repair_dao.dart';
import 'package:alink/data/dao/service_request_dao.dart';
import 'package:alink/data/dao/tasks_dao.dart';
import 'package:alink/data/dao/user_dao.dart';
import 'package:alink/data/repository/api_repository.dart';
import 'package:alink/data/repository/api_service.dart';
import 'package:alink/data/repository/user_repository.dart';
import 'package:alink/data/repository/user_service.dart';
import 'package:alink/database/database.dart';
import 'package:alink/database/dbconfig/shared.dart';
import 'package:alink/isolate/http_isolate_task.dart';
import 'package:alink/logger/logger.dart';
import 'package:alink/pages/airport/audit/audit_page.dart';
import 'package:alink/provider/task_type_provider.dart';
import 'package:alink/service_locator.dart';
import 'package:alink/util/app_color.dart';
import 'package:alink/util/application_util.dart';
import 'package:alink/util/route_generator.dart';
import 'package:camera/camera.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'bloc/audit_bloc/audit_bloc.dart';
import 'bloc/part_items/selected_part_item_bloc.dart';
import 'l10n/l10n.dart';

GlobalKey globalKey = GlobalKey();

List<CameraDescription>? cameras;

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  // Initialize your service locator or dependency injection system
  await setupLocator();

  // Initialize cameras
  try {
    cameras = await availableCameras();
  } catch (e) {
    cameras = [];
    Logger.e("Error initializing cameras: $e");
  }

  Logger.i("Before initializing Firebase");
  print(cameras.toString());

  // Initialize Firebase
  if (kIsWeb) {
    await Firebase.initializeApp(
      options: const FirebaseOptions(
        apiKey: "AIzaSyACzdMun-EJU4HRFmt4IvWYLQf8obvbQ-o",
        appId: "1:655096066337:web:7953c9841471c0564a8e60",
        messagingSenderId: "655096066337",
        projectId: "afs-alink",
      ),
    );
  } else {
    await Firebase.initializeApp();

    // Load SharedPreferences
    final prefs = await SharedPreferences.getInstance();
    String userId = prefs.getInt('userId')?.toString() ?? "Unknown";
    String customerName = prefs.getString('customerName') ?? "Unknown";
    String userEmail = prefs.getString('email') ?? "Unknown";

    // Set custom keys for Crashlytics
    FirebaseCrashlytics.instance.setCustomKey("user_id", userId);
    FirebaseCrashlytics.instance.setCustomKey("user_email", userEmail);
    FirebaseCrashlytics.instance.setCustomKey("customer_name", customerName);

    // Log additional information to Crashlytics
    FirebaseCrashlytics.instance.log("User ID: $userId");
    FirebaseCrashlytics.instance.log("User Email: $userEmail");
    FirebaseCrashlytics.instance.log("Customer Name: $customerName");

    // Set up Flutter error handling
    FlutterError.onError = (FlutterErrorDetails errorDetails) {
      FirebaseCrashlytics.instance.recordFlutterFatalError(errorDetails);
    };

    // Handle uncaught asynchronous errors
    PlatformDispatcher.instance.onError = (Object error, StackTrace stack) {
      FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
      return true;
    };
  }

  // Set up system UI overlay styles
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent, // Transparent status bar
      statusBarIconBrightness: Brightness.dark, // Dark text for status bar
    ),
  );

  runApp(MyApp(
    connectivity: Connectivity(),
  ));
}

class MyApp extends StatefulWidget {
  final Connectivity connectivity;
  MyApp({required this.connectivity});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  final _router = RouteGenerator();

  @override
  Widget build(BuildContext context) {
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.dark); // 1
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(
          create: (context) =>TaskTypeProvider(),
        ),
        MultiRepositoryProvider(
          providers: [
            RepositoryProvider<Database>(
              create: (context) {
                Database db =constructDb();
                return db;
              },
            ),
            RepositoryProvider<UserRepository>(create: (context) {
              var db = RepositoryProvider.of<Database>(context);
              return UserRepository(
                userService: UserService(userDao: UserDao(db)),
              );
            }),
            RepositoryProvider<ApiRepository>(
              create: (context) => ApiRepository(apiService: ApiService()),
            ),
          ],
          child: MultiBlocProvider(
            providers: [
              BlocProvider<InternetCubit>(
                lazy: false,
                create: (context) => InternetCubit(connectivity: widget.connectivity),
              ),
              BlocProvider<UserBloc>(
                create: (context) {
                  return UserBloc(
                    repository: RepositoryProvider.of<UserRepository>(context),
                  );
                },
              ),
              BlocProvider<TaskBloc>(
                  create: (context) {
                    var db = RepositoryProvider.of<Database>(context);
                    return TaskBloc(
                        database:db ,
                        taskDao: TaskDao(db),
                        apiRepository: ApiRepository(apiService: ApiService(),
                        ));}),
              BlocProvider<ApiBloc>(
                create: (context) => ApiBloc(apiRepository: ApiRepository(apiService: ApiService())),
              ),
              BlocProvider<ServiceRequestBloc>(
                create: (context) {
                  var db = RepositoryProvider.of<Database>(context);
                  var apiRepository = RepositoryProvider.of<ApiRepository>(context);
                  return ServiceRequestBloc(serviceRequestDao: ServiceRequestDao(db), auditStatusDao: AuditStatusDao(db), apiRepository: apiRepository);
                },
              ),
              BlocProvider<OpenServiceRequestBloc>(
                create: (context) {
                  return OpenServiceRequestBloc(apiService: ApiService());
                },
              ),
              BlocProvider<SelectedPartItemBloc>(
                create: (context) => SelectedPartItemBloc(),
              ),
              BlocProvider(
                create: (context) => RepairBloc(
                  repairDao: RepairDao(
                    RepositoryProvider.of<Database>(context),
                  ),
                ),
              ),
              BlocProvider<FilterCubit>(
                create: (context) {
                  var apiRepository = RepositoryProvider.of<ApiRepository>(context);
                  return FilterCubit(apiRepository: apiRepository);
                },
              ),
              BlocProvider<LocationCubit>(
                create: (context) {
                  var apiRepository = RepositoryProvider.of<ApiRepository>(context);
                  return LocationCubit(apiRepository: apiRepository);
                },
              ),
              BlocProvider<RepairDetailApiCubit>(
                create: (context) {
                  var apiRepository = RepositoryProvider.of<ApiRepository>(context);
                  return RepairDetailApiCubit(apiRepository: apiRepository);
                },
              ),
              BlocProvider<AuditBloc>(
                create: (context) {
                  var apiRepository = RepositoryProvider.of<ApiRepository>(context);
                  var db = RepositoryProvider.of<Database>(context);
                  return AuditBloc(apiRepository: apiRepository,database: db);
                },
              ),
              BlocProvider<AuditListCubit>(
                create: (context) => AuditListCubit(apiRepository: RepositoryProvider.of<ApiRepository>(context)),
                child: const AuditPage(),
              ),
              BlocProvider<AuditEquipmentCubit>(
                create: (context) => AuditEquipmentCubit(),
                child: const AuditPage(),
              ),
            ],
            child: MaterialApp(
              shortcuts: {
                LogicalKeySet(LogicalKeyboardKey.space): ActivateIntent(),
              },
              debugShowCheckedModeBanner: false,
              title: 'ALink',
              localizationsDelegates: const [
                AppLocalizations.delegate,
                GlobalMaterialLocalizations.delegate,
                GlobalCupertinoLocalizations.delegate,
                GlobalWidgetsLocalizations.delegate
              ],
              supportedLocales: L10n.all,
              theme: ThemeData(
                // appBarTheme: const AppBarTheme(
                //   systemOverlayStyle: SystemUiOverlayStyle.light,
                // ),
                fontFamily: 'helvetica',
                primarySwatch: ApplicationUtil.createMaterialColor(AppColor.primarySwatchColor),
                primaryColorLight: AppColor.primaryColorLight,
                useMaterial3: false,
              ),
              initialRoute: '/',
              onGenerateRoute: _router.generateRoute,
            ),
          ),
        ),

      ],
    );
  }

  @override
  void dispose() {
    _router.dispose();
    super.dispose();
  }
}
