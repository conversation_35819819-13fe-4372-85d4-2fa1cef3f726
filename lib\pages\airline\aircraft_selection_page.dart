import 'dart:developer';

import 'package:alink/bloc/api/api_bloc.dart';
import 'package:alink/bloc/part_items/part_items_bloc.dart';
import 'package:alink/cubit/lopa/selected_equipment_cubit.dart';
import 'package:alink/data/repository/api_repository.dart';
import 'package:alink/data/repository/api_service.dart';
import 'package:alink/pages/airline/audit/lopa_audit_page.dart';
import 'package:alink/pages/airline/model/lopa_model.dart';
import 'package:alink/pages/airline/model/tail.dart';
import 'package:alink/pages/airline/repair/lopa_repair_page.dart';
import 'package:alink/pages/airline/service_request/lopa_service_request_page.dart';
import 'package:alink/pages/airport/audit/audit_page.dart';
import 'package:alink/service_locator.dart';
import 'package:alink/util/app_color.dart';
import 'package:alink/util/app_constant.dart';
import 'package:alink/util/application_util.dart';
import 'package:alink/util/enums/app_enum.dart';
import 'package:alink/widget/part_list_dialog.dart';
import 'package:alink/widget/seperator.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import '../../../logger/logger.dart';

class AirCraftSelectionPage extends StatefulWidget {
  static const routeName = 'aircraft-selection-page';

  const AirCraftSelectionPage({Key? key}) : super(key: key);

  @override
  _AirCraftSelectionPageState createState() => _AirCraftSelectionPageState();
}

class _AirCraftSelectionPageState extends State<AirCraftSelectionPage> {
  String selectedFleet = 'Select';
  String selectedSubFleet = 'Select';
  late Map<String, List<String>> fleetMap;
  bool disabledSubFleet = true;
  bool disabledTailList = true;
  Map<String, dynamic> equipmentDetailMap = {};
  List summary=[];

  @override
  void initState() {
    fleetMap = {};
    //fleetMap = getFleetAndSubfleet();
    SelectedFleetCubit.tailList = [];
    apiBloc.add(GetAirCraftFleet());
    super.initState();
  }
  String tail = ApplicationUtil.getTailString();
  String aircraftTag =ApplicationUtil.getAirCraftString();
  String fleet = ApplicationUtil.getFleetString();
  String subfleet = ApplicationUtil.getSubFleetString();

  ApiBloc get apiBloc => BlocProvider.of<ApiBloc>(context);

  @override
  Widget build(BuildContext context) {

    Logger.i("Class Name: ${this.runtimeType.toString()} time: ${DateTime.now()}");
    return Scaffold(
      body: SafeArea(
        child: Center(
          child: Container(
            constraints: const BoxConstraints(maxWidth: 500),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                ApplicationUtil.displayNotificationWidgetIfExist(context, AuditPage.routeName),
                _aircraftSelectionAppBar(),
                Expanded(
                  child: SingleChildScrollView(
                    /*physics: BouncingScrollPhysics(
                        parent: AlwaysScrollableScrollPhysics(),
                      ),*/
                    child: BlocConsumer<ApiBloc, ApiState>(
                      listener: (context, state) {
                        if (state is FetchedAirCraftFleet) {
                          fleetMap = state.data;
                        }
                      },
                      builder: (context, state) {
                        if (state is FetchAirCraftFleetApiError) {
                          return Text(state.errorMessage);
                        } else if (fleetMap.isEmpty) {
                          return const CircularProgressIndicator();
                        }
                        return Container(
                          margin: const EdgeInsets.symmetric(vertical: 5),
                          padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Container(
                                    padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
                                    width: double.infinity,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(15),
                                      border: Border.all(color: Theme.of(context).primaryColor),
                                    ),
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        _fleetOrSubfleetDropDown(),
                                      ],
                                    ),
                                  ),
                                  const SizedBox(height: 15),
                                  !disabledTailList
                                      ? Container(
                                          child: _tailList(),
                                        )
                                      : Container()
                                ],
                              ),
                              const SizedBox(
                                height: 10,
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      floatingActionButton: ApplicationUtil.getBackButton(
        context,
        onBackPressed: () {
          Navigator.pop(context);
        },
      ),
    );
  }

  _fleetOrSubfleetDropDown() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(fleet,
          style: TextStyle(color: Theme.of(context).primaryColor, fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(
          height: 10,
        ),
        const DottedDivider(
          color: AppColor.redColor,
        ),
        const SizedBox(
          height: 10,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            _fleetDropdown(),
            const SizedBox(
              width: 50,
            ),
            !disabledSubFleet ? _subFleetDropdown() : Container(),
          ],
        ),
        const SizedBox(
          height: 10,
        ),
      ],
    );
  }

  _fleetDropdown() => Container(
        width: 140,
        decoration: BoxDecoration(color: Theme.of(context).primaryColor, borderRadius: BorderRadius.circular(10)),
        child: DropdownButton(
          underline: Container(),
          value: selectedFleet,
          hint: Center(
            child: Text(
              AppLocalizations.of(context)!.select,
              style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
            ),
          ),
          icon: const Icon(
            // Add this
            Icons.arrow_drop_down,
            color: Colors.white,
            size: 35, // Add this
          ),
          isExpanded: true,
          items: fleetMap.keys.map(
            (val) {
              return DropdownMenuItem(
                value: val,
                child: Text(val),
              );
            },
          ).toList(),
          selectedItemBuilder: (BuildContext ctx) {
            return fleetMap.keys.map<Widget>((item) {
              return DropdownMenuItem(
                  child: Container(
                    margin: const EdgeInsets.only(left: 20),
                    child: Text(item, style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold)),
                  ),
                  value: item);
            }).toList();
          },
          onChanged: (value) {
            selectedFleet = value as String;
            selectedSubFleet = AppLocalizations.of(context)!.select;
            if (fleetMap[selectedFleet]!.length == 1) {
              // Get tails By fleet name
              disabledSubFleet = true;
              if (selectedFleet != AppLocalizations.of(context)!.select) {
                disabledTailList = false;
                _getTailsByFleetName(fleetName: selectedFleet, subFleetName: null);
              } else {
                disabledTailList = true;
              }
            } else {
              if (selectedSubFleet == AppLocalizations.of(context)!.select) {
                disabledTailList = true;
              }
              disabledSubFleet = false;
            }

            setState(() {});
          },
        ),
      );

  _subFleetDropdown() => Expanded(
        child: Container(
          decoration: BoxDecoration(color: Theme.of(context).primaryColor, borderRadius: BorderRadius.circular(10)),
          child: DropdownButton(
            underline: Container(),
            value: selectedSubFleet,
            hint: Align(
              alignment: Alignment.centerLeft,
              child: Container(
                padding: const EdgeInsets.only(left: 20),
                child: Text(
                  AppLocalizations.of(context)!.select,
                  style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                ),
              ),
            ),
            icon: const Icon(
              // Add this
              Icons.arrow_drop_down,
              color: Colors.white,
              size: 35, // Add this
            ),
            isExpanded: true,
            items: fleetMap[selectedFleet]!.map(
              (val) {
                return DropdownMenuItem(
                  value: val,
                  child: Text(val),
                );
              },
            ).toList(),
            selectedItemBuilder: (BuildContext ctx) {
              return fleetMap[selectedFleet]!.map<Widget>((item) {
                return DropdownMenuItem(
                    child: Container(
                      margin: const EdgeInsets.only(left: 20),
                      child: Text(item, style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold)),
                    ),
                    value: item);
              }).toList();
            },
            onChanged: (value) {
              selectedSubFleet = value as String;

              if (selectedFleet != AppLocalizations.of(context)!.select && selectedSubFleet != AppLocalizations.of(context)!.select) {
                disabledTailList = false;
                _getTailsByFleetName(fleetName: selectedFleet, subFleetName: selectedSubFleet);
              } else {
                disabledTailList = true;
              }

              setState(() {});
            },
          ),
        ),
      );

  _aircraftSelectionAppBar() => Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Container(
            margin: const EdgeInsets.only(left: 15, top: 5),
            child: Text(aircraftTag
              ,
              style: const TextStyle(color: AppColor.blackTextColor, fontSize: AppConstant.toolbarTitleFontSize, fontWeight: FontWeight.bold),
            ),
          ),
        ],
      );

  _tailList() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('${AppLocalizations.of(context)!.select} $tail',
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
          ],
        ),
        const SizedBox(height: 10),
        BlocConsumer<ApiBloc, ApiState>(
          listener: (context, state) {
            if (state is FetchedAirCraftTails) {
              Map<String, dynamic> data = state.data[0];
              //tailList = List<Map<String, dynamic>>.from(data['Tailnos']);
              SelectedFleetCubit.tailList = List<Tail>.from(data['Tailnos'].map((e) => Tail.fromMap(e)));
              equipmentDetailMap = data;
            }
          },
          builder: (context, state) {
            if (state is FetchAirCraftTailsApiError) {
              return Center(
                child: Text(state.errorMessage),
              );
            } else if (state is FetchingAirCraftTails) {
              return const Center(
                child: CircularProgressIndicator(),
              );
            }
            if (SelectedFleetCubit.tailList.isNotEmpty) {
              return ListView.builder(
                padding: const EdgeInsets.all(0),
                shrinkWrap: true,
                physics: const ScrollPhysics(),
                itemCount: SelectedFleetCubit.tailList.length,
                itemBuilder: (context, index) {
                  Tail tailDetail = SelectedFleetCubit.tailList[index];

                  return getSingleListTile(tailDetail, equipmentDetailMap);
                },
              );
            }
            return const SizedBox();
          },
        ),
      ],
    );
  }

  getSingleListTile(Tail tailDetail, Map<String, dynamic> equipmentDetailMap) {
    return Container(
      margin: const EdgeInsets.only(bottom: 10),
      decoration: BoxDecoration(border: Border.all(color: AppColor.greyBorderColor), borderRadius: BorderRadius.circular(10)),
      child: ListTile(
        onTap: () {
          setLopaData(equipmentDetailMap, tailDetail);
          if (getIt<ServiceType>().type == ServiceRequestType.ADD) {
            Navigator.pushNamed(context, DigitalLopaPage.routeName, arguments: false);
          } else if (getIt<ServiceType>().type == ServiceRequestType.REPAIR || getIt<ServiceType>().type == ServiceRequestType.SCHEDULED) {
            Navigator.pushNamed(
              context,
              LopaRepairPage.routeName,
            ).then((value) {
              setState(() {});
            });
          } else {
            Navigator.pushNamed(
              context,
              LopaAuditPage.routeName,
            );
          }
        },
        leading: ApplicationUtil.getIcons(),
        title: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '$tail ${tailDetail.tailNo}',
              style: const TextStyle(
                color: Color(0xff101010),
              ),
            ),
          ],
        ),
        subtitle: Text(selectedFleet),
        trailing: Wrap(
          children: [
            if ((getIt<ServiceType>().type == ServiceRequestType.REPAIR && tailDetail.messageCount > 0) ||
                (getIt<ServiceType>().type == ServiceRequestType.SCHEDULED && tailDetail.messageCount > 0))
              _getMessageIcon(tailDetail),
            if ((getIt<ServiceType>().type == ServiceRequestType.REPAIR && tailDetail.serviceRequestCount > 0) || (getIt<ServiceType>().type == ServiceRequestType.SCHEDULED &&
                tailDetail.serviceRequestCount > 0)) _getPartListIconBag(tailDetail),
            const SizedBox(
              width: 10,
            ),
            SizedBox(
              height: 45,
              child: Stack(
                alignment: Alignment.centerRight,
                children: [
                  FaIcon(
                    FontAwesomeIcons.chevronRight,
                    color: Theme.of(context).primaryColor,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  bool validateFleetAndSubFleet() {
    return selectedFleet == null || selectedFleet == AppLocalizations.of(context)!.select || selectedSubFleet == AppLocalizations.of(context)!.select;
  }

  Map<String, List<String>> getFleetAndSubfleet() {
    return {
      "Select": [AppLocalizations.of(context)!.select],
      "A321T": [AppLocalizations.of(context)!.select],
      "A322T": [AppLocalizations.of(context)!.select, "A322T-1", "A322T-2"]
    };
  }

  void _getTailsByFleetName({required String fleetName, String? subFleetName}) {
    // "/fleets/A321T"
    apiBloc.add(GetAirCraftTails(fleetName: fleetName, subFleetName: subFleetName));
  }

  void setLopaData(Map<String, dynamic> equipmentDetailMap, Tail tailDetail) {
    List<SeatCategory> seatCategoryList = [];
    SelectedFleetCubit.selectedTail = tailDetail;

    SelectedFleetCubit.fleetLocation = FleetLocation(fleetName: selectedFleet, subFleetName: selectedSubFleet, tailName: tailDetail.tailNo.toString());
    try {
      int rowCount = 1;
      Map<String, dynamic> details=equipmentDetailMap["Details"];
      List<dynamic> summary=equipmentDetailMap["Summary"];

      for (var summaryObj in summary) {
        String className=summaryObj['Summary'];
        Map<String, dynamic> value = details[className];
        SeatCategory seatCategory = SeatCategory(seatDetailList: [], isClassNameSelected: false, className: className, rowCount: 0);
        //row count
        (value['Seat Number'] as Map<String, dynamic>).forEach((key, value) {
          seatCategory.rowCount = value.length;
        });

        List<SeatDetail> seatDetailLst = [];
        (value['Seat Number'] as Map<String, dynamic>).forEach((String key, value) {
          (value as Map<String, dynamic>).forEach((String key2, value2) {
            SeatDetail seatDetail = SeatDetail(partList: [], imageList: [], repairList: []);
            seatDetail.type = className;
            int seatInRow = (value2 as List<dynamic>).length;
            for (var element in (value2)) {
              seatDetail.partList.add(Part(
                  description: element['Description'],
                  part1: element['Part1'],
                  part2: element['Part2'] ?? '',
                  part3: element['Part3'] ?? '',
                  qty: element['Qty'] ?? 1,
                  equipmentLocationId: _getEquipmentLocationId(seatDetail.type, int.parse(key))));
            }
            //seat row count

            seatDetail.name = key + key2;
            seatDetail.rowCount=value.length;
            seatDetailLst.add(seatDetail);
          });
          //ROW COUNT
          rowCount++;
          seatCategory.seatDetailList = seatDetailLst;
        });
        seatCategoryList.add(seatCategory);
      }
    } catch (e) {
      Logger.e('${ApplicationUtil.getFormattedCurrentDateAndTime()}: ' + e.toString());
    }
    SelectedFleetCubit.seatCategoryList = seatCategoryList;
  }

  _getEquipmentLocationId(String className, int rowNumber) {
    FleetLocation fleetLocation = SelectedFleetCubit.fleetLocation;
    /*BUILD CLASS CODE eg FIRST CLASS TO FC*/
    String classCode = className.isNotEmpty ? className.trim().split(RegExp(' +')).map((s) => s[0].toUpperCase()).take(2).join() : '';
    String locationId = fleetLocation.fleetName;
    if (fleetLocation.subFleetName != AppLocalizations.of(context)!.select) {
      if (fleetLocation.subFleetName.contains('-')) {
        locationId += '-' + fleetLocation.subFleetName.replaceAll('-', '_');
      } else {
        locationId += '-' + fleetLocation.subFleetName;
      }
    }
    locationId += '-' + fleetLocation.tailName;
    return locationId + '-' + classCode.toUpperCase() + '-' + rowNumber.toString();
  }

  _getPartListIconBag(Tail tailDetail) {
    return InkResponse(
      onTap: () {
        SelectedFleetCubit.selectedTail = tailDetail;
        String query = 'fleet=' + selectedFleet;
        if (selectedSubFleet != 'Select') {
          query += '&subfleet=' + selectedSubFleet;
        }
        query += '&tailno=' + tailDetail.tailNo.toString();
        if (getIt<ServiceType>().type == ServiceRequestType.SCHEDULED) {
          query+='&requestType=SCHEDULED';
        }
        showDialog(
          context: context,
          builder: (context) => BlocProvider<PartItemsBloc>(
            create: (context) => PartItemsBloc(apiRepository: ApiRepository(apiService: ApiService())),
            child: PartListDialog(
              query: query,
              onClose: () {
                setState(() {});
              },
            ),
          ),
        );
      },
      child: SizedBox(
        height: 40,
        width: 45,
        child: Stack(
          alignment: Alignment.center,
          children: [
            FaIcon(
              FontAwesomeIcons.shoppingBag,
              color: Theme.of(context).primaryColor,
            ),
            Positioned(
              right: 0,
              top: 0,
              child: Container(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Theme.of(context).primaryColor,
                ),
                child: Padding(
                  padding: EdgeInsets.all(tailDetail.serviceRequestCount < 10 ? 5 : 3),
                  child: Text(
                    tailDetail.serviceRequestCount.toString(),
                    style: const TextStyle(color: Colors.white, fontSize: 12, height: 1.2),
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  _getMessageIcon(Tail tailDetail) {
    return SizedBox(
      height: 45,
      width: 45,
      child: Stack(
        alignment: Alignment.center,
        children: [
          FaIcon(
            FontAwesomeIcons.envelope,
            color: Theme.of(context).primaryColor,
          ),
          Positioned(
            right: 0,
            top: 0,
            child: Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Theme.of(context).primaryColor,
              ),
              child: Padding(
                padding: EdgeInsets.all(tailDetail.messageCount < 10 ? 5 : 3),
                child: Text(
                  tailDetail.messageCount.toString(),
                  style: const TextStyle(color: Colors.white, fontSize: 12, height: 1.2),
                ),
              ),
            ),
          )
        ],
      ),
    );
  }
}

class SubFleet {
  String name;
  String id;

  SubFleet({required this.id, required this.name});
}

class Fleet {
  String name;
  String id;

  Fleet({required this.id, required this.name});
}
