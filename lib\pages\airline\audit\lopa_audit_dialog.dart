import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'dart:ui';

import 'package:alink/pages/airline/model/lopa_audit_option.dart';
import 'package:alink/pages/airline/model/lopa_model.dart';
import 'package:alink/pages/full_image_view.dart';
import 'package:alink/service_locator.dart';
import 'package:alink/util/application_util.dart';
import 'package:alink/widget/image_picker_afs.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/painting.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:image_picker/image_picker.dart';

import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../logger/logger.dart';

class LopaAuditDialog extends StatefulWidget {
  final List<Part> seatItemList;
  final String title;
  final List<Map<String, dynamic>> imageList;
  final List<AuditScoreOption> auditScoreOptionList;

  const LopaAuditDialog({Key? key, required this.seatItemList, required this.title, required this.imageList, required this.auditScoreOptionList})
      : super(key: key);

  @override
  _LopaServiceRequestDialogState createState() => _LopaServiceRequestDialogState();
}

class _LopaServiceRequestDialogState extends State<LopaAuditDialog> {
  static const double padding = 15;
  static const double avatarRadius = 35;
  Logger logger = Logger();
  int? customerId = getIt<SharedPreferences>().getInt('customerId');

  @override
  Widget build(BuildContext context) {
    Logger.i("Class Name: ${this.runtimeType.toString()} time: ${DateTime.now()}");
    return Dialog(
      insetPadding: const EdgeInsets.symmetric(horizontal: 10),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(padding),
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      child: contentBox(context),
    );
  }

  contentBox(context) {
    return Container(
      constraints: const BoxConstraints(maxWidth: 500),
      child: Stack(
        alignment: Alignment.topRight,
        children: <Widget>[
          Container(
            //width: MediaQuery.of(context).size.width,
            padding: const EdgeInsets.only(
                left: padding,
                top: padding,
                //top: avatarRadius + padding,
                right: padding,
                bottom: 10),
            //margin: EdgeInsets.only(top: avatarRadius),
            decoration: BoxDecoration(shape: BoxShape.rectangle, color: Colors.white, borderRadius: BorderRadius.circular(15), boxShadow: const [
              BoxShadow(color: Colors.black, offset: Offset(0, 10), blurRadius: 10),
            ]),
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  const SizedBox(
                    height: 5,
                  ),
                  Text(
                    widget.title,
                    style: const TextStyle(fontSize: 22, fontWeight: FontWeight.w600),
                  ),
                  const Divider(),
                  Text(
                   customerId == 15 ? AppLocalizations.of(context)!.scoreEachPart:AppLocalizations.of(context)!.noteSelectThePartWhichNeed,
                    style: const TextStyle(
                      fontSize: 16,
                    ),
                  ),
                  const Divider(),
                  SingleChildScrollView(
                    child: Column(
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                AppLocalizations.of(context)!.partName,
                                textAlign: TextAlign.start,
                                style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                              ),
                            ),
                            Text(
                              AppLocalizations.of(context)!.score,
                              textAlign: TextAlign.center,
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                            const SizedBox(
                              width: 5,
                            ),
                          customerId == 15 ?const SizedBox(
                            width: 55,

                          ): Text(
                              AppLocalizations.of(context)!.reason,
                              textAlign: TextAlign.center,
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                        Column(
                          children: widget.seatItemList
                              .where((element) => (element.hasServiceRequest == false && element.hasNotification == false))
                              .map((seatItem) {
                            return Column(
                              children: [
                                Row(
                                  children: [
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            seatItem.description,
                                            style: const TextStyle(
                                              fontSize: 18,
                                            ),
                                          ),
                                          const SizedBox(
                                            height: 5,
                                          ),
                                          Text(
                                            seatItem.part1,
                                            style: TextStyle(color: Colors.blue[800], fontSize: 15),
                                          ),
                                          const SizedBox(
                                            height: 5,
                                          ),
                                        ],
                                      ),
                                    ),
                                    Container(
                                      width: 55,
                                      alignment: Alignment.center,
                                      child: PopupMenuButton<AuditScoreOption>(
                                        offset: const Offset(20, 30),
                                        onSelected: (AuditScoreOption value) {
                                          seatItem.selectedScore = value;
                                          if (seatItem.selectedScore!.choiceScore != 4) {
                                            seatItem.isChecked = true;
                                            seatItem.checkedCount++;
                                          } else {
                                            seatItem.isChecked = false;
                                            seatItem.checkedCount--;
                                          }
                                          setState(() {});
                                        },
                                        child: setDefaultScore(seatItem.selectedScore!, seatItem),
                                        itemBuilder: (context) {
                                          return _getScoreOptionMenu(widget.auditScoreOptionList, seatItem);
                                        },
                                      ),
                                    ),
                                    Container(
                                      width: 55,
                                      alignment: Alignment.center,
                                      child: getSubScore(seatItem),
                                    )
                                  ],
                                )
                              ],
                            );
                          }).toList(),
                        ),
                      ],
                    ),
                  ),
                  _getConcernedRaisedWidget(),
                  const SizedBox(
                    height: 10,
                  ),
                  const Divider(),
                  _getAddPhotosWidget(),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Align(
                        alignment: Alignment.bottomRight,
                        child: TextButton(
                          onPressed: () {
                            Navigator.pop(context);
                          },
                          child: Text(
                            AppLocalizations.of(context)!.continueString,
                            style: const TextStyle(fontSize: 18),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          IconButton(
            onPressed: () {
              Navigator.pop(context);
            },
            icon: const FaIcon(FontAwesomeIcons.times),
          ),
        ],
      ),
    );
  }

  _getAddPhotosWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            RichText(
              textAlign: TextAlign.start,
              text: TextSpan(style: const TextStyle(fontSize: 18), children: <TextSpan>[
                TextSpan(
                  text: AppLocalizations.of(context)!.addPhotos,
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: Theme.of(context).primaryColor),
                ),
              ]),
            ),
            TextButton(
              style: ButtonStyle(
                shape: MaterialStateProperty.all(
                  RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8.0),
                    side: BorderSide(color: Theme.of(context).primaryColor),
                  ),
                ),
                padding: MaterialStateProperty.all(
                  const EdgeInsets.symmetric(horizontal: 10),
                ),
              ),
              onPressed: () async {
                if (!kIsWeb) {
                  bool gr = await Permission.photos.request().isGranted;
                  bool de = await Permission.photos.request().isDenied;
                  var isShown0 = await Permission.photos.request();
                  var isShown00 = await Permission.storage.request();
                  bool isShown1 = await Permission.storage.shouldShowRequestRationale;
                  bool isShown2= await Permission.manageExternalStorage.shouldShowRequestRationale;
                  bool isShown3 = await Permission.mediaLibrary.shouldShowRequestRationale;
                  if (Platform.isAndroid) {
                    final androidInfo = await DeviceInfoPlugin().androidInfo;
                    if (androidInfo.version.sdkInt <= 32) {
                      /// use [Permissions.storage.status]
                      PermissionStatus photosPermission = await Permission.storage.status;
                      print(photosPermission.toString());
                    }  else {
                      /// use [Permissions.photos.status]
                      PermissionStatus photosPermission = await Permission.photos.status;
                      print(photosPermission.toString());
                    }
                  }
                  // Plugin Works only for mobiles
                  PermissionStatus photosPermission = await Permission.photos.status;
                  //If permission is ask before once
                  bool isShown = await Permission.photos.shouldShowRequestRationale;
                  var status = await Permission.photos.status;
                  if (!status.isGranted) {
                    print("Permission not granted");
                    // We didn't ask for permission yet.
                    await Permission.photos.request();
                    //showPermissionDialog=true;
                  }
                  if (/*!isShown && photosPermission.isDenied || */(photosPermission.isGranted)) {
                    AfsImagePicker.onImageButtonPressed(
                      ImageSource.gallery,
                      context: context,
                      onReturn: (editedImageBase64) {
                        widget.imageList.add({"DOCUMENT_TYPE": "image/jpeg", "DOCUMENT_BLOB": editedImageBase64});
                        setState(() {});
                      },
                    );
                  } else {
                    AfsImagePicker.askCameraPermission(context);
                  }
                } else {
                  AfsImagePicker.onImageButtonPressed(
                    ImageSource.gallery,
                    context: context,
                    onReturn: (editedImageBase64) {
                      widget.imageList.add({"DOCUMENT_TYPE": "image/jpeg", "DOCUMENT_BLOB": editedImageBase64});
                      setState(() {});
                    },
                  );
                }
              },
              child: Text(
                AppLocalizations.of(context)!.browse,
                style: const TextStyle(
                  fontSize: 16,
                ),
              ),
            )
          ],
        ),
        const SizedBox(
          height: 5,
        ),
        _getImages(),
        const SizedBox(
          height: 10,
        ),
      ],
    );
  }

  _getImages() {
    if (widget.imageList.isEmpty) {
      return _imagePlaceHolder();
    }
    return SizedBox(
      height: 75,
      child: ListView.builder(
        padding: const EdgeInsets.all(0),
        scrollDirection: Axis.horizontal,
        itemCount: widget.imageList.length,
        itemBuilder: (context, index) {
          var base64Img = widget.imageList[index]['DOCUMENT_BLOB'];
          if (index == 0) {
            return Row(
              children: [_imagePlaceHolder(), _getSingleImage(base64Img, index, widget.imageList)],
            );
          } else {
            return _getSingleImage(base64Img, index, widget.imageList);
          }
        },
      ),
    );
  }

  _imagePlaceHolder() => InkWell(
        onTap: () async {
          PermissionStatus cameraPermission = await Permission.camera.status;
          //If permission is ask before once
          bool isShown = await Permission.camera.shouldShowRequestRationale;
          if (!isShown && cameraPermission.isDenied || (cameraPermission.isGranted)) {
            AfsImagePicker.onImageButtonPressed(
              ImageSource.camera,
              context: context,
              onReturn: (editedImageBase64) {
                widget.imageList.add({"DOCUMENT_TYPE": "image/jpeg", "DOCUMENT_BLOB": editedImageBase64});
                setState(() {});
              },
            );
          } else {
            AfsImagePicker.askCameraPermission(context);
          }
        },
        child: Container(
          margin: const EdgeInsets.only(right: 10),
          decoration: BoxDecoration(border: Border.all(color: Colors.grey), borderRadius: BorderRadius.circular(15)),
          width: 70,
          height: 70,
          child: Center(
            child: FaIcon(
              FontAwesomeIcons.solidCamera,
              size: 25,
              color: Theme.of(context).primaryColor,
            ),
          ),
        ),
      );

  _getSingleImage(base64imageString, int index, List<Map<String, dynamic>> imageList) => SizedBox(
        height: 75,
        child: Stack(
          alignment: Alignment.center,
          children: [
            InkWell(
              onTap: () {
                print(index);
                Navigator.pushNamed(context, ImageViewPage.routeName, arguments: ImageWithTag(base64: base64imageString, index: index));
              },
              child: Container(
                height: 65,
                margin: const EdgeInsets.symmetric(horizontal: 5),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(5),
                ),
                child: ClipRRect(
                  borderRadius: const BorderRadius.all(Radius.circular(5)),
                  child: Hero(
                    tag: index,
                    child: Image.memory(
                      base64Decode(base64imageString),
                      fit: BoxFit.cover,
                      height: 65,
                      width: 65,
                    ),
                  ),
                ),
              ),
            ),
            Positioned(
              top: -1,
              right: 0,
              child: InkWell(
                onTap: () {
                  imageList.removeAt(index);
                  setState(() {});
                },
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(15),
                    color: Colors.white,
                  ),
                  child: const FaIcon(
                    FontAwesomeIcons.solidTimesCircle,
                    color: Colors.red,
                    size: 18,
                  ),
                ),
              ),
            ),
          ],
        ),
      );

  setDefaultScore(AuditScoreOption selectedScore, Part seatItem) {
    return Container(
      padding: const EdgeInsets.all(1),
      decoration: BoxDecoration(shape: BoxShape.circle, border: Border.all(color: Colors.grey)),
      child: _getDeafultScore(seatItem, selectedScore),
    );
  }

  getSubScore(Part seatItem) {
    return (seatItem.selectedScore != null && seatItem.selectedScore!.subChoiceList.isNotEmpty)
        ? InkWell(
            onTap: () {
              showDialog(
                barrierDismissible: false,
                context: context,
                builder: (ctx) {
                  bool showInputForSubScore = false;
                  TextEditingController descriptionController = TextEditingController();
                  if (seatItem.selectedSubScore != null && seatItem.selectedSubScore!.choiceValue == 'OTHER') {
                    showInputForSubScore = true;
                    descriptionController.text = seatItem.subScoreDescription;
                  }
                  return StatefulBuilder(
                    builder: (ctx, setState) {
                      return AlertDialog(
                        contentPadding: EdgeInsets.zero,
                        titlePadding: EdgeInsets.zero,
                        title: Stack(
                          alignment: Alignment.topCenter,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(top: 20),
                              child: Align(
                                alignment: Alignment.topCenter,
                                child: Text(
                                  AppLocalizations.of(context)!.reasonCode,
                                  style: const TextStyle(fontSize: 22, fontWeight: FontWeight.w600),
                                ),
                              ),
                            ),
                            /* Align(
                              alignment: Alignment.topRight,
                              child: IconButton(
                                  onPressed: () {
                                    Navigator.pop(ctx);
                                  },
                                  icon: Icon(Icons.close)),
                            ),*/
                          ],
                        ),
                        content: Container(
                          width: double.maxFinite,
                          constraints: const BoxConstraints(maxWidth: 600),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              ListView.separated(
                                padding: const EdgeInsets.only(top: 5),
                                shrinkWrap: true,
                                itemCount: seatItem.selectedScore!.subChoiceList.length,
                                separatorBuilder: (context, innerIndex) => const Divider(),
                                itemBuilder: (context, innerIndex) {
                                  SubChoice subChoice = seatItem.selectedScore!.subChoiceList[innerIndex];
                                  return ListTile(
                                    onTap: () {
                                      if (subChoice.choiceValue == 'OTHER') {
                                        showInputForSubScore = true;
                                        setState(() {});
                                      } else {
                                        showInputForSubScore = false;
                                        Navigator.pop(ctx);
                                      }
                                      seatItem.selectedSubScore = subChoice;
                                    },
                                    dense: true,
                                    title: Text(
                                      subChoice.choiceName,
                                      style: const TextStyle(
                                        fontSize: 18,
                                      ),
                                    ),
                                    subtitle: Text(subChoice.choiceValue),
                                    trailing: _getTrailing(subChoice, seatItem.selectedSubScore),
                                  );
                                },
                              ),
                              if (showInputForSubScore)
                                AnimatedSize(
                                  duration: const Duration(milliseconds: 200),
                                  child: Padding(
                                    padding: const EdgeInsets.only(left: 15, right: 15),
                                    child: TextFormField(
                                      controller: descriptionController,
                                      textInputAction: TextInputAction.done,
                                      minLines: 2,
                                      maxLines: 2,
                                      decoration: InputDecoration(
                                        hintText: AppLocalizations.of(context)!.enterReason,
                                        border: OutlineInputBorder(
                                          borderSide: const BorderSide(
                                            color: Colors.grey,
                                          ),
                                          borderRadius: BorderRadius.circular(
                                            5,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ),
                        actions: [
                          TextButton(
                            onPressed: () {
                              seatItem.subScoreDescription = descriptionController.text;
                              if (seatItem.selectedSubScore != null && seatItem.selectedSubScore!.choiceValue == 'OTHER') {
                                if (seatItem.subScoreDescription.trim().isEmpty) {
                                  ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text(AppLocalizations.of(context)!.enterReason)));
                                } else {
                                  Navigator.pop(ctx);
                                }
                              } else {
                                Navigator.pop(ctx);
                              }
                            },
                            child: Text(AppLocalizations.of(context)!.continueString),
                          ),
                        ],
                      );
                    },
                  );
                },
              );
            },
            child: Container(
              padding: const EdgeInsets.all(1),
              decoration: BoxDecoration(shape: BoxShape.rectangle, border: Border.all(color: Colors.grey)),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  Container(
                    width: 20,
                    height: 20,
                    color: _getColorBaseOnOption(seatItem.selectedScore!),
                  ),
                  Text(
                    AppLocalizations.of(context)!.rc,
                    style: const TextStyle(color: Colors.white, fontSize: 12, fontWeight: FontWeight.bold, height: 1.2),
                  ),
                ],
              ),
            ),
          )
        : Container();
  }

  _getScoreOptionMenu(List<AuditScoreOption> scoreList, Part seatItem) {
    return widget.auditScoreOptionList
        .map((item) => PopupMenuItem<AuditScoreOption>(
              value: item,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Stack(
                    alignment: Alignment.center,
                    children: [
                      FaIcon(
                        FontAwesomeIcons.solidCircle,
                        size: 20,
                        color: _getColorBaseOnOption(item, seatItem),
                      ),
                      Text(
                        item.choiceScore.toString(),
                        style: const TextStyle(color: Colors.white, fontSize: 15, fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                  const SizedBox(
                    width: 10,
                  ),
                  Expanded(
                    child: Text(
                      item.choiceName,
                      style: _getTextStyleForScoreMenuOptions(item.choiceScore, seatItem),
                    ),
                  ),
                ],
              ),
            ))
        .toList();
  }

  Color _getColorBaseOnOption(AuditScoreOption selectedScore, [Part? seatItem]) {
    if (seatItem != null && seatItem.hasServiceRequest == true && !seatItem.hasSafetyIssue) {
      switch (selectedScore.choiceScore) {
        case 1:
          return Colors.red;
        case 2:
          return Colors.orange;
        default:
          return Colors.grey;
      }
    }
    if (seatItem != null && seatItem.hasServiceRequest == true && seatItem.hasSafetyIssue) {
      switch (selectedScore.choiceScore) {
        case 1:
          return Colors.red;
        default:
          return Colors.grey;
      }
    }
    if (seatItem != null && seatItem.hasNotification == true) {
      switch (selectedScore.choiceScore) {
        case 1:
          return Colors.red;
        case 2:
          return Colors.orange;
        case 3:
          return Colors.yellow;
        default:
          return Colors.grey;
      }
    }
    switch (selectedScore.choiceScore) {
      case 1:
        return Colors.red;
      case 2:
        return Colors.orange;
      case 3:
        return Colors.yellow;
      default:
        return Colors.green;
    }
  }

  _getTrailing(SubChoice subChoice, SubChoice? selectedSubScore) {
    if (selectedSubScore != null) {
      if (selectedSubScore == subChoice) {
        return FaIcon(
          FontAwesomeIcons.check,
          color: Theme.of(context).primaryColor,
        );
      }
    }
  }

  _getConcernedRaisedWidget() {
    if (widget.seatItemList.where((element) => (element.hasServiceRequest == true || element.hasNotification == true)).isEmpty) {
      return Container();
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(
          height: 5,
        ),
        const Divider(
          thickness: 1,
        ),
        Text(
          AppLocalizations.of(context)!.recordedConcerns,
          style: TextStyle(
            fontSize: 18,
            color: Theme.of(context).primaryColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(
          height: 5,
        ),
        SingleChildScrollView(
          child: Column(
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      AppLocalizations.of(context)!.partName,
                      textAlign: TextAlign.start,
                      style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                    ),
                  ),
                  Text(
                    AppLocalizations.of(context)!.score,
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(
                    width: 5,
                  ),
                  customerId == 15 ?const SizedBox(
                    width: 55,
                  ):
                  Text(
                    AppLocalizations.of(context)!.reason,
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
              Column(
                children:
                    widget.seatItemList.where((element) => element.hasServiceRequest == true || element.hasNotification == true).map((seatItem) {
                  return Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              seatItem.description,
                              style: const TextStyle(
                                fontSize: 18,
                              ),
                            ),
                            const SizedBox(
                              height: 5,
                            ),
                            Text(
                              seatItem.part1,
                              style: TextStyle(color: Colors.blue[800], fontSize: 15),
                            ),
                            const SizedBox(
                              height: 5,
                            ),
                          ],
                        ),
                      ),
                      Container(
                        width: 55,
                        alignment: Alignment.center,
                        child: PopupMenuButton<AuditScoreOption>(
                          offset: const Offset(20, 30),
                          onSelected: (AuditScoreOption value) {
                            if (seatItem.hasServiceRequest && !seatItem.hasSafetyIssue) {
                              if (value.choiceScore <= 2) {
                                seatItem.selectedScore = value;
                                if(value.choiceScore == 1){
                                  seatItem.hasSafetyIssue=true;
                                  seatItem.selectedScore=value;
                                }
                                if (seatItem.selectedScore!.choiceScore != 4) {
                                  seatItem.isChecked = true;
                                  seatItem.checkedCount++;
                                } else {
                                  seatItem.isChecked = false;
                                  seatItem.checkedCount--;
                                }
                              }
                            } else if (seatItem.hasNotification) {
                              if (value.choiceScore <= 3) {
                                seatItem.selectedScore = value;
                                if (seatItem.selectedScore!.choiceScore != 4) {
                                  seatItem.isChecked = true;
                                  seatItem.checkedCount++;
                                } else {
                                  seatItem.isChecked = false;
                                  seatItem.checkedCount--;
                                }
                              }
                            }
                            else if (seatItem.hasSafetyIssue ) {
                              if (value.choiceScore <= 1) {
                                seatItem.selectedScore = value;
                                if (seatItem.selectedScore!.choiceScore != 4) {
                                  seatItem.isChecked = true;
                                  seatItem.checkedCount++;
                                } else {
                                  seatItem.isChecked = false;
                                  seatItem.checkedCount--;
                                }
                              }
                            }
                            setState(() {});
                          },
                          child: setDefaultScore(seatItem.selectedScore!, seatItem),
                          itemBuilder: (context) {
                            return _getScoreOptionMenu(widget.auditScoreOptionList, seatItem);
                          },
                        ),
                      ),
                      Container(
                        width: 55,
                        alignment: Alignment.center,
                        child: getSubScore(seatItem),
                      )
                    ],
                  );
                }).toList(),
              ),
            ],
          ),
        ),
      ],
    );
  }

  _getDeafultScore(Part seatItem, AuditScoreOption selectedScore) {
    if (seatItem.hasServiceRequest == true && seatItem.isChecked == false) {
      //SubChoice subChoice=seatItem.selectedScore!.subChoiceList[0];
      /*seatItem.selectedScore=widget.auditScoreOptionList[2];
      seatItem.isChecked = true;
      seatItem.checkedCount++;*/
      print("\n\n\n\n\t\t\tSub-Score: " + seatItem.selectedSubScore.toString() + "\n\n\n\n");
      return Stack(
        alignment: Alignment.center,
        children: [
          FaIcon(
            FontAwesomeIcons.solidCircle,
            color: _getColorBaseOnOption(seatItem.selectedScore!),
            size: 22,
          ),
          Text(
            seatItem.selectedScore!.choiceScore.toString(),
            style: const TextStyle(color: Colors.white, fontSize: 17, fontWeight: FontWeight.bold, height: 1.2),
          ),
        ],
      );
    } else if (seatItem.hasNotification == true && seatItem.isChecked == false) {
      //SubChoice subChoice=seatItem.selectedScore!.subChoiceList[0];
      /*seatItem.selectedScore=widget.auditScoreOptionList[1];
      seatItem.isChecked = true;
      seatItem.checkedCount++;*/
      return Stack(
        alignment: Alignment.center,
        children: [
          FaIcon(
            FontAwesomeIcons.solidCircle,
            color: _getColorBaseOnOption(seatItem.selectedScore!),
            size: 22,
          ),
          Text(
            seatItem.selectedScore!.choiceScore.toString(),
            style: const TextStyle(color: Colors.white, fontSize: 17, fontWeight: FontWeight.bold, height: 1.2),
          ),
        ],
      );
    } else {
      return Stack(
        alignment: Alignment.center,
        children: [
          FaIcon(
            FontAwesomeIcons.solidCircle,
            color: _getColorBaseOnOption(selectedScore),
            size: 22,
          ),
          Text(
            selectedScore.choiceScore.toString(),
            style: const TextStyle(color: Colors.white, fontSize: 17, fontWeight: FontWeight.bold, height: 1.2),
          ),
        ],
      );
    }
  }

  _getTextStyleForScoreMenuOptions(int choiceScore, Part seatItem) {
    if (seatItem.hasServiceRequest || seatItem.hasNotification) {
      if (seatItem != null && seatItem.hasServiceRequest == true && !seatItem.hasSafetyIssue) {
        switch (choiceScore) {
          case 3:
            return TextStyle(color: Colors.grey);
          case 4:
            return TextStyle(color: Colors.grey);
          default:
            return TextStyle(color: Colors.black);
        }
      }
      if (seatItem != null && seatItem.hasServiceRequest == true && seatItem.hasSafetyIssue) {
        switch (choiceScore) {
          case 2:
            return TextStyle(color: Colors.grey);
          case 3:
            return TextStyle(color: Colors.grey);
          case 4:
            return TextStyle(color: Colors.grey);
          default:
            return TextStyle(color: Colors.black);
        }
      }
      if (seatItem != null && seatItem.hasNotification == true) {
        switch (choiceScore) {
          case 4:
            return TextStyle(color: Colors.grey);
          default:
            return TextStyle(color: Colors.black);
        }
      }
      return const TextStyle(color: Colors.black);
    }
  }
}
