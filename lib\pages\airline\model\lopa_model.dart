import 'package:alink/pages/airline/model/lopa_audit_option.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

class SeatCategory {
  String className;
  List<SeatDetail> seatDetailList;
  bool isClassNameSelected;
  int rowCount;

  SeatCategory({required this.seatDetailList, required this.isClassNameSelected, required this.className, required this.rowCount});
}

class SeatDetail {
  String type;
  String name;
  List<Part> partList;
  bool seatSelected;
  bool isChecked;
  List<Map<String, dynamic>> imageList;
  List<Map<String, dynamic>> repairList;

  ///TO SHOW REPAIR PHOTOS UI IF IT IS MARK AS REPAIRED
  bool markSeatIsRepaired;

  ///FLAG TO INDICATE ITEM CONTINUE IS SELECTED OR NOT IN SR DIALOG
  bool continueForRepairOrSR;

  ///CHECK IF SEAT HAS SR ALREADY
  bool hasServiceRequest;

  int? rowCount;

  SeatDetail({
    this.type = '',
    this.name = '',
    required this.partList,
    this.seatSelected = false,
    this.isChecked = false,
    required this.imageList,
    required this.repairList,
    this.markSeatIsRepaired = false,
    this.hasServiceRequest = false,
    this.continueForRepairOrSR = false,
    this.rowCount=0,
  });
}

class Part {
  String description;
  String part1;
  String part2;
  String part3;
  int qty;
  bool isChecked;
  int checkedCount;
  bool isComplete;

  //for audit;
  AuditScoreOption? selectedScore;
  SubChoice? selectedSubScore;
  String subScoreDescription = '';
  String equipmentLocationId;

  ///CHECK IF PART HAS SR ALREADY
  bool hasServiceRequest;

  ///CHECK IF PART HAS NOTIFICATION ALREADY
  bool hasNotification;
  bool hasSafetyIssue;

  Part({
    this.description = '',
    this.part1 = '',
    this.part2 = '',
    this.part3 = '',
    this.qty = 0,
    this.isChecked = false,
    this.checkedCount = 0,
    this.isComplete = false,
    this.selectedScore,
    this.selectedSubScore,
    this.subScoreDescription = '',
    this.equipmentLocationId = '',
    this.hasServiceRequest = false,
    this.hasNotification = false,
    this.hasSafetyIssue = false,
    /*this.scoreList = const [
      ScoreLevel(
          id: 0,
          level: 4,
          description: 'No Issue Found',
          colorCode: Colors.green),
      ScoreLevel(
          id: 1,
          level: 3,
          description: 'Requires Monitoring',
          colorCode: Colors.yellow),
      ScoreLevel(
          id: 2,
          level: 2,
          description: 'Reporting an Item of concern',
          colorCode: Colors.orange),
      ScoreLevel(
          id: 3,
          level: 1,
          description: 'Reporting a missing item or a safety issue',
          colorCode: Colors.red)
    ],*/
  });

  @override
  String toString() {
    return 'Part{description: $description, part1: $part1, part2: $part2, part3: $part3, qty: $qty, isChecked: $isChecked, checkedCount: $checkedCount, isComplete: $isComplete}';
  }
}
