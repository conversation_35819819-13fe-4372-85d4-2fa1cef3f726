import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:alink/bloc/repair_db_bloc/repair_bloc.dart';
import 'package:alink/data/model/customization.dart';
import 'package:alink/data/model/drop_down_option.dart';
import 'package:alink/data/model/extension.dart';
import 'package:alink/pages/airline/model/single_equipment.dart';
import 'package:alink/pages/full_image_view.dart';
import 'package:alink/service_locator.dart';
import 'package:alink/util/app_color.dart';
import 'package:alink/util/application_util.dart';
import 'package:alink/widget/image_picker_afs.dart';
import 'package:alink/widget/seperator.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:image_picker/image_picker.dart';

import 'package:permission_handler/permission_handler.dart';

import '../../../logger/logger.dart';
import '../../../util/enums/app_enum.dart';

class LopaRepairDialogBox extends StatefulWidget {
  List<SingleEquipmentAndPart?> seatItemList;
  final String title;
  final List<SingleEquipmentAndPart?>? serviceRequestEquipmentAndPartFromServer;
  late SingleEquipmentAndPart selectedRequestEquipmentAndPartFromServer;
  SingleEquipmentAndPart? selectedSeatItemList;
  final Function(Map<String, dynamic> extensionMap, SingleEquipmentAndPart selectedRequestEquipmentAndPart) onSave;
  final Function() onSavedInDB;
  final Function(String comment, SingleEquipmentAndPart selectedRequestEquipmentAndPart) onCancelConfirmed;
  final TextEditingController commentTextEditingController;

  LopaRepairDialogBox({
    Key? key,
    required this.seatItemList,
    required this.title,
    this.serviceRequestEquipmentAndPartFromServer,
    required this.onSave,
    required this.onSavedInDB,
    required this.onCancelConfirmed,
    required this.commentTextEditingController,
  }) : super(key: key);

  @override
  _LopaRepairDialogBoxState createState() => _LopaRepairDialogBoxState();
}

class _LopaRepairDialogBoxState extends State<LopaRepairDialogBox> {
  static const double padding = 20;
  bool enabledInfo = false;
  Logger logger = Logger();

  // EXTENSION LIST FROM SERVER
  List<Extension> extensionList = [];
  Map<String, dynamic> extensionMap = {};
  bool isValueNotExist = true;
  int selectedIndex = 0;
  late PageController pageController;
  bool showPermissionDialog = false;

  @override
  void initState() {
    extensionList = getExtensionData();
    initExtensionMapData();
    pageController = PageController(
      initialPage: 0,
      keepPage: true,
    );
    super.initState();
  }

  @override
  void dispose() {
    pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    Logger.i("Class Name: ${this.runtimeType.toString()} time: ${DateTime.now()}");
    if (widget.serviceRequestEquipmentAndPartFromServer!.isNotEmpty) {
      widget.selectedRequestEquipmentAndPartFromServer = widget.serviceRequestEquipmentAndPartFromServer![selectedIndex]!;
    }
    widget.selectedSeatItemList = widget.seatItemList[selectedIndex];
    return Dialog(
      insetPadding: const EdgeInsets.symmetric(horizontal: 10),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(padding),
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      child: PageView.builder(
        controller: pageController,
        itemCount: widget.serviceRequestEquipmentAndPartFromServer!.length,
        itemBuilder: (context, index) {
          if (widget.selectedRequestEquipmentAndPartFromServer != null) {
            return contentBox(context);
          }
          return Container();
        },
        onPageChanged: (index) {
          selectedIndex = index;
          widget.selectedSeatItemList = null;
          widget.selectedSeatItemList = widget.seatItemList[selectedIndex];
          widget.selectedRequestEquipmentAndPartFromServer = widget.serviceRequestEquipmentAndPartFromServer![selectedIndex]!;
          widget.selectedRequestEquipmentAndPartFromServer.imageList = [];
          for (var element in widget.selectedRequestEquipmentAndPartFromServer.partList) {
            element.isRepaired = false;
          }
          extensionMap = {};
          extensionList = getExtensionData();
          initExtensionMapData();
          setState(() {});
        },
      ),
    );
  }

  contentBox(context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 5),
      constraints: const BoxConstraints(maxWidth: 500),
      child: Stack(
        alignment: Alignment.topRight,
        children: <Widget>[
          Container(
            //width: MediaQuery.of(context).size.width,
            padding: const EdgeInsets.only(
                left: padding,
                top: padding,
                //top: avatarRadius + padding,
                right: padding,
                bottom: 10),
            //margin: EdgeInsets.only(top: avatarRadius),
            decoration: BoxDecoration(shape: BoxShape.rectangle, color: Colors.white, borderRadius: BorderRadius.circular(15), boxShadow: const [
              BoxShadow(color: Colors.black, offset: Offset(0, 10), blurRadius: 10),
            ]),
            child: SingleChildScrollView(
              physics: const ScrollPhysics(),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  const SizedBox(
                    height: 10,
                  ),
                  _getDialogTitle(),
                  const SizedBox(
                    height: 8,
                  ),
                  _getCenterDurationWidget(
                      widget.selectedRequestEquipmentAndPartFromServer.remainingDateTime,
                      widget.selectedRequestEquipmentAndPartFromServer.status,
                      widget.selectedRequestEquipmentAndPartFromServer.requestType,
                      widget.selectedRequestEquipmentAndPartFromServer),
                  const SizedBox(
                    height: 5,
                  ),
                  enabledInfo
                      ? AnimatedSize(
                          duration: const Duration(milliseconds: 400),
                          child: Text(
                            AppLocalizations.of(context)!.noteSelectAllThePartThatAreReplaced,
                            style: const TextStyle(
                              fontSize: 18,
                            ),
                          ),
                        )
                      : Container(),
                  const SizedBox(
                    height: 10,
                  ),
                  ListView.builder(
                    physics: const NeverScrollableScrollPhysics(),
                    shrinkWrap: true,
                    itemCount: widget.selectedSeatItemList!.partList.length,
                    itemBuilder: (context, index) {
                      return InkWell(
                        onTap: () {
                          if (!widget.selectedSeatItemList!.partList[index].completed) {
                            if (!widget.selectedSeatItemList!.partList[index].isRepaired) {
                              widget.selectedSeatItemList!.partList[index].isRepaired = true;
                              widget.selectedSeatItemList!.partList[index].checkCount++;
                            } else {
                              widget.selectedSeatItemList!.partList[index].isRepaired = false;
                              widget.selectedSeatItemList!.partList[index].checkCount--;
                            }
                          }

                          setState(() {});
                          //Navigator.pop(context);
                        },
                        child: ListTile(
                          contentPadding: EdgeInsets.zero,
                          visualDensity: const VisualDensity(horizontal: 0, vertical: -4),
                          //leading: Icon(widget.seatItemList[index].icon),
                          subtitle: Text(
                            widget.selectedSeatItemList!.partList[index].partId,
                            style: TextStyle(color: Colors.blue[800], fontSize: 16),
                          ),
                          title: Text(
                            widget.selectedSeatItemList!.partList[index].description,
                            style: const TextStyle(fontSize: 18),
                          ),
                          trailing: widget.selectedSeatItemList!.partList[index].isRepaired || widget.selectedSeatItemList!.partList[index].completed
                              ? Icon(
                                  Icons.check_box,
                                  color: widget.selectedSeatItemList!.partList[index].completed ? Colors.grey : Theme.of(context).primaryColor,
                                )
                              : Icon(
                                  Icons.check_box_outline_blank,
                                  color: Theme.of(context).primaryColor,
                                ),
                        ),
                      );
                    },
                  ),
                  const Divider(),
                  widget.serviceRequestEquipmentAndPartFromServer != null
                      ? Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [_generateExtensionUI(), _getDamageEquipmentPhotos(), _getAddPhotosWidget()],
                        )
                      : Container(),
                  const Divider(
                    height: 1,
                    thickness: 2,
                  ),
                  _getActionButton(),
                ],
              ),
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              IconButton(
                onPressed: () {
                  enabledInfo = !enabledInfo;
                  setState(() {});
                },
                icon: FaIcon(
                  FontAwesomeIcons.infoCircle,
                  color: enabledInfo ? Theme.of(context).primaryColor : Colors.black,
                ),
              ),
              IconButton(
                onPressed: () {
                  for (var seatItem in widget.selectedSeatItemList!.partList) {
                    if (seatItem.isRepaired) {
                      seatItem.isRepaired = false;
                      seatItem.checkCount = seatItem.checkCount - 1;
                    }
                  }
                  widget.selectedSeatItemList!.imageList = [];
                  Navigator.of(context).pop();
                },
                icon: const FaIcon(FontAwesomeIcons.times),
              ),
            ],
          ),
        ],
      ),
    );
  }

  _getImages() {
    if (widget.serviceRequestEquipmentAndPartFromServer != null) {
      if (widget.serviceRequestEquipmentAndPartFromServer != null && widget.selectedRequestEquipmentAndPartFromServer.imageList.isEmpty) {
        return _imagePlaceHolder();
      }
      return SizedBox(
        height: 75,
        child: ListView.builder(
          padding: const EdgeInsets.all(0),
          scrollDirection: Axis.horizontal,
          itemCount: widget.selectedRequestEquipmentAndPartFromServer.imageList.length,
          itemBuilder: (context, index) {
            var base64Img = widget.selectedRequestEquipmentAndPartFromServer.imageList[index]['DOCUMENT_BLOB'];
            if (index == 0) {
              return Row(
                children: [_imagePlaceHolder(), _getSingleImage(base64Img, index, widget.selectedRequestEquipmentAndPartFromServer.imageList)],
              );
            } else {
              return _getSingleImage(base64Img, index, widget.selectedRequestEquipmentAndPartFromServer.imageList);
            }
          },
        ),
      );
    }
    return _imagePlaceHolder();
  }

  _imagePlaceHolder() => InkWell(
        onTap: () async {
          PermissionStatus cameraPermission = await Permission.camera.status;
          //If permission is ask before once
          bool isShown = await Permission.camera.shouldShowRequestRationale;
          if (!isShown && cameraPermission.isDenied || (cameraPermission.isGranted)) {
            AfsImagePicker.onImageButtonPressed(
              ImageSource.camera,
              context: context,
              onReturn: (editedImageBase64) {
                widget.selectedRequestEquipmentAndPartFromServer.imageList.add({"DOCUMENT_TYPE": "image/jpeg", "DOCUMENT_BLOB": editedImageBase64});
                setState(() {});
              },
            );
          } else {
            AfsImagePicker.askCameraPermission(context);
          }
        },
        child: Container(
          margin: const EdgeInsets.only(right: 10),
          decoration: BoxDecoration(border: Border.all(color: Colors.grey), borderRadius: BorderRadius.circular(15)),
          width: 70,
          height: 70,
          child: Center(
            child: FaIcon(
              FontAwesomeIcons.solidCamera,
              size: 25,
              color: Theme.of(context).primaryColor,
            ),
          ),
        ),
      );

  _getSingleImage(base64imageString, int index, List<Map<String, dynamic>> imageList, {bool hideDeleteIcon = false}) => SizedBox(
        height: 75,
        child: Stack(
          alignment: Alignment.center,
          children: [
            InkWell(
              onTap: () {
                Navigator.pushNamed(context, ImageViewPage.routeName, arguments: ImageWithTag(base64: base64imageString, index: index));
              },
              child: Container(
                height: 65,
                margin: const EdgeInsets.only(right: 5),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(5),
                ),
                child: ClipRRect(
                  borderRadius: const BorderRadius.all(Radius.circular(5)),
                  child: Hero(
                    tag: index,
                    child: Image.memory(
                      base64Decode(base64imageString),
                      fit: BoxFit.cover,
                      height: 65,
                      width: 65,
                    ),
                  ),
                ),
              ),
            ),
            hideDeleteIcon
                ? Container()
                : Positioned(
                    top: 0,
                    right: 0,
                    child: InkWell(
                      onTap: () {
                        imageList.removeAt(index);
                        setState(() {});
                      },
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(15),
                          color: Colors.white,
                        ),
                        child: const FaIcon(
                          FontAwesomeIcons.solidTimesCircle,
                          color: Colors.red,
                          size: 18,
                        ),
                      ),
                    ),
                  ),
          ],
        ),
      );

  _getAddPhotosWidget() {
    int completePartCount = widget.selectedSeatItemList!.partList.where((element) => element.completed == true).length;

    if (widget.seatItemList.length == completePartCount) {
      return Container();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            RichText(
              textAlign: TextAlign.start,
              text: TextSpan(style: const TextStyle(fontSize: 18), children: <TextSpan>[
                TextSpan(
                  text: AppLocalizations.of(context)!.addPhotos,
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: Theme.of(context).primaryColor),
                ),
              ]),
            ),
            TextButton(
              style: ButtonStyle(
                shape: MaterialStateProperty.all(
                  RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8.0),
                    side: BorderSide(color: Theme.of(context).primaryColor),
                  ),
                ),
                padding: MaterialStateProperty.all(
                  const EdgeInsets.symmetric(horizontal: 10),
                ),
              ),
              onPressed: () async {
                if (!kIsWeb) {
                  bool gr = await Permission.photos.request().isGranted;
                  bool de = await Permission.photos.request().isDenied;
                  var isShown0 = await Permission.photos.request();
                  var isShown00 = await Permission.storage.request();
                  bool isShown1 = await Permission.storage.shouldShowRequestRationale;
                  bool isShown2 = await Permission.manageExternalStorage.shouldShowRequestRationale;
                  bool isShown3 = await Permission.mediaLibrary.shouldShowRequestRationale;
                  if (Platform.isAndroid) {
                    final androidInfo = await DeviceInfoPlugin().androidInfo;
                    if (androidInfo.version.sdkInt <= 32) {
                      /// use [Permissions.storage.status]
                      PermissionStatus photosPermission = await Permission.storage.status;
                      print(photosPermission.toString());
                    } else {
                      /// use [Permissions.photos.status]
                      PermissionStatus photosPermission = await Permission.photos.status;
                      print(photosPermission.toString());
                    }
                  }
                  // Plugin Works only for mobiles
                  PermissionStatus photosPermission = await Permission.photos.status;
                  //If permission is ask before once
                  bool isShown = await Permission.photos.shouldShowRequestRationale;
                  var status = await Permission.photos.status;
                  if (!status.isGranted) {
                    print("Permission not granted");
                    // We didn't ask for permission yet.
                    await Permission.photos.request();
                    //showPermissionDialog=true;
                  }
                  if (/*!isShown && photosPermission.isDenied || */ (photosPermission.isGranted)) {
                    print("entered if\n${photosPermission}");
                    AfsImagePicker.onImageButtonPressed(
                      ImageSource.gallery,
                      context: context,
                      onReturn: (editedImageBase64) {
                        widget.selectedRequestEquipmentAndPartFromServer.imageList
                            .add({"DOCUMENT_TYPE": "image/jpeg", "DOCUMENT_BLOB": editedImageBase64});
                        setState(() {});
                      },
                    );
                  } else {
                    AfsImagePicker.askPhotosPermission(context);
                  }
                } else {
                  AfsImagePicker.onImageButtonPressed(
                    ImageSource.gallery,
                    context: context,
                    onReturn: (editedImageBase64) {
                      widget.selectedRequestEquipmentAndPartFromServer.imageList
                          .add({"DOCUMENT_TYPE": "image/jpeg", "DOCUMENT_BLOB": editedImageBase64});
                      setState(() {});
                    },
                  );
                }
              },
              child: Text(
                AppLocalizations.of(context)!.browse,
                style: const TextStyle(
                  fontSize: 16,
                ),
              ),
            )
          ],
        ),
        _getImages(),
        const SizedBox(
          height: 10,
        ),
      ],
    );
  }

  _getDamageEquipmentPhotos() {
    if (widget.selectedRequestEquipmentAndPartFromServer != null) {
      if (widget.selectedRequestEquipmentAndPartFromServer.serverImageList.isNotEmpty) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(
              height: 10,
            ),
            RichText(
              textAlign: TextAlign.start,
              text: TextSpan(style: const TextStyle(fontSize: 18), children: <TextSpan>[
                TextSpan(
                  text: AppLocalizations.of(context)!.damagePhotos,
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: Theme.of(context).primaryColor),
                ),
              ]),
            ),
            const SizedBox(
              height: 5,
            ),
            SizedBox(
              height: 75,
              child: ListView.builder(
                padding: const EdgeInsets.all(0),
                scrollDirection: Axis.horizontal,
                itemCount: widget.selectedRequestEquipmentAndPartFromServer.serverImageList.length,
                itemBuilder: (context, index) {
                  var base64Img = widget.selectedRequestEquipmentAndPartFromServer.serverImageList[index]['DOCUMENT_BLOB'];
                  return _getSingleImage(base64Img, index, widget.selectedRequestEquipmentAndPartFromServer.imageList, hideDeleteIcon: true);
                },
              ),
            ),
            const Divider(),
          ],
        );
      }
    }
    return Container();
  }

  _getCenterDurationWidget(
      int? remainingRequestTime, String? status, String requestType, SingleEquipmentAndPart selectedRequestEquipmentAndPartFromServer) {
    if (remainingRequestTime != null) {
      if (requestType == "NOTIFICATION") {
        return Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              width: 140,
              padding: const EdgeInsets.symmetric(
                vertical: 5,
              ),
              decoration: BoxDecoration(
                color: AppColor.orangeColor,
                borderRadius: BorderRadius.circular(5),
              ),
              child: Text(
                status!,
                textAlign: TextAlign.center,
                style: const TextStyle(color: Colors.white, fontSize: 11, fontWeight: FontWeight.bold),
              ),
            ),
            const SizedBox(
              width: 10,
            ),
            Container(
              width: 140,
              padding: const EdgeInsets.symmetric(vertical: 5),
              decoration: BoxDecoration(
                color: AppColor.orangeColor,
                borderRadius: BorderRadius.circular(5),
              ),
              child: Text(
                requestType,
                textAlign: TextAlign.center,
                overflow: TextOverflow.ellipsis,
                style: const TextStyle(color: Colors.white, fontSize: 11, fontWeight: FontWeight.bold),
              ),
            ),
          ],
        );
      } else {
        if (remainingRequestTime > 0) {
          return Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                width: 140,
                padding: const EdgeInsets.symmetric(
                  vertical: 5,
                ),
                decoration: BoxDecoration(
                  color: AppColor.orangeColor,
                  borderRadius: BorderRadius.circular(5),
                ),
                child: Text(
                  status!,
                  textAlign: TextAlign.center,
                  style: const TextStyle(color: Colors.white, fontSize: 11, fontWeight: FontWeight.bold),
                ),
              ),
              const SizedBox(
                width: 10,
              ),
              Container(
                width: 130,
                padding: const EdgeInsets.symmetric(vertical: 5),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor,
                  borderRadius: BorderRadius.circular(5),
                ),
                child: getIt<ServiceType>().type == ServiceRequestType.SCHEDULED
                    ? Text(
                        ApplicationUtil.getHourAndMinuteOrDayFromMinute(remainingRequestTime) + ' ${AppLocalizations.of(context)!.remaining}',
                        textAlign: TextAlign.center,
                        overflow: TextOverflow.ellipsis,
                        style: const TextStyle(color: Colors.white, fontSize: 11, fontWeight: FontWeight.bold),
                      )
                    : Text(
                        ApplicationUtil.getHourAndMinuteFromMinute(remainingRequestTime) + ' ${AppLocalizations.of(context)!.remaining}',
                        textAlign: TextAlign.center,
                        overflow: TextOverflow.ellipsis,
                        style: const TextStyle(color: Colors.white, fontSize: 11, fontWeight: FontWeight.bold),
                      ),
              ),
              const SizedBox(
                width: 5,
              ),
              selectedRequestEquipmentAndPartFromServer.safetyIssue != null
                  ? Expanded(
                      child: Image.asset(
                        width: 20,
                        height: 20,
                        "assets/images/hazard.png",
                      ),
                    )
                  : Container(),
            ],
          );
        } else {
          return Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                width: 140,
                padding: const EdgeInsets.symmetric(vertical: 5),
                decoration: BoxDecoration(
                  color: AppColor.redColor,
                  borderRadius: BorderRadius.circular(5),
                ),
                child: Text(
                  status!,
                  textAlign: TextAlign.center,
                  style: const TextStyle(color: Colors.white, fontSize: 11, fontWeight: FontWeight.bold),
                ),
              ),
              const SizedBox(
                width: 10,
              ),
              Container(
                width: 140,
                padding: const EdgeInsets.symmetric(vertical: 5),
                decoration: BoxDecoration(
                  color: AppColor.redColor,
                  borderRadius: BorderRadius.circular(5),
                ),
                child: getIt<ServiceType>().type == ServiceRequestType.SCHEDULED
                    ? Text(
                        ApplicationUtil.getHourAndMinuteOrDayFromMinute(remainingRequestTime) + ' ${AppLocalizations.of(context)!.overdue}',
                        textAlign: TextAlign.center,
                        style: const TextStyle(color: Colors.white, fontSize: 11, fontWeight: FontWeight.bold),
                      )
                    : Text(
                        ApplicationUtil.getHourAndMinuteFromMinute(remainingRequestTime) + ' ${AppLocalizations.of(context)!.overdue}',
                        textAlign: TextAlign.center,
                        style: const TextStyle(color: Colors.white, fontSize: 11, fontWeight: FontWeight.bold),
                      ),
              ),
              const SizedBox(
                width: 5,
              ),
              selectedRequestEquipmentAndPartFromServer.safetyIssue != null
                  ? Expanded(
                      child: Image.asset(
                        width: 20,
                        height: 20,
                        "assets/images/hazard.png",
                      ),
                    )
                  : Container()
            ],
          );
        }
      }
    }
    return Container();
  }

  _getActionButton() {
    return BlocConsumer<RepairBloc, RepairState>(
      listener: (context, state) {
        if (state is RepairError) {
          ApplicationUtil.showSnackBar(context: context, message: state.error);
        }
        if (state is RepairSaved) {
          widget.onSavedInDB();
          Navigator.of(context).pop(true);
        }
      },
      builder: (ctx, state) {
        if (state is RepairRequestSaving) {
          return const Align(
            alignment: Alignment.bottomCenter,
            child: CircularProgressIndicator(),
          );
        }

        return Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            IconButton(
              onPressed: () {
                widget.commentTextEditingController.clear();
                ApplicationUtil.showConfirmDialogWithInput(
                  context,
                  title: AppLocalizations.of(context)!.cancelServiceRequest,
                  description: AppLocalizations.of(context)!.doYouWantToCancelServiceRequest,
                  continueString: AppLocalizations.of(context)!.yesCancelRequest,
                  continueColor: AppColor.redColor,
                  cancelString: AppLocalizations.of(context)!.keepRequest,
                  textEditingController: widget.commentTextEditingController,
                  onContinue: () {
                    widget.onCancelConfirmed(widget.commentTextEditingController.text, widget.selectedRequestEquipmentAndPartFromServer);
                  },
                );
              },
              icon: const FaIcon(
                FontAwesomeIcons.solidBan,
                color: AppColor.orangeColor,
                size: 22,
              ),
            ),
            _getNextAndPrevButton(),
            Align(
              alignment: Alignment.bottomRight,
              child: TextButton(
                onPressed: () {
                  if (widget.selectedSeatItemList!.partList.where((element) => element.isRepaired == true).isNotEmpty) {
                    dynamic response = validateExtensionMap(extensionMap);
                    if (response is bool) {
                      return;
                    }
                    widget.onSave(extensionMap, widget.selectedRequestEquipmentAndPartFromServer);
                  } else {
                    ApplicationUtil.showSnackBar(context: context, message: AppLocalizations.of(context)!.atLeastRepairAPartToContinue);
                  }
                },
                child: Text(
                  AppLocalizations.of(context)!.submit,
                  style: const TextStyle(fontSize: 18),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  _generateExtensionUI() {
    Widget widget = ListView.builder(
      padding: const EdgeInsets.all(0),
      shrinkWrap: true,
      physics: const ScrollPhysics(),
      itemCount: extensionList.length,
      itemBuilder: (context, index) {
        Extension extension = extensionList[index];
        return _buildWidgetFromExtension(extension.title!, extension.extensionContent!);
      },
    );
    //isValueNotExist = false;
    return widget;
  }

  List<Extension> getExtensionData() {
    List<Extension> extensionList = [];
    Map<String, dynamic>? data = getIt<Customization>().customizationData;
    if (data != null) {
      if (data.containsKey("EXTENSIONS")) {
        if (data['EXTENSIONS'].containsKey('REPAIR')) {
          Map<String, dynamic> sectionMap = data['EXTENSIONS']['REPAIR'];
          for (var key in sectionMap.keys) {
            Extension extension = Extension();
            extension.title = key;
            List<dynamic> sectionDetailList = sectionMap[key];
            List<ExtensionContent> extensionContentList = [];
            for (var mapData in sectionDetailList) {
              extensionContentList.add(ExtensionContent.fromMap(mapData));
            }
            extension.extensionContent = extensionContentList;
            extensionList.add(extension);
          }
        }
      }
    }
    return extensionList;
  }

  /*-----------------------------------EXTENSION FIELD DATA------------------------------------------*/

  Widget _buildWidgetFromExtension(String title, List<ExtensionContent> extensionContent) {
    return Container(
      padding: const EdgeInsets.all(10),
      margin: const EdgeInsets.symmetric(vertical: 2),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(5),
        border: Border.all(color: AppColor.greyBorderColor, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _getSectionHeader(title),
          _getSectionBody(extensionContent, title),
        ],
      ),
    );
  }

  _getSectionHeader(String title) => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(color: Theme.of(context).primaryColor, fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(
            height: 10,
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 5),
            child: const DottedDivider(
              color: AppColor.redColor,
            ),
          ),
        ],
      );

  _getSectionBody(List<ExtensionContent> extensionContentList, String title) {
    if (extensionMap[title] == null) {
      extensionMap[title] = [];
    }
    return ListView.builder(
      shrinkWrap: true,
      physics: const ScrollPhysics(),
      itemCount: extensionContentList.length,
      itemBuilder: (context, index) {
        ExtensionContent extensionContent = extensionContentList[index];

        if (extensionContent.fieldControl == "TEXT") {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 5,
              ),
              _getLabelBaseOnMandatory(extensionContent),
              Container(
                margin: const EdgeInsets.only(
                  top: 5,
                ),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(color: AppColor.greyBorderColor),
                ),
                child: TextField(
                  style: const TextStyle(height: 1.2),
                  decoration: const InputDecoration(
                    contentPadding: EdgeInsets.symmetric(horizontal: 5),
                    border: InputBorder.none,
                    //hintText: extensionContent.fieldName,
                  ),
                  onChanged: (value) {
                    extensionMap[title][index] = {extensionContent.fieldTechName: value};
                    print(extensionMap.toString());
                  },
                ),
              ),
              const SizedBox(
                height: 10,
              ),
            ],
          );
        } else if (extensionContent.fieldControl == "TEXTAREA") {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 5,
              ),
              _getLabelBaseOnMandatory(extensionContent),
              Container(
                margin: const EdgeInsets.symmetric(
                  vertical: 5,
                ),
                padding: const EdgeInsets.symmetric(vertical: 5),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(color: AppColor.greyBorderColor),
                ),
                child: TextField(
                  minLines: 3,
                  maxLines: 5,
                  style: const TextStyle(height: 1.2),
                  decoration: const InputDecoration(
                    contentPadding: EdgeInsets.symmetric(horizontal: 5),
                    border: InputBorder.none,
                    //  hintText: extensionContent.fieldName,
                  ),
                  onChanged: (value) {
                    extensionMap[title][index] = {extensionContent.fieldTechName: value};
                    print(extensionMap.toString());
                  },
                ),
              ),
              const SizedBox(
                height: 10,
              ),
            ],
          );
        } else if (extensionContent.fieldControl == "NUMBER") {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 5,
              ),
              _getLabelBaseOnMandatory(extensionContent),
              Container(
                margin: const EdgeInsets.symmetric(
                  vertical: 5,
                ),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(color: AppColor.greyBorderColor),
                ),
                child: TextField(
                  style: const TextStyle(height: 1.2),
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    contentPadding: const EdgeInsets.symmetric(horizontal: 5),
                    border: InputBorder.none,
                    hintText: extensionContent.fieldName,
                  ),
                  onChanged: (value) {
                    extensionMap[title][index] = {extensionContent.fieldTechName: value};
                    print(extensionMap.toString());
                  },
                ),
              ),
            ],
          );
        } else if (extensionContent.fieldControl == "TOGGLE") {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 5,
              ),
              _getLabelBaseOnMandatory(extensionContent),
              Container(
                  margin: const EdgeInsets.symmetric(
                    vertical: 5,
                  ),
                  padding: const EdgeInsets.symmetric(
                    vertical: 10,
                  ),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(color: AppColor.greyBorderColor),
                  ),
                  child: Row(
                    children: [
                      Switch(
                        value: _getSwitchValue(extensionMap, title, index, extensionContent),
                        onChanged: (bool value) {
                          extensionMap[title][index] = {extensionContent.fieldTechName: value.toString()};
                          isValueNotExist = false;
                          setState(() {});
                        },
                      ),
                    ],
                  )),
            ],
          );
        } else if (extensionContent.fieldControl == "CHOICE") {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 5,
              ),
              _getLabelBaseOnMandatory(extensionContent),
              dropDownOption(extensionContent, index, title)
            ],
          );
        } else {
          return Container();
        }
      },
    );
  }

  _getLabelBaseOnMandatory(ExtensionContent extensionContent) {
    return RichText(
      textAlign: TextAlign.start,
      text: TextSpan(style: const TextStyle(fontSize: 18), children: <TextSpan>[
        extensionContent.fieldMandatory == "Y"
            ? const TextSpan(
                text: "* ",
                style: TextStyle(color: AppColor.redColor, fontWeight: FontWeight.bold),
              )
            : const TextSpan(text: ''),
        TextSpan(
          text: extensionContent.fieldName,
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: AppColor.blackTextColor),
        ),
      ]),
    );
  }

  _getSwitchValue(Map<String, dynamic> extensionMap, String title, int index, ExtensionContent extensionContent) {
    if (extensionMap[title][index][extensionContent.fieldTechName] == null || extensionMap[title][index][extensionContent.fieldTechName].isEmpty) {
      extensionMap[title][index][extensionContent.fieldTechName] = 'false';
    }
    return extensionMap[title][index][extensionContent.fieldTechName] == 'true';
  }

  dropDownOption(
    ExtensionContent extensionContent,
    int index,
    String title,
  ) {
    List<DropDownChoice> dropDownList = [];
    Map<String, dynamic>? data = getIt<Customization>().customizationData;
    if (data != null) {
      if (data.containsKey("CHOICE")) {
        if (data['CHOICE'].containsKey(extensionContent.fieldChoiceType)) {
          List<dynamic> dropDownOptionList = data['CHOICE'][extensionContent.fieldChoiceType];
          dropDownList.add(DropDownChoice(choiceName: 'Select', choiceValue: 'select'));
          for (var choiceMap in dropDownOptionList) {
            dropDownList.add(DropDownChoice.fromMap(choiceMap));
          }
        }
      }
    }

    return Container(
      margin: const EdgeInsets.only(top: 5),
      height: 38,
      decoration: BoxDecoration(color: Theme.of(context).primaryColor, borderRadius: BorderRadius.circular(5)),
      child: DropdownButton(
          underline: Container(),
          value: _getExtensionDropdownSelectedValue(dropDownList, title, index, extensionContent),
          hint: Center(
            child: Text(
              _getExtensionDropdownSelectedName(dropDownList, title, index, extensionContent),
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
          icon: const Icon(
            // Add this
            Icons.arrow_drop_down,
            color: Colors.white,
            size: 40, // Add this
          ),
          isExpanded: true,
          items: dropDownList.map(
            (val) {
              return DropdownMenuItem(
                value: val.choiceValue,
                child: Text(
                  val.choiceName!,
                  style: const TextStyle(color: Colors.black),
                ),
              );
            },
          ).toList(),
          selectedItemBuilder: (BuildContext ctxt) {
            return dropDownList.map<Widget>((DropDownChoice item) {
              return DropdownMenuItem(
                  child: Container(
                    margin: const EdgeInsets.only(left: 20),
                    child: Text("${item.choiceName}", style: const TextStyle(fontWeight: FontWeight.bold, color: Colors.white)),
                  ),
                  value: item);
            }).toList();
          },
          onChanged: (value) {
            extensionMap[title][index][extensionContent.fieldTechName] = value;
            isValueNotExist = false;
            setState(() {});
          }),
    );
  }

  _getExtensionDropdownSelectedValue(List<DropDownChoice> dropDownList, String title, int index, ExtensionContent extensionContent) {
    String selected = extensionMap[title][index][extensionContent.fieldTechName];
    if (selected.isNotEmpty) return selected;
    return dropDownList[0].choiceValue;
  }

  String _getExtensionDropdownSelectedName(List<DropDownChoice> dropDownList, String title, int index, ExtensionContent extensionContent) {
    var selected = extensionMap[title][index][extensionContent.fieldTechName];
    print(selected);

    for (DropDownChoice choice in dropDownList) {
      if (choice.choiceValue == selected) {
        print('element.choiceName' + choice.choiceName!);
        return choice.choiceName!;
      }
    }
    return dropDownList[0].choiceName!;
  }

  validateExtensionMap(Map<String, dynamic> extensionMap) {
    List<String> errorList = [];
    List<Map<String?, dynamic>> returnedMap = [];
    extensionMap.forEach((key, value) {
      List<dynamic> innerMap = extensionMap[key];
      for (var element in innerMap) {
        Map<String?, dynamic> map = element;
        if (map['FIELD_MANDATORY'] == 'Y') {
          if (map[map['FIELD_TECH_NAME']].isEmpty || map[map['FIELD_TECH_NAME']] == 'select') {
            errorList.add(map['FIELD_NAME']);
          }
        } else {
          returnedMap.add(element);
        }
      }
    });
    if (errorList.isNotEmpty) {
      ApplicationUtil.showSnackBar(context: context, message: '${errorList[0]} is required');
      return true;
    }
    return returnedMap;
  }

  void initExtensionMapData() {
    for (var extension in extensionList) {
      for (var extensionContent in extension.extensionContent!) {
        if (extensionMap[extension.title] == null) {
          extensionMap[extension.title!] = [];
        }
        extensionMap[extension.title].add({
          extensionContent.fieldTechName: '',
          'FIELD_MANDATORY': extensionContent.fieldMandatory,
          'FIELD_NAME': extensionContent.fieldName,
          'FIELD_TECH_NAME': extensionContent.fieldTechName,
        });
      }
    }
  }

  Widget _getDialogTitle() {
    return Text(widget.title, style: const TextStyle(fontSize: 22, fontWeight: FontWeight.w600));
  }

  _getNextAndPrevButton() {
    if (widget.serviceRequestEquipmentAndPartFromServer!.length > 1) {
      if (selectedIndex == 0) {
        return _getNextButton();
      } else if (selectedIndex == widget.serviceRequestEquipmentAndPartFromServer!.length - 1) {
        return _getPrevButton();
      }
      return Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _getPrevButton(),
          const SizedBox(
            width: 20,
          ),
          _getNextButton(),
        ],
      );
    }
    return Container();
  }

  _getNextButton() {
    return InkResponse(
      onTap: () {
        pageController.animateToPage(
          ++selectedIndex,
          duration: const Duration(milliseconds: 300),
          curve: Curves.linear,
        );
      },
      child: Center(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              AppLocalizations.of(context)!.next,
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600, color: Theme.of(context).primaryColor, height: 1.2),
            ),
            const SizedBox(
              width: 7,
            ),
            FaIcon(
              FontAwesomeIcons.angleDoubleRight,
              color: Theme.of(context).primaryColor,
            ),
          ],
        ),
      ),
    );
  }

  _getPrevButton() {
    return InkWell(
      onTap: () {
        pageController.animateToPage(
          --selectedIndex,
          duration: const Duration(milliseconds: 300),
          curve: Curves.linear,
        );
      },
      child: Center(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            FaIcon(
              //FontAwesomeIcons.arrowCircleLeft,
              FontAwesomeIcons.angleDoubleLeft,
              color: Theme.of(context).primaryColor,
            ),
            const SizedBox(
              width: 8,
            ),
            Text(
              AppLocalizations.of(context)!.prev,
              style: TextStyle(
                fontSize: 16,
                height: 1.2,
                fontWeight: FontWeight.w600,
                color: Theme.of(context).primaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
