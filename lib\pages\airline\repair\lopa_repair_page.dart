import 'dart:convert';
import 'dart:developer';

import 'package:alink/bloc/api/api_bloc.dart';
import 'package:alink/bloc/part_items/part_items_bloc.dart';
import 'package:alink/bloc/repair_db_bloc/repair_bloc.dart';
import 'package:alink/bloc/repair_list_api/repair_list_api_bloc.dart';
import 'package:alink/bloc/service/service_request_bloc.dart';
import 'package:alink/cubit/lopa/selected_equipment_cubit.dart';
import 'package:alink/cubit/message/message_cubit.dart';
import 'package:alink/data/model/repair_service_request.dart';
import 'package:alink/data/repository/api_repository.dart';
import 'package:alink/data/repository/api_service.dart';
import 'package:alink/database/database.dart';
import 'package:alink/pages/airline/model/lopa_model.dart';
import 'package:alink/pages/airline/model/single_equipment.dart';
import 'package:alink/pages/airline/repair/lopa_repair_dialog.dart';
import 'package:alink/pages/airline/service_request/lopa_service_request_page.dart';
import 'package:alink/service_locator.dart';
import 'package:alink/util/app_color.dart';
import 'package:alink/util/application_util.dart';
import 'package:alink/util/enums/app_enum.dart';
import 'package:alink/widget/message_dialog.dart';
import 'package:alink/widget/part_list_dialog.dart';
import 'package:drift/drift.dart' as moor;
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:intl/intl.dart';
import 'package:location/location.dart' as gps;
import 'package:shared_preferences/shared_preferences.dart';

import '../../../logger/logger.dart';

class LopaRepairPage extends StatefulWidget {
  static const routeName = 'digital-lopa-repair';

  const LopaRepairPage({Key? key}) : super(key: key);

  @override
  _LopaRepairPageState createState() => _LopaRepairPageState();
}

class _LopaRepairPageState extends State<LopaRepairPage> {
  gps.LocationData? locationData;
  bool showAllServiceRequestUIOption = true;
  List<RepairServiceRequest> serviceRequestList = [];
  RepairServiceRequest selectedServiceRequest = defaultRepairServiceRequest();
  SingleEquipmentAndPart? selectedSingleEquipmentAndPart;
  List<SingleEquipmentAndPart> singleEquipmentAndPartList = [];
  late TextEditingController cancelCommentTextEditingController;
  late ScrollController _scrollController;
  bool isTimedService = false;
  String timedServiceParams = "";

  @override
  void initState() {
    _initLocation();
    if (getIt<ServiceType>().type == ServiceRequestType.SCHEDULED) {
      timedServiceParams = "&requestType=SCHEDULED";
    } else {
      timedServiceParams = "&requestType=NOTIFICATION,SERVICE_REQUEST";
    }
    cancelCommentTextEditingController = TextEditingController();
    _scrollController = ScrollController();
    repairListBloc
        .add(FetchLopaRepairServiceRequestList(query: '&location=${_getLocationId()}$timedServiceParams&status=OPEN,PAUSE&isRestricted=true'));
    super.initState();
  }
  int? customerId = getIt<SharedPreferences>().getInt('customerId');

  @override
  void dispose() {
    cancelCommentTextEditingController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  RepairListApiBloc get repairListBloc => BlocProvider.of<RepairListApiBloc>(context);

  RepairBloc get repairBloc => BlocProvider.of<RepairBloc>(context);

  ServiceRequestBloc get serviceRequestBloc => BlocProvider.of<ServiceRequestBloc>(context);

  ApiBloc get apiBloc => BlocProvider.of<ApiBloc>(context);

  PartItemsBloc get partItemsBloc => BlocProvider.of<PartItemsBloc>(context);

  @override
  Widget build(BuildContext context) {
    Logger.i("Class Name: ${this.runtimeType.toString()} time: ${DateTime.now()}");
    return WillPopScope(
      child: BlocListener<ApiBloc, ApiState>(
        listener: (context, state) {
          if (state is CancelledServiceRequest) {
            ApplicationUtil.showSnackBar(context: context, message: AppLocalizations.of(context)!.serviceRequestCancelled);
            selectedServiceRequest = defaultRepairServiceRequest();
            selectedSingleEquipmentAndPart = null;
            singleEquipmentAndPartList = [];
            repairListBloc.add(FetchLopaRepairServiceRequestList(
                query:
                    '&location=${_getLocationId()}$timedServiceParams&status=OPEN,PAUSE&isRestricted=true'));
          } else if (state is CancelServiceRequestError) {
            ApplicationUtil.showSnackBar(context: context, message: state.errorMessage);
          }
        },
        child: Scaffold(
          body: SafeArea(
            child: Center(
              child: BlocConsumer<ServiceRequestBloc, ServiceRequestState>(
                listener: (context, state) {
                  if (state is ServiceRequestNotificationSent) {
                    repairListBloc.add(FetchLopaRepairServiceRequestList(
                        query:
                            '&location=${_getLocationId()}$timedServiceParams&status=OPEN,PAUSE&isRestricted=true'));
                  }
                },
                builder: (context, state) {
                  return Container(
                    constraints: const BoxConstraints(maxWidth: 500),
                    child: _getBody(),
                  );
                },
              ),
            ),
          ),
          floatingActionButton: FloatingActionButton(
            onPressed: () {
              _showConfirmationDialog(context);
              //Navigator.pop(context);
            },
            child: Container(margin: const EdgeInsets.only(right: 5), child: const FaIcon(FontAwesomeIcons.chevronLeft)),
          ),
        ),
      ),
      onWillPop: () async {
        _showConfirmationDialog(context);
        return false;
      },
    );
  }

  _getLopaLayoutFromJson() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
      decoration: BoxDecoration(
          border: Border.all(color: Colors.black),
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(150),
            topRight: Radius.circular(150),
          ),
          color: Colors.white),
      child: Container(
        padding: const EdgeInsets.all(5),
        margin: const EdgeInsets.only(top: 85),
        child: Column(
          mainAxisSize: MainAxisSize.max,
          children: [
            _generateSeatsLayout(),
            //_sheetCurtain(),
            const SizedBox(
              height: 20,
            ),
          ],
        ),
      ),
    );
  }

  _generateSeatsLayout() {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: SelectedFleetCubit.seatCategoryList.length,
      itemBuilder: (context, index) {
        return Container(
          margin: const EdgeInsets.symmetric(vertical: 10),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.black),
            borderRadius: const BorderRadius.only(topLeft: Radius.circular(10), topRight: Radius.circular(10)),
          ),
          child: Column(
            children: [
              Container(
                padding: const EdgeInsets.all(5),
                decoration: BoxDecoration(border: Border.all(color: Colors.black)),
                child: Text(
                  SelectedFleetCubit.seatCategoryList[index].className,
                  style: const TextStyle(
                    fontSize: 18,
                  ),
                ),
              ),
              _generateRowAndColumnForSeats(
                SelectedFleetCubit.seatCategoryList[index].seatDetailList,
                SelectedFleetCubit.seatCategoryList[index].rowCount,
              ),
            ],
          ),
        );
      },
    );
  }

  _singleSeat(
    SeatDetail seatDetail,
    List<SeatDetail> seatDetailList,
    List<SingleEquipmentAndPart?> serviceRequestEquipmentAndPartFromServer,
  ) {
    bool isSelected = seatDetail.partList.where((element) => element.isChecked == true).toList().isNotEmpty;
    return InkWell(
      onTap: () {
        for (var element in SelectedFleetCubit.seatCategoryList) {
          for (var element in element.seatDetailList) {
            element.seatSelected = false;
          }
        }
        seatDetail.seatSelected = true;
        // CHECK EQUIPMENT HAS SR OR NOT
        if (serviceRequestEquipmentAndPartFromServer.isEmpty) {
          bool? afsUser = getIt<SharedPreferences>().getBool('isAfs');
          if (afsUser != null && afsUser == true) {
            _showServiceRequestDialogWarning(context, seatDetail);
          }
        } else {
          if (selectedServiceRequest.requestId > -1 && serviceRequestEquipmentAndPartFromServer.first!.isSRPresentInDb) {
            ApplicationUtil.showSnackBar(context: context, message: AppLocalizations.of(context)!.repairInProgress);
            return;
          } else {
            if (serviceRequestEquipmentAndPartFromServer.where((element) => element!.isSRPresentInDb == true).length ==
                serviceRequestEquipmentAndPartFromServer.length) {
              ApplicationUtil.showSnackBar(context: context, message: AppLocalizations.of(context)!.repairInProgress);
              return;
            }
          }
          showDialog(
              context: context,
              barrierDismissible: false,
              builder: (BuildContext context) {
                return LopaRepairDialogBox(
                  title: "${AppLocalizations.of(context)!.repairSeat}: ${seatDetail.name}",
                  seatItemList: _getPartList(
                    seatDetail,
                    serviceRequestEquipmentAndPartFromServer,
                  ),
                  serviceRequestEquipmentAndPartFromServer: _getServiceRequestEquipmentAndPartBaseOnSR(
                    serviceRequestEquipmentAndPartFromServer,
                  ),
                  onSave: (extensionMap, selectedRequestEquipmentAndPart) async {
                    if (selectedRequestEquipmentAndPart.requestType == "NOTIFICATION") {
                      final response = await ApiService().convertToServiceRequest(requestId: selectedRequestEquipmentAndPart.serviceRequestId);
                      if (response.statusCode == 204 || response.statusCode == 200) {
                        var refRequestId = json.decode(response.body)[0]["REQUEST_ID"];
                        selectedRequestEquipmentAndPart.serviceRequestId = refRequestId;
                        selectedRequestEquipmentAndPart.requestType = "SERVICE_REQUEST";
                        selectedSingleEquipmentAndPart = selectedRequestEquipmentAndPart;
                        saveRepairServiceRequestInDatabase(extensionMap);
                        print(
                            "Successfully converted notification to Service request. request ID: ${selectedRequestEquipmentAndPart.serviceRequestId}");
                        print("Successfully fetched converted SR to save");
                        print("Current Equipment Service request is with equipmentId:${selectedRequestEquipmentAndPart.equipmentName} and requestId: "
                            "${selectedRequestEquipmentAndPart.serviceRequestId} and converted "
                            "requestId: $refRequestId");
                      } else {
                        print("Unable to convert notification to Service request. request ID: ${selectedRequestEquipmentAndPart.serviceRequestId}");
                      }
                    } else {
                      selectedSingleEquipmentAndPart = selectedRequestEquipmentAndPart;
                      saveRepairServiceRequestInDatabase(extensionMap);
                    }
                    /*repairListBloc.add(FetchLopaRepairServiceRequestList(
                        query: '&location=${_getLocationId()}$timedServiceParams&status=OPEN,PAUSE&isRestricted=true'));*/
                  },
                  onSavedInDB: () {
                    selectedServiceRequest = defaultRepairServiceRequest();
                    selectedSingleEquipmentAndPart = null;
                    singleEquipmentAndPartList = [];
                    repairBloc.add(SendPendingRepairToServer());
                    setState(() {});
                  },
                  commentTextEditingController: cancelCommentTextEditingController,
                  onCancelConfirmed: (commentString, selectedServiceRequestAndPart) {
                    selectedSingleEquipmentAndPart = selectedServiceRequestAndPart;
                    cancelServiceRequest(commentString);
                    Navigator.pop(context);
                  },
                );
              }).then((value) {
            print("callback value $value");
            if (value != null) {
              Future.delayed(const Duration(seconds: 1),(){
                repairListBloc.add(
                    FetchLopaRepairServiceRequestList(query: '&location=${_getLocationId()}$timedServiceParams&status=OPEN,PAUSE&isRestricted=true'));
                //setState(() {});
              });
            }
          });
          setState(() {});
        }
      },
      child: Card(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10.0),
        ),
        elevation: 5,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Center(
              child: Stack(
                alignment: Alignment.bottomCenter,
                children: [
                  Opacity(
                    opacity: 0.7,
                    child: Container(
                      height: 55,
                      margin: const EdgeInsets.symmetric(horizontal: 10),
                      decoration: BoxDecoration(
                          borderRadius: const BorderRadius.only(
                            topRight: Radius.circular(15),
                            topLeft: Radius.circular(15),
                          ),
                          color: _getForeGroundColorCode(isSelected, serviceRequestEquipmentAndPartFromServer)),
                    ),
                  ),
                  Container(
                    height: 40,
                    margin: const EdgeInsets.symmetric(horizontal: 5),
                    decoration: BoxDecoration(
                        borderRadius: const BorderRadius.only(
                          topRight: Radius.circular(15),
                          topLeft: Radius.circular(15),
                        ),
                        color: _getBackgroundGroundColorCode(isSelected, serviceRequestEquipmentAndPartFromServer)),
                    child: Center(
                      child: Text(
                        seatDetail.name,
                        style: const TextStyle(fontWeight: FontWeight.bold, color: Colors.white),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  _generateRowAndColumnForSeats(
    List<SeatDetail> firstClassSeatList,
    int rowCount,
  ) {
    int rowSeatCount = rowCount;
    // int columnCount = firstClassSeatList.length/rowSeatCount;
    List<Widget> rowList = [];
    for (int seatPosition = 0; seatPosition < firstClassSeatList.length;) {
      List<Widget> rowWidgets = [];
      List<SeatDetail> rowSeatList = [];
      for (int index = seatPosition; index < firstClassSeatList.length && index < seatPosition + firstClassSeatList[index].rowCount!;index++) {
        SeatDetail seatDetail = firstClassSeatList[index];
        rowSeatCount=firstClassSeatList[index].rowCount!;
        List<SingleEquipmentAndPart?> serviceRequestEquipmentAndPartFromServer = [];
        //IF SINGLE EQUIPMENT SELECTED
        if (serviceRequestList.length == 1) {
          serviceRequestEquipmentAndPartFromServer = [];
        } else {
          if (selectedServiceRequest.requestId > -1 && selectedSingleEquipmentAndPart != null) {
            if (seatDetail.name == selectedSingleEquipmentAndPart!.equipmentName) {
              serviceRequestEquipmentAndPartFromServer.add(selectedSingleEquipmentAndPart);
            }
          }
          //IF ALL SELECTED
          else {
            for (var element in singleEquipmentAndPartList) {
              if (seatDetail.name == element.equipmentName) {
                serviceRequestEquipmentAndPartFromServer.add(element);
              }
            }
          }
        }

        rowWidgets.add(Stack(
          alignment: Alignment.topRight,
          children: [
            Container(
              margin: const EdgeInsets.all(5),
              width: 75,
              child: _singleSeat(seatDetail, firstClassSeatList, serviceRequestEquipmentAndPartFromServer),
            ),
            _getSelectedPartBadge(seatDetail, serviceRequestEquipmentAndPartFromServer),
          ],
        ));
        rowSeatList.add(seatDetail);
      }
      seatPosition = seatPosition + rowSeatCount;
      //Set all row selection to false only one time

      rowList.add(
        InkWell(
          onTap: () => null,
          child: Container(
            margin: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
            decoration: BoxDecoration(
              border: Border.all(width: 2, color: Colors.grey),
            ),
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(5),
              child: Center(
                child: SingleChildScrollView(
                  physics: const BouncingScrollPhysics(),
                  scrollDirection: Axis.horizontal,
                  child: Row(mainAxisAlignment: MainAxisAlignment.spaceEvenly, children: rowWidgets),
                ),
              ),
            ),
          ),
        ),
      );
    }
    return Column(
      children: rowList,
    );
  }

  _getSelectedPartBadge(SeatDetail seatDetail, List<SingleEquipmentAndPart?> serviceRequestEquipmentAndPartFromServer) {
    int inCompletePart = 0;
    if (selectedServiceRequest.requestId > -1 && serviceRequestEquipmentAndPartFromServer.isNotEmpty) {
      if (!serviceRequestEquipmentAndPartFromServer.first!.isSRPresentInDb) {
        //GET ALL COUNT OF PART WHICH ARE NOT COMPLETE
        inCompletePart = serviceRequestEquipmentAndPartFromServer.first!.partList.where((element) => element.completed == false).length;
      }

      // IF ALL COMPLETE
      if (inCompletePart == 0) {
        return Center(
          child: Icon(
            Icons.check_circle,
            color: Theme.of(context).primaryColor,
          ),
        );
      }
      //IF THERE ARE INCOMPLETE PARTS
      if (inCompletePart > 0) {
        //GET COUNT OF PART WHICH ARE EITHER REPAIRED OR COMPLETED

        int completeRepairCount =
            serviceRequestEquipmentAndPartFromServer.first!.partList.where((element) => element.isRepaired == true || element.completed).length;
        //IF ALL PART ARE EITHER REPAIRED OR COMPLETED
        if (completeRepairCount == serviceRequestEquipmentAndPartFromServer.first!.partList.length) {
          return Center(
            child: Icon(
              Icons.check_circle,
              color: Theme.of(context).primaryColor,
            ),
          );
        }
        //IF NOT SAME THEN SET INCOMPLETE COUNT AS TOTAL PART NEED TO BE  REPAIR COUNT
        if (completeRepairCount > 0) {
          inCompletePart = completeRepairCount;
        }
      }
    }
    //IF ALL SELECTED
    else {
      int totalPartCount = 0;

      //GET ALL COUNT OF PART WHICH ARE NOT COMPLETE
      if (serviceRequestEquipmentAndPartFromServer.isNotEmpty) {
        for (var element in serviceRequestEquipmentAndPartFromServer) {
          inCompletePart += element!.partList.where((element) => element.completed == false).length;
          totalPartCount += element.partList.length;
          if (element.isSRPresentInDb) {
            inCompletePart = inCompletePart - 1;
          }
        }
        // IF ALL COMPLETE
        if (inCompletePart == 0) {
          return Center(
            child: Icon(
              Icons.check_circle,
              color: Theme.of(context).primaryColor,
            ),
          );
        }
        //IF THERE ARE INCOMPLETE PARTS
        if (inCompletePart > 0) {
          //GET COUNT OF PART WHICH ARE EITHER REPAIRED OR COMPLETED
          int completeRepairCount = 0;
          for (var element in serviceRequestEquipmentAndPartFromServer) {
            completeRepairCount += element!.partList.where((element) => element.isRepaired == true || element.completed).length;
          }

          //IF ALL PART ARE EITHER REPAIRED OR COMPLETED
          if (completeRepairCount == totalPartCount) {
            return Center(
              child: Icon(
                Icons.check_circle,
                color: Theme.of(context).primaryColor,
              ),
            );
          }
          //IF NOT SAME THEN SET INCOMPLETE COUNT AS TOTAL PART NEED TO BE  REPAIR COUNT
          if (completeRepairCount > 0) {
            inCompletePart = completeRepairCount;
          }
        }
      }
    }
    return inCompletePart > 0
        ? Positioned(
            right: -6,
            top: -6,
            child: Container(
              margin: const EdgeInsets.all(7.0),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(15.0),
                color: Theme.of(context).primaryColor,
              ),
              constraints: const BoxConstraints(
                minWidth: 21,
                minHeight: 21,
              ),
              child: Center(
                child: Text(
                  '$inCompletePart',
                  textAlign: TextAlign.center,
                  style: const TextStyle(fontSize: 13, color: Colors.white, height: 1.2),
                ),
              ),
            ),
          )
        : Container();
  }

  _getBody() {
    if (SelectedFleetCubit.seatCategoryList.isNotEmpty) {
      return StreamBuilder<List<RepairData>>(
          stream: repairBloc.repairDao.getPendingRepairFromDbAsStream(),
          builder: (context, snapshot) {
            //bool isPresentInDb = false;
            if (selectedServiceRequest.requestId > -1) {
              if (snapshot.hasData) {
                List<RepairData> repairData = snapshot.data!.where((element) => element.requestId == selectedServiceRequest.requestId).toList();
                if (repairData.isNotEmpty) {
                  //isPresentInDb = true;
                  selectedServiceRequest.isPresentInDb = true;
                  selectedSingleEquipmentAndPart!.isSRPresentInDb = true;
                } else {
                  selectedServiceRequest.isPresentInDb = false;
                  selectedSingleEquipmentAndPart!.isSRPresentInDb = false;
                }
              }
            } else {
              if (snapshot.hasData) {
                List<RepairData>? repairData = snapshot.data;
                if (repairData != null) {
                  if (repairData.isNotEmpty) {
                    //isPresentInDb = true;
                    for (var singleEquipment in singleEquipmentAndPartList) {
                      if (repairData.where((element) => element.requestId == singleEquipment.serviceRequestId).isNotEmpty) {
                        singleEquipment.isSRPresentInDb = true;
                      } else {
                        singleEquipment.isSRPresentInDb = false;
                      }
                    }
                  }
                }
              }
            }

            return Stack(
              alignment: Alignment.topCenter,
              children: [
                ConstrainedBox(
                  constraints: BoxConstraints(maxHeight: MediaQuery.of(context).size.height),
                  child: Scrollbar(
                    controller: _scrollController,
                    child: SingleChildScrollView(
                      controller: _scrollController,
                      physics: const BouncingScrollPhysics(),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ApplicationUtil.displayNotificationWidgetIfExist(context, LopaRepairPage.routeName),
                          /* SizedBox(
                            height: MediaQuery.of(context).padding.top + 10,
                          ),*/
                          _getFleetAndSubFleetName(),
                          const SizedBox(
                            height: 10,
                          ),
                          BlocConsumer<RepairListApiBloc, RepairListApiState>(
                            listener: (context, state) {
                              if (state is FetchedLopaRepairServiceRequestList) {
                                serviceRequestList = state.repairServiceRequestList;
                                _updateDefaultValueBaseOnSRSize();
                              }
                            },
                            builder: (context, state) {
                              if (state is FetchingLopaRepairServiceRequestList) {
                                return const Center(child: CircularProgressIndicator());
                              }
                              if (serviceRequestList.length == 1) {
                                return Center(
                                    child: Text(
                                  AppLocalizations.of(context)!.noServiceRequestFound,
                                  style: const TextStyle(fontSize: 18),
                                ));
                              } else if (serviceRequestList.isNotEmpty) {
                                return _getServiceRequestDropdown();
                              }
                              return const Center(child: CircularProgressIndicator());
                            },
                          ),
                          if (!showAllServiceRequestUIOption)
                            Container(
                              padding: const EdgeInsets.only(left: 15, right: 15, top: 5),
                              child: _getDurationWidget(selectedServiceRequest),
                            ),
                          _getLopaLayoutFromJson()
                        ],
                      ),
                    ),
                  ),
                ),
                BlocConsumer<RepairBloc, RepairState>(
                  listener: (context, state) {
                    if (state is SentPendingRepairToServer) {
                      selectedServiceRequest = defaultRepairServiceRequest();
                      SelectedFleetCubit.selectedTail!.serviceRequestCount = SelectedFleetCubit.selectedTail!.serviceRequestCount - state.count!;
                      selectedSingleEquipmentAndPart = null;
                      singleEquipmentAndPartList = [];
                      serviceRequestBloc.add(ServiceRequestSendNotification(count: 1, message: AppLocalizations.of(context)!.repairCompleted));
                      repairListBloc.add(FetchLopaRepairServiceRequestList(
                          query:
                              '&location=${_getLocationId()}$timedServiceParams&status=OPEN,PAUSE&isRestricted=true'));
                    } else if (state is NetworkErrorInRepair) {
                      serviceRequestBloc.add(
                        ServiceRequestSendErrorNotification(message: state.error),
                      );
                      _updateDefaultValueBaseOnSRSize();
                    }
                  },
                  builder: (context, state) {
                    return Container();
                  },
                ),
              ],
            );
          });
    } else {
      return Text(AppLocalizations.of(context)!.noRecordFound);
    }
  }

  _getSubFleetName() {
    if (SelectedFleetCubit.fleetLocation.subFleetName != AppLocalizations.of(context)!.select) {
      return Row(
        children: [
          Text(
            SelectedFleetCubit.fleetLocation.subFleetName,
            style: TextStyle(color: Theme.of(context).primaryColor, fontWeight: FontWeight.bold, fontSize: 18, height: 1.2),
          ),
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 5),
            child: const FaIcon(
              FontAwesomeIcons.angleRight,
              size: 20,
            ),
          ),
        ],
      );
    }
    return Container();
  }

  String _getLocationId() {
    FleetLocation fleetLocation = SelectedFleetCubit.fleetLocation;
    String locationId = fleetLocation.fleetName;
    if (fleetLocation.subFleetName != 'Select') {
      if (fleetLocation.subFleetName.contains('-')) {
        locationId += '-' + fleetLocation.subFleetName.replaceAll('-', '_');
      } else {
        locationId += '-' + fleetLocation.subFleetName;
      }
    }
    locationId += '-' + fleetLocation.tailName;
    return locationId;
  }

  _getFleetAndSubFleetName() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 15),
      child: Row(
        children: [
          Text(
            SelectedFleetCubit.fleetLocation.fleetName,
            style: TextStyle(color: Theme.of(context).primaryColor, fontWeight: FontWeight.bold, fontSize: 18, height: 1.2),
          ),
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 5),
            child: const FaIcon(
              FontAwesomeIcons.angleRight,
              size: 20,
            ),
          ),
          _getSubFleetName(),
          Text(
            SelectedFleetCubit.fleetLocation.tailName,
            style: TextStyle(color: Theme.of(context).primaryColor, fontWeight: FontWeight.bold, fontSize: 18, height: 1.2),
          ),
        ],
      ),
    );
  }

  _getServiceRequestDropdown() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          margin: const EdgeInsets.only(left: 15, right: 15, bottom: 8),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  AppLocalizations.of(context)!.selectServiceRequest,
                  style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: Colors.black),
                ),
              ),
              _getPartBagIconButton(),
              if (!showAllServiceRequestUIOption) _getMessagesWidget(),
            ],
          ),
        ),
        Container(
          margin: const EdgeInsets.only(left: 15, right: 15),
          decoration: BoxDecoration(color: Theme.of(context).primaryColor, borderRadius: BorderRadius.circular(5)),
          child: DropdownButton(
            underline: Container(),
            value: selectedServiceRequest,
            hint: Align(
              alignment: Alignment.centerLeft,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Text(
                  AppLocalizations.of(context)!.allServiceRequests,
                  textAlign: TextAlign.start,
                  style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                ),
              ),
            ),
            icon: const Icon(
              // Add this
              Icons.arrow_drop_down,
              color: Colors.white,
              size: 35, // Add this
            ),
            isExpanded: true,
            items: serviceRequestList.map(
              (val) {
                return DropdownMenuItem(
                  value: val,
                  child: Row(
                    children: [
                      if (val.requestType == 'SCHEDULED')
                        FaIcon(
                          FontAwesomeIcons.clock,
                          color: Theme.of(context).primaryColor,
                        ),
                      const SizedBox(
                        width: 10,
                      ),
                      Text(_getEquipmentNameAndDescription(val)),
                    ],
                  ),
                );
              },
            ).toList(),
            selectedItemBuilder: (BuildContext ctx) {
              return serviceRequestList.map<Widget>((item) {
                return DropdownMenuItem<RepairServiceRequest>(
                    child: Container(
                      margin: const EdgeInsets.only(left: 20),
                      child: Text(_getEquipmentNameAndDescription(item), style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold)),
                    ),
                    value: item);
              }).toList();
            },
            onChanged: (value) {
              setState(() {
                selectedServiceRequest = value as RepairServiceRequest;
                if (selectedServiceRequest.requestId > -1) {
                  showAllServiceRequestUIOption = false;
                } else {
                  showAllServiceRequestUIOption = true;
                }
                _setDefaultValues(selectedServiceRequest);
              });
            },
          ),
        ),
      ],
    );
  }

  _getForeGroundColorCode(bool isSelected, List<SingleEquipmentAndPart?> serviceRequestEquipmentAndPartFromServer) {
    Color color = Colors.blue.shade200;

    if (selectedServiceRequest.requestId > -1) {
      if (serviceRequestEquipmentAndPartFromServer.isNotEmpty) {
        if (serviceRequestEquipmentAndPartFromServer.first!.isSRPresentInDb) {
          color = Colors.green;
        } else {
          int completeRepairCount =
              serviceRequestEquipmentAndPartFromServer.first!.partList.where((partMap) => partMap.completed == true || partMap.isRepaired).length;
          if (completeRepairCount == serviceRequestEquipmentAndPartFromServer.first!.partList.length) {
            color = Colors.green;
          } else {
            color = Colors.orange.shade200;
          }
        }
      }
    } else {
      if (serviceRequestEquipmentAndPartFromServer.isNotEmpty) {
        //IF ALL PART ARE PRESENT IN DB THAN SHOW GREEN
        if (serviceRequestEquipmentAndPartFromServer.where((element) => element!.isSRPresentInDb == true).length ==
            serviceRequestEquipmentAndPartFromServer.length) {
          color = Colors.green;
        } else {
          int completeRepairCount = 0;
          int totalRepairCount = 0;
          for (var element in serviceRequestEquipmentAndPartFromServer) {
            if (element!.serviceRequestId > 0) {
              completeRepairCount += element.partList.where((partMap) => partMap.completed == true || partMap.isRepaired).length;
              totalRepairCount += element.partList.length;
            }
          }
          if (completeRepairCount == totalRepairCount) {
            color = Colors.green;
          } else {
            color = Colors.orange.shade200;
          }
        }
      }
    }
    return color;

    /* if (isSelected) {
      return Colors.orange.shade200;
    } else {
      return Colors.blue.shade200;
    }*/
  }

  _getBackgroundGroundColorCode(bool isSelected, List<SingleEquipmentAndPart?> serviceRequestEquipmentAndPartFromServer) {
    Color color = Colors.blue;
    if (selectedServiceRequest.requestId > 0) {
      if (serviceRequestEquipmentAndPartFromServer.isNotEmpty) {
        if (serviceRequestEquipmentAndPartFromServer.first!.isSRPresentInDb) {
          color = Colors.green;
        } else {
          int completeRepairCount = serviceRequestEquipmentAndPartFromServer.first!.partList
              .where((partMap) => partMap.completed == true || partMap.isRepaired == true)
              .length;
          if (completeRepairCount == serviceRequestEquipmentAndPartFromServer.first!.partList.length) {
            color = Colors.green;
          } else {
            color = Colors.orange;
          }
        }
      }
    } else {
      if (serviceRequestEquipmentAndPartFromServer.isNotEmpty) {
        //IF ALL PART ARE PRESENT IN DB THAN SHOW GREEN
        int serviceRequestCountInDB = serviceRequestEquipmentAndPartFromServer.where((element) => element!.isSRPresentInDb == true).length;
        if (serviceRequestCountInDB > 0 && serviceRequestCountInDB == serviceRequestEquipmentAndPartFromServer.length) {
          color = Colors.green;
        }
        //ELSE CHECK FOR COUNT AND UPDTE
        else {
          int completeRepairCount = 0;
          int totalRepairCount = 0;
          for (var element in serviceRequestEquipmentAndPartFromServer) {
            if (element!.serviceRequestId > 0) {
              completeRepairCount += element.partList.where((partMap) => partMap.completed == true || partMap.isRepaired).length;
              totalRepairCount += element.partList.length;
              if (element.isSRPresentInDb) {
                totalRepairCount = totalRepairCount + 1;
              }
            }
          }
          if (completeRepairCount == totalRepairCount) {
            color = Colors.green;
          } else {
            color = Colors.orange;
          }
        }
      }
    }
    return color;

    /*if (isSelected) {
      return Colors.orange;
    } else {
      return Colors.blue;
    }*/
  }

  _getPartList(SeatDetail seatDetail, List<SingleEquipmentAndPart?> serviceRequestEquipmentAndPartFromServer) {
    if (selectedServiceRequest.requestId > -1) {
      if (serviceRequestEquipmentAndPartFromServer.first != null) {
        for (var srPart in serviceRequestEquipmentAndPartFromServer.first!.partList) {
          if (seatDetail.partList.where((part) => part.part1 == srPart.partId).isNotEmpty) {
            Part part = seatDetail.partList.where((part) => part.part1 == srPart.partId).first;
            srPart.description = part.description;
            srPart.equipmentLocationId = part.equipmentLocationId;
          }
        }
        return serviceRequestEquipmentAndPartFromServer;
      }
    } else {
      // SingleEquipmentAndPart? singleEquipmentAndPart;
      List<SingleEquipmentAndPart?> list = serviceRequestEquipmentAndPartFromServer.where((element) => element!.isSRPresentInDb == false).toList();
      /* if (list.length > 1) {
        singleEquipmentAndPart = list.reduce((a, b) =>
            a!.createdDateTimeSR.isBefore(b!.createdDateTimeSR) ? a : b);
        selectedSingleEquipmentAndPart = singleEquipmentAndPart;
      } else {
        singleEquipmentAndPart = list.first;
        selectedSingleEquipmentAndPart = singleEquipmentAndPart;
      }*/
      for (var singleEquipmentAndPart in list) {
        if (singleEquipmentAndPart != null) {
          for (var srPart in singleEquipmentAndPart.partList) {
            if (seatDetail.partList.where((part) => part.part1 == srPart.partId).isNotEmpty) {
              Part part = seatDetail.partList.where((part) => part.part1 == srPart.partId).first;
              srPart.description = part.description;
              srPart.equipmentLocationId = part.equipmentLocationId;
            }
          }
        }
      }
      return list;
    }

    /// IF THERE IS NO SR,RETURN ALL DEFAULT PARTS
    List<LopaRepairPartItem> lopaPartList = [];
    for (var element in seatDetail.partList) {
      lopaPartList.add(
        LopaRepairPartItem(partId: element.part1, completed: false, description: element.description, imageList: []),
      );
    }
    return lopaPartList;
  }

  void saveRepairServiceRequestInDatabase(Map<String, dynamic> extensionMap) {
    int? userId = getIt<SharedPreferences>().getInt('userId');
    Map<String, List<Map<String, dynamic>>> imageListMap = {};
    Map<String, List<Map<String, dynamic>>> mapData = {};
    //
    List<Map<String, dynamic>> checkPartList = [];
    for (var part in selectedSingleEquipmentAndPart!.partList) {
      Map<String, dynamic> partsMap = {};
      partsMap['PartId'] = part.partId;
      partsMap['Completed'] = part.completed;
      partsMap['LocationId'] = part.equipmentLocationId;
      if (part.isRepaired == true) {
        partsMap['Completed'] = true;
      }
      partsMap['IsRepaired'] = part.isRepaired;
      checkPartList.add(partsMap);
    }
    mapData[selectedSingleEquipmentAndPart!.equipmentName] = checkPartList;
    // ADDED DOCUMENT
    imageListMap[selectedSingleEquipmentAndPart!.equipmentName] = selectedSingleEquipmentAndPart!.imageList;
    //
    print(jsonEncode(mapData));
    bool checkIsAtleastOnePartRepaired = true;
    mapData.forEach((key, value) {
      if (value.where((element) => element['Completed'] == true).toList().isEmpty) {
        checkIsAtleastOnePartRepaired = false;
      }
    });
    if (checkIsAtleastOnePartRepaired) {
      repairBloc.add(
        SaveRepairRequest(
          repairCompanion: RepairCompanion(
              createdAt: moor.Value(DateTime.now()),
              createdBy: moor.Value(userId),
              currentLocationId: moor.Value(_getLocationId()),
              newLocationId: const moor.Value(null),
              parts: moor.Value(json.encode(mapData)),
              extn: moor.Value(json.encode(extensionMap)),
              repairDocument: moor.Value(json.encode(imageListMap)),
              customerId: const moor.Value(null),
              equipmentId: const moor.Value(null),
              requestId: moor.Value(selectedSingleEquipmentAndPart!.serviceRequestId),
              latitude: moor.Value(locationData?.latitude),
              longitude: moor.Value(locationData?.longitude)),
        ),
      );
    } else {
      ApplicationUtil.showSnackBar(context: context, message: AppLocalizations.of(context)!.atLeastRepairAPartToContinue);
    }
  }

  _getRepairNotification(double repairHeight) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      alignment: Alignment.centerLeft,
      width: double.infinity,
      color: const Color(0xff022F49),
      height: repairHeight,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: Container(
              margin: const EdgeInsets.only(left: 10, top: 20),
              padding: const EdgeInsets.all(10),
              child: RichText(
                text: TextSpan(
                  style: const TextStyle(
                    fontSize: 16.0,
                    color: Colors.black,
                  ),
                  children: <TextSpan>[
                    TextSpan(
                      text: '${AppLocalizations.of(context)!.repair} ',
                      style: const TextStyle(color: Colors.white),
                    ),
                    TextSpan(text: AppLocalizations.of(context)!.completedSuccessfully, style: const TextStyle(color: Colors.white)),
                  ],
                ),
              ),
            ),
          ),
          repairHeight != 0
              ? Container(
                  margin: const EdgeInsets.only(left: 10, top: 20),
                  child: IconButton(
                    onPressed: () {
                      repairBloc.add(ResetRepairUI());
                    },
                    icon: const FaIcon(
                      FontAwesomeIcons.lightTimesCircle,
                      color: Colors.white,
                    ),
                  ),
                )
              : Container()
        ],
      ),
    );
  }

  void _setDefaultValues(RepairServiceRequest selectedServiceRequest) {
    //IF SINGLE SRU IS SELECTED
    if (selectedServiceRequest.requestId > -1) {
      selectedSingleEquipmentAndPart = null;
      selectedServiceRequest.partsMap!.forEach((key, value) {
        Map<String, dynamic> map = {};
        map['equipmentName'] = key;
        map['partData'] = value;
        SingleEquipmentAndPart singleEquipment = SingleEquipmentAndPart.fromMap(map, selectedServiceRequest);
        selectedSingleEquipmentAndPart = singleEquipment;
        selectedSingleEquipmentAndPart!.serviceRequestId = selectedServiceRequest.requestId;
        selectedSingleEquipmentAndPart!.remainingDateTime = selectedServiceRequest.remainingRequestTime;
        selectedSingleEquipmentAndPart!.status = selectedServiceRequest.status;
        selectedSingleEquipmentAndPart!.isSRPresentInDb = selectedServiceRequest.isPresentInDb;
      });
    }
    //IF ALL SR IS SELECTED
    else {
      selectedSingleEquipmentAndPart = null;
      singleEquipmentAndPartList = [];
      for (var element in serviceRequestList) {
        if (element.requestId > -1) {
          element.partsMap!.forEach((key, value) {
            Map<String, dynamic> map = {};
            map['equipmentName'] = key;
            map['partData'] = value;
            SingleEquipmentAndPart singleEquipment = SingleEquipmentAndPart.fromMap(map, element);
            singleEquipmentAndPartList.add(singleEquipment);
          });
        }
      }
    }
  }

  void _showServiceRequestDialogWarning(BuildContext ctx, SeatDetail seatDetail) {
    AlertDialog alert = AlertDialog(
      title: Text(AppLocalizations.of(context)!.noServiceRequestFound),
      content: Container(
        width: double.maxFinite,
        constraints: const BoxConstraints(
          maxWidth: 500,
        ),
        child: Text(
         customerId == 15 ? AppLocalizations.of(context)!.thereIsNoServiceRequestWithThisAsset: AppLocalizations.of(context)!.thereIsNoServiceRequestWithThisEquipment,
          style: const TextStyle(height: 1.2),
        ),
      ),
      actions: [
        //Continue button
        ElevatedButton(
          child: Text(AppLocalizations.of(context)!.continueString),
          onPressed: () {
            SelectedFleetCubit.serviceRequestList = serviceRequestList;
            SelectedFleetCubit.selectedSeat = seatDetail;
            Navigator.of(ctx).pop();
            Navigator.pushNamed(context, DigitalLopaPage.routeName, arguments: ServiceRequestType.DIRECT_REPAIR).then((value) {
              for (var seatCategory in SelectedFleetCubit.seatCategoryList) {
                // LOOP EACH SEAT
                for (var seatDetail in seatCategory.seatDetailList) {
                  seatDetail.markSeatIsRepaired = false;
                  seatDetail.continueForRepairOrSR = false;
                }
              }
            });
          },
        ),
        //Cancel button
        ElevatedButton(
          child: Text(AppLocalizations.of(context)!.cancel),
          onPressed: () {
            Navigator.of(ctx).pop();
          },
        ),
      ],
    );
    showDialog(
      barrierDismissible: false,
      context: ctx,
      builder: (BuildContext context) {
        return alert;
      },
    );
  }

  _getMessagesWidget() {
    return Stack(
      alignment: Alignment.topRight,
      children: [
        IconButton(
          onPressed: () {
            showDialog(
              context: context,
              builder: (context) {
                return BlocProvider<MessageCubit>(
                  create: (context) => MessageCubit(apiRepository: ApiRepository(apiService: ApiService())),
                  child: MessageDialog(
                    requestId: selectedServiceRequest.requestId,
                  ),
                );
              },
            );
          },
          icon: FaIcon(
            FontAwesomeIcons.envelope,
            color: Theme.of(context).primaryColor,
          ),
        ),
      ],
    );
  }

  _getDurationWidget(RepairServiceRequest serviceRequest) {
    if (serviceRequest.remainingRequestTime != null) {
      if (serviceRequest.requestType == "NOTIFICATION") {
        return Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              width: 150,
              padding: const EdgeInsets.symmetric(
                vertical: 5,
              ),
              decoration: BoxDecoration(
                color: AppColor.orangeColor,
                borderRadius: BorderRadius.circular(5),
              ),
              child: Text(
                serviceRequest.status!,
                textAlign: TextAlign.center,
                style: const TextStyle(color: Colors.white, fontSize: 13, fontWeight: FontWeight.bold),
              ),
            ),
            const SizedBox(
              width: 10,
            ),
            Container(
              width: 150,
              padding: const EdgeInsets.symmetric(vertical: 5),
              decoration: BoxDecoration(
                color: AppColor.orangeColor,
                borderRadius: BorderRadius.circular(5),
              ),
              child: Text(
                serviceRequest.requestType!,
                textAlign: TextAlign.center,
                overflow: TextOverflow.ellipsis,
                style: const TextStyle(color: Colors.white, fontSize: 12, fontWeight: FontWeight.bold),
              ),
            ),
            serviceRequest.safetyIssue != null
                ? Expanded(
                    child: Image.asset(
                      width: 20,
                      height: 20,
                      "assets/images/hazard.png",
                    ),
                  )
                : Container()
          ],
        );
      } else {
        if (serviceRequest.remainingRequestTime! > 0) {
          return Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                width: 150,
                padding: const EdgeInsets.symmetric(
                  vertical: 5,
                ),
                decoration: BoxDecoration(
                  color: AppColor.orangeColor,
                  borderRadius: BorderRadius.circular(5),
                ),
                child: Text(
                  serviceRequest.status!,
                  textAlign: TextAlign.center,
                  style: const TextStyle(color: Colors.white, fontSize: 13, fontWeight: FontWeight.bold),
                ),
              ),
              const SizedBox(
                width: 10,
              ),
              Container(
                width: 150,
                padding: const EdgeInsets.symmetric(vertical: 5),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor,
                  borderRadius: BorderRadius.circular(5),
                ),
                child: getIt<ServiceType>().type == ServiceRequestType.SCHEDULED
                    ? Text(
                        ApplicationUtil.getHourAndMinuteOrDayFromMinute(serviceRequest.remainingRequestTime!) +
                            ' ${AppLocalizations.of(context)!.remaining}',
                        textAlign: TextAlign.center,
                        overflow: TextOverflow.ellipsis,
                        style: const TextStyle(color: Colors.white, fontSize: 12, fontWeight: FontWeight.bold),
                      )
                    : Text(
                        ApplicationUtil.getHourAndMinuteFromMinute(serviceRequest.remainingRequestTime!) +
                            ' ${AppLocalizations.of(context)!.remaining}',
                        textAlign: TextAlign.center,
                        overflow: TextOverflow.ellipsis,
                        style: const TextStyle(color: Colors.white, fontSize: 12, fontWeight: FontWeight.bold),
                      ),
              ),
              serviceRequest.safetyIssue != null
                  ? Expanded(
                      child: Image.asset(
                        width: 20,
                        height: 20,
                        "assets/images/hazard.png",
                      ),
                    )
                  : Container()
            ],
          );
        } else {
          return Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                width: 150,
                padding: const EdgeInsets.symmetric(vertical: 5),
                decoration: BoxDecoration(
                  color: AppColor.redColor,
                  borderRadius: BorderRadius.circular(5),
                ),
                child: Text(
                  serviceRequest.status!,
                  textAlign: TextAlign.center,
                  style: const TextStyle(color: Colors.white, fontSize: 13, fontWeight: FontWeight.bold),
                ),
              ),
              const SizedBox(
                width: 10,
              ),
              Container(
                width: 150,
                padding: const EdgeInsets.symmetric(vertical: 5),
                decoration: BoxDecoration(
                  color: AppColor.redColor,
                  borderRadius: BorderRadius.circular(5),
                ),
                child: getIt<ServiceType>().type == ServiceRequestType.SCHEDULED
                    ? Text(
                        ApplicationUtil.getHourAndMinuteOrDayFromMinute(serviceRequest.remainingRequestTime!) +
                            ' ${AppLocalizations.of(context)!.overdue}',
                        textAlign: TextAlign.center,
                        style: const TextStyle(color: Colors.white, fontSize: 13, fontWeight: FontWeight.bold),
                      )
                    : Text(
                        ApplicationUtil.getHourAndMinuteFromMinute(serviceRequest.remainingRequestTime!) +
                            ' ${AppLocalizations.of(context)!.overdue}',
                        textAlign: TextAlign.center,
                        style: const TextStyle(color: Colors.white, fontSize: 13, fontWeight: FontWeight.bold),
                      ),
              ),
              serviceRequest.safetyIssue != null
                  ? Expanded(
                      child: Image.asset(
                        width: 20,
                        height: 20,
                        "assets/images/hazard.png",
                      ),
                    )
                  : Container()
            ],
          );
        }
      }
    }
    return Container();
  }

  _getPartBagIconButton() {
    return InkResponse(
      onTap: () {
        String query = 'fleet=' + SelectedFleetCubit.fleetLocation.fleetName;
        if (SelectedFleetCubit.fleetLocation.subFleetName != AppLocalizations.of(context)!.select) {
          query += '&subfleet=' + SelectedFleetCubit.fleetLocation.subFleetName;
        }
        query += '&tailno=' + SelectedFleetCubit.fleetLocation.tailName;
        if (getIt<ServiceType>().type == ServiceRequestType.SCHEDULED) {
          query += '&requestType=SCHEDULED';
        }
        showDialog(
          context: context,
          builder: (context) => BlocProvider<PartItemsBloc>(
            create: (context) => PartItemsBloc(apiRepository: ApiRepository(apiService: ApiService())),
            child: PartListDialog(
              query: query,
              onClose: () {
                setState(() {});
              },
            ),
          ),
        );
      },
      child: Stack(
        alignment: Alignment.topRight,
        children: [
          Padding(
            padding: const EdgeInsets.all(9),
            child: FaIcon(
              FontAwesomeIcons.shoppingBag,
              color: Theme.of(context).primaryColor,
            ),
          ),
          SelectedFleetCubit.selectedTail!.serviceRequestCount > 0
              ? Container(
                  padding: EdgeInsets.all(SelectedFleetCubit.selectedTail!.serviceRequestCount < 10 ? 5 : 3),
                  decoration: BoxDecoration(color: Theme.of(context).primaryColor.withOpacity(0.9), shape: BoxShape.circle),
                  child: Center(
                    child: Text(
                      SelectedFleetCubit.selectedTail!.serviceRequestCount.toString(),
                      textAlign: TextAlign.center,
                      style: const TextStyle(color: Colors.white, fontSize: 12, height: 1.2),
                    ),
                  ),
                )
              : Container()
        ],
      ),
    );
  }

  List<SingleEquipmentAndPart?> _getServiceRequestEquipmentAndPartBaseOnSR(List<SingleEquipmentAndPart?> serviceRequestEquipmentAndPartFromServer) {
    /* if (selectedServiceRequest.requestId > -1) {
      return serviceRequestEquipmentAndPartFromServer.first;
    } else {
      SingleEquipmentAndPart? singleEquipmentAndPart =
          serviceRequestEquipmentAndPartFromServer.reduce((a, b) =>
              a!.createdDateTimeSR.isAfter(b!.createdDateTimeSR) ? a : b);
      return singleEquipmentAndPart;
    }*/
    return serviceRequestEquipmentAndPartFromServer;
  }

  void _updateDefaultValueBaseOnSRSize() {
    if (serviceRequestList.length > 1) {
      selectedServiceRequest = serviceRequestList[0];
      setState(() {
        _setDefaultValues(selectedServiceRequest);
      });
    } else if (serviceRequestList.length == 1) {
      showAllServiceRequestUIOption = true;
      setState(() {});
    }
  }

  _showConfirmationDialog(BuildContext context) {
    if (serviceRequestList.length > 1) {
      ApplicationUtil.showConfirmDialog(
        context,
        title: AppLocalizations.of(context)!.warning,
        description: customerId == 15 ? AppLocalizations.of(context)!.thereAreOpenServiceRequestforStadium : AppLocalizations.of(context)!.thereAreOpenServiceRequest,
        continueString: AppLocalizations.of(context)!.continueString,
        cancelString: AppLocalizations.of(context)!.cancel,
        onContinue: () {
          Navigator.pop(context);
        },
      );
    } else {
      Navigator.pop(context);
    }
  }

  void cancelServiceRequest(String commentString) {
    apiBloc.add(CancelServiceRequest(serviceRequestId: selectedSingleEquipmentAndPart!.serviceRequestId, comment: commentString));
  }

  _getEquipmentNameAndDescription(RepairServiceRequest val) {
    if (val.requestId > -1) {
      if (val.equipmentName != null) {
        return '${AppLocalizations.of(context)!.seat} ${val.equipmentName}: ${val.description!}';
      }
    }
    return val.description!;
  }

  void _initLocation() async {
    var locationDetails = await ApplicationUtil.getGeoLocatorLocation();
    if (locationDetails != null && locationDetails.runtimeType != String) {
      locationData = locationDetails;
    } else if (locationDetails == "WEB") {
      Logger.i("Web platform detected in init - skipping location initialization");
    } else if(locationDetails == "TIMEOUT"){
      Logger.i("Timeout detected in init - skipping location initialization");
    }
  }
}
