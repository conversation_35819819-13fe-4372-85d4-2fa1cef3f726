import 'package:alink/util/application_util.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import '../../../../logger/logger.dart';
import 'audit_barcode_scanner_widget.dart';

/// FullScreenScannerPage
class AuditBarCodeScannerPage extends StatefulWidget {
  static const routeName = '/audit-barcode-scanner';

  const AuditBarCodeScannerPage({Key? key}) : super(key: key);

  @override
  _AuditBarCodeScannerPageState createState() => _AuditBarCodeScannerPageState();
}

class _AuditBarCodeScannerPageState extends State<AuditBarCodeScannerPage> {
  static const String className = '_AuditBarCodeScannerPageState';

  @override
  Widget build(BuildContext context) {
    if (kDebugMode) {
      Logger.i("Class Name: " + className);
    }
    return Scaffold(
      body: Column(
        children: [
          Expanded(
            child: AuditBarcodeScannerWidget.defaultStyle(
              resultCallback: (String code) {
                Navigator.pop(context, code);
              },
            ),
          ),
        ],
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
      floatingActionButton: ApplicationUtil.getBackButton(
        context,
        onBackPressed: () {
          Navigator.pop(context);
        },
      ),
    );
  }
}
