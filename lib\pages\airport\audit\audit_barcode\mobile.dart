import 'dart:developer';
import 'dart:io';

import 'package:alink/bloc/audit/audit_equipment_cubit.dart';
import 'package:alink/bloc/service/service_request_bloc.dart';
import 'package:alink/data/model/barcode_response.dart';
import 'package:alink/data/repository/api_service.dart';
import 'package:alink/util/application_util.dart';
import 'package:alink/widget/audit_bar_code_dialog.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:vibration/vibration.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:qr_code_scanner/qr_code_scanner.dart';

import '../../../../bloc/api/api_bloc.dart';
import '../../../../logger/logger.dart';
import '../../../../util/enums/app_enum.dart';
import '../../repair/assign_equipment/assign_equipment_page.dart';

///ScannerWidget
class AuditBarcodeScanner extends StatefulWidget {
  bool closeScreen = false;
  static String routeName = 'scanner';

  AuditBarcodeScanner({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _AuditBarcodeScannerState();
  }
}

class _AuditBarcodeScannerState extends State<AuditBarcodeScanner> {
  static const String className = '_AuditBarcodeScannerState';
  late FToast fToast;

  Barcode? result;
  QRViewController? controller;
  final GlobalKey qrKey = GlobalKey(debugLabel: 'QR');
  int prevCount = 0, newCount = 0;
  bool scanStatus = true;

  @override
  void initState() {
    super.initState();
    fToast = FToast();
    fToast.init(context);
    widget.closeScreen = false;
  }

  AuditEquipmentCubit get auditEquipmentCubit => BlocProvider.of<AuditEquipmentCubit>(context);

  ServiceRequestBloc get serviceRequestBloc => BlocProvider.of<ServiceRequestBloc>(context);

  ApiBloc get apiBloc => BlocProvider.of<ApiBloc>(context);

  @override
  void reassemble() {
    super.reassemble();
    /*if (Platform.isAndroid) {
      controller!.pauseCamera();
    }*/
    if (Platform.isIOS) {
      controller!.resumeCamera();
    }
    // controller!.resumeCamera();
  }

  @override
  Widget build(BuildContext context) {
    if (kDebugMode) {
      Logger.i("Class Name: " + className);
    }
    return Stack(
      alignment: Alignment.bottomCenter,
      children: [
        //_getNotificationBanner(),
        Stack(
          alignment: Alignment.center,
          children: [
            Column(
              children: <Widget>[
                Expanded(flex: 4, child: _buildQrView(context)),
              ],
            ),
            Center(
              child: Container(
                margin: EdgeInsets.only(top: MediaQuery.of(context).size.width - 50),
                child: Text(
                  AppLocalizations.of(context)!.getTheBarcodeInSquare,
                  style: const TextStyle(color: Colors.white, fontSize: 18),
                ),
              ),
            ),
          ],
        ),
        Container(
          padding: const EdgeInsets.only(bottom: 20),
          child: BlocConsumer<ServiceRequestBloc, ServiceRequestState>(
            listener: (ctx, state) {
              log("log::before FetchedEquipmentFromAuditTable Scanned barcode count->$newCount");
              /*if (state is ServiceRequestSendNotification || state is DismissServiceRequestNotification) {

              }  */
              if (state is FetchedEquipmentFromAuditTable) {
                log("log::Scanned barcode count->$newCount");
                if (newCount < state.equipmentList!.where((element) => element.isScanned == true).toList().length) {
                  if (kDebugMode) {
                    print(newCount);
                    print("+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++");
                    print(state.equipmentList!.where((element) => element.isScanned == true).toList().length);
                  }
                  log("log::show success toast msg");
                  _showSuccessToast();
                  log("log::Scanned barcode count->$newCount");
                  prevCount = newCount;
                  scanStatus = true;
                } else {
                  log("log::show warning toast msg");
                  _showWaringToast();
                  scanStatus = true;
                }
                int count = state.equipmentList!.where((element) => element.isScanned == true).toList().length;
                if (count == state.equipmentList!.length && widget.closeScreen == false) {
                  if (kDebugMode) {
                    print('pop from widget');
                    print(AuditBarcodeScanner.routeName);
                  }
                  widget.closeScreen = true;
                  closePage();
                }
              }
            },
            builder: (context, state) {
              if (state is FetchedEquipmentFromAuditTable || state is ServiceNotificationDismissed) {
                int count = ServiceRequestBloc.equipmentList.where((element) => element.isScanned == true && element.status != "Movement").toList().length;
                int newEquipment = ServiceRequestBloc.equipmentList.where((element) => element.equipmentId == null).toList().length;
                newCount = ServiceRequestBloc.equipmentList.where((element) => element.isScanned == true).toList().length;
                prevCount = ServiceRequestBloc.equipmentList.where((element) => element.isScanned == true && element.name != null).toList().length;
                return Padding(
                  padding: const EdgeInsets.only(bottom: 30),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      const SizedBox(
                        height: 50,
                      ),
                      Text(
                        count > 0
                            ? '${AppLocalizations.of(context)!.equipmentPartOfAudit}: $count'
                                '/${((ServiceRequestBloc.equipmentList.length) - newEquipment).toString()}'
                            : '${AppLocalizations.of(context)!.equipmentPartOfAudit}: 0',
                        style: const TextStyle(fontSize: 18, color: Colors.white),
                      ),
                      const SizedBox(
                        height: 5,
                      ),
                      Text(
                        newEquipment > 0 ? '${AppLocalizations.of(context)!.equipmentOutsideAudit}: $newEquipment' : '${AppLocalizations.of(context)!.equipmentOutsideAudit}: 0',
                        style: const TextStyle(fontSize: 18, color: Colors.white),
                      ),
                      const SizedBox(
                        height: 5,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          getButtonsUI(),
                          const SizedBox(
                            width: 40,
                          ),
                          IconButton(
                            onPressed: () {
                              showGeneralDialog(
                                barrierDismissible: false,
                                context: context,
                                pageBuilder: (context, animation, secondaryAnimation) {
                                  return AuditBarCodeDialog(
                                    callback: () {
                                      Navigator.pop(context);
                                    },
                                  );
                                },
                              );
                            },
                            icon: const Icon(
                              Icons.keyboard_alt,
                              size: 40,
                              color: Colors.white,
                            ),
                          )
                        ],
                      ),
                    ],
                  ),
                );
              }
              else{
                return Column(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    const SizedBox(
                      height: 50,
                    ),
                    Text(
                      '${AppLocalizations.of(context)!.equipmentPartOfAudit}: 0',
                      style: const TextStyle(fontSize: 18, color: Colors.white),
                    ),
                    const SizedBox(
                      height: 5,
                    ),
                    Text(
                      '${AppLocalizations.of(context)!.equipmentOutsideAudit}: 0',
                      style: const TextStyle(fontSize: 18, color: Colors.white),
                    ),
                    const SizedBox(
                      height: 5,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        getButtonsUI(),
                        const SizedBox(
                          width: 40,
                        ),
                        IconButton(
                          onPressed: () {
                            showGeneralDialog(
                              barrierDismissible: false,
                              context: context,
                              pageBuilder: (context, animation, secondaryAnimation) {
                                return AuditBarCodeDialog(
                                  callback: () {
                                    Navigator.pop(context);
                                  },
                                );
                              },
                            );
                          },
                          icon: const Icon(
                            Icons.keyboard_alt,
                            size: 40,
                            color: Colors.white,
                          ),
                        )
                      ],
                    ),
                  ],
                );
              }
            },
          ),
        )
      ],
    );
  }

  Widget _buildQrView(BuildContext context) {
    // For this example we check how width or tall the device is and change the scanArea and overlay accordingly.
    double width = MediaQuery.of(context).size.width;
    double height = MediaQuery.of(context).size.height;
    var scanArea = 0.0;
    //tablet
    if (width > height) {
      scanArea = height - 50;
    } else {
      scanArea = width - 100;
    }
    return QRView(
      key: qrKey,
      onQRViewCreated: _onQRViewCreated,
      overlay: QrScannerOverlayShape(borderColor: Colors.red, borderRadius: 10, borderLength: 30, borderWidth: 10, cutOutSize: scanArea),
      onPermissionSet: (ctrl, p) => _onPermissionSet(context, ctrl, p),
    );
  }

  Future<void> _onQRViewCreated(QRViewController controller) async {
    this.controller = controller;
    // controller.resumeCamera();
    if (Platform.isAndroid) {
      await controller.resumeCamera();
    }
    controller.scannedDataStream.listen((scanData) async {
      if (scanStatus) {
        scanStatus = false;
        Vibration.vibrate();
        var isFlashOn = false;
        var flashStatus = await controller.getFlashStatus();
        if (flashStatus == true) {
          isFlashOn = true;
          this.controller!.toggleFlash();
        }
        this.controller!.pauseCamera();
        Future.delayed(const Duration(seconds: 2), () {
          _saveBarcodeDataInDatabase(scanData.code);
          this.controller!.resumeCamera();
          if (isFlashOn) {
            Future.delayed(const Duration(milliseconds: 200), () {
              this.controller!.toggleFlash();
            });
          }
        });
        //this.controller!.resumeCamera();
      }
    });
  }

  void _onPermissionSet(BuildContext context, QRViewController ctrl, bool p) {
    if (!p) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('no Permission')),
      );
    }
  }

  @override
  void dispose() {
    controller?.dispose();
    super.dispose();
  }

  getButtonsUI() {
    return Container(
      margin: const EdgeInsets.all(8),
      child: IconButton(
        onPressed: () async {
          await controller?.toggleFlash();
          setState(() {});
        },
        icon: FutureBuilder(
          future: controller?.getFlashStatus(),
          builder: (context, snapshot) {
            if (snapshot.data == true) {
              return const Icon(
                Icons.flash_on,
                color: Colors.white,
                size: 35,
              );
            } else {
              return const Icon(
                Icons.flash_off,
                color: Colors.white,
                size: 35,
              );
            }
          },
        ),
      ),
    );
  }

  void closePage() {
    Navigator.pop(context);
  }

  Future<void> _saveBarcodeDataInDatabase(barcodeTag) async {
    log("Scanned barcode and saving in database tag->$barcodeTag");
    try {
      final response = await ApiService().getEquipmentDetailByBarcodeNumber(barcodeTag,false);
      if (response is BarcodeResponse) {
        //serviceRequestBloc.add(DeleteAuditFromTable(auditId: AuditEquipmentCubit.auditId!));
        serviceRequestBloc.add(UpdateBarcodeDataInAudit(AuditEquipmentCubit.auditId!, barcodeTag, null, null, null, const []));
        return;
      } else if (response == ApiResponse.BAR_CODE_NOT_FOUND) {
        //controller!.pauseCamera();
        ApplicationUtil.showWarningAlertDialog(
          context,
          title: AppLocalizations.of(context)!.barcodeNotfound,
          desc: 'This barcode ($barcodeTag) is not mapped to any equipment. Do you want to assign this barcode to new equipment?',
          positiveLabel: AppLocalizations.of(context)!.assign,
          negativeLabel: AppLocalizations.of(context)!.cancel,
          onPositiveClickListener: () async {
            Navigator.of(context).pop();
            //controller!.pauseCamera();
            var getFlashStatus = await controller!.getFlashStatus();
            if (getFlashStatus == true) {
              setState(() {
                controller!.toggleFlash();
              });
            }
            if (kDebugMode) {
              print(AuditEquipmentCubit.auditLocation!['LOCATION_ID']);
            }
            final assignedConfirmation = await Navigator.pushNamed(context, AssignEquipmentPage.routeName,
                arguments: ServiceType(
                    type: ServiceRequestType.ASSIGN_EQUIPMENT, barcodeNumber: barcodeTag, fromAudit: true, defaultLocationId: AuditEquipmentCubit.auditLocation!['LOCATION_ID']));
            if (assignedConfirmation == 204) {
              //serviceRequestBloc.add(UpdateBarcodeDataInAudit(AuditEquipmentCubit.auditId!, barcodeTag, null, null, null, const []));
              _saveBarcodeDataInDatabase(barcodeTag);
            }
            setState(() {
              scanStatus = true;
            });
            //controller!.resumeCamera();
          },
          onNegativeClickListener: () {
            setState(() {
              scanStatus = true;
            });
            //controller!.resumeCamera();
          },
        );
      } else {
        ApplicationUtil.showWarningAlertDialog(context,
            title: AppLocalizations.of(context)!.error, desc: AppLocalizations.of(context)!.errorOccurWhileFetching, negativeLabel: AppLocalizations.of(context)!.okay);
      }
    } catch (e) {
      if (kDebugMode) {
        print("Error occurred while making API call to fetch equipment");
      }
    }
  }

  _showSuccessToast() {
    log("-----------------------------log::Showing success toast-----------------------------");
    Widget toast = Center(
        child: Container(
      width: MediaQuery.of(context).size.width - 50,
      height: 120,
      padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 12.0),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(25.0),
        color: Colors.greenAccent,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(
            FontAwesomeIcons.checkCircle,
            size: 50,
            color: Colors.white,
          ),
          const SizedBox(
            width: 12.0,
          ),
          Text(
            AppLocalizations.of(context)!.successfulScan,
            style: const TextStyle(fontSize: 22, color: Colors.white),
          ),
        ],
      ),
    ));

    fToast.showToast(
      child: toast,
      gravity: ToastGravity.TOP,
      toastDuration: const Duration(seconds: 2),
    );
  }

  _showWaringToast() {
    log("-----------------------------log::Showing warning toast-----------------------------");
    Widget toast = Center(
        child: Container(
      width: MediaQuery.of(context).size.width - 50,
      height: 120,
      padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 12.0),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(25.0),
        color: Colors.orangeAccent,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(
            FontAwesomeIcons.exclamationTriangle,
            size: 50,
            color: Colors.white,
          ),
          const SizedBox(
            width: 12.0,
          ),
          Text(
            AppLocalizations.of(context)!.alreadyScanned,
            style: const TextStyle(fontSize: 18, color: Colors.white),
          ),
        ],
      ),
    ));

    fToast.showToast(
      child: toast,
      gravity: ToastGravity.TOP,
      toastDuration: const Duration(seconds: 2),
    );
  }
  /*_getNotificationBanner() {
    return ApplicationUtil.displayNotificationWidgetIfExist(context, AuditBarcodeScanner.routeName);
  }*/
}
