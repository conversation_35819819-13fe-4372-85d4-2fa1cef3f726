import 'dart:async';

import 'package:alink/bloc/api/api_bloc.dart';
import 'package:alink/bloc/audit/audit_equipment_cubit.dart';
import 'package:alink/bloc/audit_bloc/audit_bloc.dart';
import 'package:alink/bloc/service/service_request_bloc.dart';
import 'package:alink/cubit/location/location_cubit.dart';
import 'package:alink/cubit/pages/audit/audit_list_cubit.dart';
import 'package:alink/data/model/audit_id_model.dart';
import 'package:alink/data/model/audit_location.dart';
import 'package:alink/data/model/audit_response.dart';
import 'package:alink/data/model/location_detail.dart';
import 'package:alink/data/repository/api_service.dart';
import 'package:alink/pages/airport/audit/equipment_list_page.dart';
import 'package:alink/pages/auth/login_page.dart';
import 'package:alink/pages/dashboard_page.dart';
import 'package:alink/util/app_color.dart';
import 'package:alink/util/app_constant.dart';
import 'package:alink/util/application_util.dart';
import 'package:alink/widget/createAdhocAuditDialog.dart';
import 'package:alink/widget/location_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import '../../../data/model/audit_schedule.dart';
import '../../../logger/logger.dart';
import '../../../widget/audit_selection_section.dart';
import '../../../widget/seperator.dart';
import '../../../widget/service_type.dart';

class AuditPage extends StatefulWidget {
  static const String routeName = "audit";

  const AuditPage({Key? key}) : super(key: key);

  @override
  _AuditPageState createState() => _AuditPageState();
}

class _AuditPageState extends State<AuditPage> {
  static const String className = '_AuditPageState';

  String? selectedItem;
  int selected = 0;
  List<LocationDetail> locationList = [];
  late TextEditingController _auditCommentController;
  bool showCreateAuditButton = false;
  String selectedLocationId = '';
  String selectedAuditDescription = '';
  bool getAuditButtonPressed = false;
  int? selectedSegmentValue = 0;
  List<AuditSchedules> auditSchedules = [];
  List emptyList = [];

  @override
  void initState() {
    _auditCommentController = TextEditingController();
    locationCubit.getLocationData();
    super.initState();
  }

  @override
  void dispose() {
    _auditCommentController.dispose();
    super.dispose();
  }

  LocationCubit get locationCubit => BlocProvider.of<LocationCubit>(context);

  AuditListCubit get auditCubit => BlocProvider.of<AuditListCubit>(context);

  ApiBloc get apiBLoc => BlocProvider.of<ApiBloc>(context);

  ServiceRequestBloc get serviceRequestBloc => BlocProvider.of<ServiceRequestBloc>(context);

  AuditBloc get auditBloc => BlocProvider.of<AuditBloc>(context);

  final scrollController = ScrollController();

  @override
  Widget build(BuildContext context) {
    if (kDebugMode) {
      Logger.i("Class Name: " + className);
    }
    return WillPopScope(
      onWillPop: () async {
        if (kDebugMode) {
          print('reset');
        }
        auditBloc.add(ResetAuditListData());
        auditCubit.resetAuditList();
        return false;
      },
      child: Scaffold(
        body: SafeArea(
          child: Center(
            child: Stack(
              alignment: Alignment.center,
              children: [
                Container(
                  constraints: const BoxConstraints(maxWidth: 500),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      _getNotificationBanner(),
                      _auditAppBar(),
                      Expanded(
                        child: SingleChildScrollView(
                          child: Container(
                            margin: const EdgeInsets.symmetric(vertical: 5),
                            padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 15),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                BlocConsumer<LocationCubit, LocationState>(
                                  listener: (context, state) {
                                    if (state is FetchLocationDataError) {
                                      if (state.errorMessage == ApiResponse.INVALID_AUTH) {
                                        Navigator.pushNamedAndRemoveUntil(context, LoginPage.routeName, (route) => false, arguments: true);
                                      }
                                    } else if (state is FetchedLocationData) {
                                      locationList = state.locationList;
                                    }
                                  },
                                  builder: (context, state) {
                                    if (state is FetchedLocationData) {
                                      if (InMemoryAudiData.location != null) {
                                        selectedLocationId = InMemoryAudiData.location!;
                                      }
                                      if (InMemoryAudiData.selectedAuditDescriptionInMemory != null) {
                                        print("Selected audit Description in app memory: $selectedAuditDescription");
                                        selectedAuditDescription = InMemoryAudiData.selectedAuditDescriptionInMemory!;
                                      }
                                      if (InMemoryAudiData.selectedSegmentValueInMemory != null) {
                                        selectedSegmentValue = InMemoryAudiData.selectedSegmentValueInMemory!;
                                      } else {
                                        selectedSegmentValue = 0;
                                      }
                                      return Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        children: [
                                          Container(
                                            padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
                                            width: double.infinity,
                                            decoration: BoxDecoration(
                                              borderRadius: BorderRadius.circular(15),
                                              border: Border.all(color: Theme.of(context).primaryColor),
                                            ),
                                            child: LocationDropdownWidget(
                                              title: AppLocalizations.of(context)!.selectLocation,
                                              locationList: locationList,
                                              defaultLocationId: InMemoryAudiData.location,
                                              onLocationSelected: (selectedId, name, category) {
                                                InMemoryAudiData.location = selectedId;
                                                selectedLocationId = selectedId;
                                              },
                                            ),
                                          ),
                                          const SizedBox(height: 10),
                                          AuditSelectionSection(
                                              auditSchedules: auditSchedules, selectedAuditDescription: selectedAuditDescription, selectedSegmentValue: selectedSegmentValue),
                                          //_getAuditSelection(),
                                          const SizedBox(height: 10),
                                          Row(
                                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                            children: [
                                              _createNewAuditButton(context),
                                              _getAuditList(),
                                            ],
                                          ),
                                          const SizedBox(height: 10),
                                          Container(
                                            child: _auditListUI(),
                                          ),
                                        ],
                                      );
                                    }
                                    return const Center(
                                      child: CircularProgressIndicator(),
                                    );
                                  },
                                ),
                                const SizedBox(
                                  height: 10,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        floatingActionButton: ApplicationUtil.getBackButton(
          context,
          onBackPressed: () {
            getAuditButtonPressed = false;
            auditCubit.resetAuditList();
            auditBloc.add(ResetAuditListData());
            Navigator.of(context).pushNamedAndRemoveUntil(DashboardPage.routeName, (Route<dynamic> route) => false);
          },
        ),
      ),
    );
  }

  _auditAppBar() => Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Container(
            margin: const EdgeInsets.only(left: 10, top: 5),
            child: Text(
              AppLocalizations.of(context)!.scheduledServices,
              style: const TextStyle(color: AppColor.blackTextColor, fontSize: AppConstant.toolbarTitleFontSize, fontWeight: FontWeight.bold),
            ),
          ),
        ],
      );

  _auditListUI() {
    return BlocConsumer<AuditBloc, AuditState>(
      listener: (context, state) {
        if (state is SubmittedAuditListData) {
          String save = 'Submitted';
          if (state.isSaveAudit) {
            save = 'Saved';
          }
          Navigator.of(context).pop();
          ApplicationUtil.showSnackBar(
              context: context,
              message: (InMemoryAudiData.serviceType != null ? InMemoryAudiData.serviceType! : "Service") + " $save Successfully");

          //auditCubit.getAuditListData("", widget.refAuditId);
          //serviceRequestBloc.add(DeleteAuditFromTable(auditId: state.auditId));
        } else if (state is SubmittingAuditListData) {
          String save = 'submitting';
          if (state.isSaveAudit) {
            save = 'saving';
          }
          ApplicationUtil.showLoaderDialog(
              context, "Please wait while $save " + (InMemoryAudiData.serviceType != null ? InMemoryAudiData.serviceType! : "Service"));
        } else if (state is SubmitAuditListDataError) {
          if (state.errorMessage == ApiResponse.INVALID_AUTH) {
            Navigator.pushNamedAndRemoveUntil(context, LoginPage.routeName, (route) => false, arguments: true);
          } else {
            ApplicationUtil.showSnackBar(context: context, message: state.errorMessage);
          }
        }
      },
      builder: (context, state) {
        List<Audit> auditList = [];
        int? auditCount = 0;
        bool isLoading = false;
        /*print(state);*/
        if (state is FetchingAuditListData && state.isFirstFetch) {
          return _loadingIndicator();
        } else if (state is FetchedAuditListData) {
          auditList = state.auditResponse.auditList;
          auditCount = state.auditResponse.auditCount;
        } else if (state is FetchAuditListDataError) {
          return Center(
            child: Text(state.errorMessage),
          );
        } else if (state is FetchingAuditListData) {
          auditList = state.oldList;
          isLoading = true;
        }
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ListView.builder(
              padding: const EdgeInsets.all(0),
              controller: scrollController,
              shrinkWrap: true,
              //physics: const ScrollPhysics(),
              itemCount: (auditList.length + 1),
              itemBuilder: (context, index) {
                if (index < auditList.length) {
                  Audit audit = auditList[index];
                  return getSingleListTile(audit, index);
                } else if (auditList.length < auditCount!) {
                  return isLoading
                      ? _loadingIndicator()
                      : ElevatedButton(
                          onPressed: () {
                            //auditCubit.getAuditListData(0, selectedLocationId, null);
                              auditBloc.add(FetchAuditList(
                                  selectedLocationId, null, _getSelectedValue(InMemoryAudiData.selectedSegmentValueInMemory), InMemoryAudiData.selectedAuditDescriptionInMemory,
                                  refresh: false));
                          },
                          child: const Text("Load More"));
                } else if (auditList.length == auditCount) {
                  if (auditList.isEmpty && getAuditButtonPressed) {
                    return Container(
                      width: double.maxFinite,
                      alignment: Alignment.center,
                      child: const Text(
                        "No Services found",
                        style: TextStyle(fontSize: 20),
                      ),
                    );
                  } else {
                    return Container();
                  }
                } else if (auditList.length >= auditCount) {
                  if (isLoading && auditCount ==0 ) {
                    return _loadingIndicator();
                  }
                  else{
                    return Container();
                  }
                }
                {
                  Timer(const Duration(milliseconds: 30), () {
                    scrollController.jumpTo(scrollController.position.maxScrollExtent);
                  });
                  return _loadingIndicator();
                }
              },
            )
          ],
        );
      },
    );
  }

  Widget _loadingIndicator() {
    return Container(
      padding: const EdgeInsets.all(8.0),
      child: const Center(child: CircularProgressIndicator()),
    );
  }

  getSingleListTile(Audit audit, int index) {
    List auditLocation = [];
    for (var element in audit.auditLocationList!) {
      element.forEach((key, value) {
        if (key == "NAME") {
          auditLocation.add(value);
        }
      });
    }
    return Container(
      margin: const EdgeInsets.only(bottom: 10),
      decoration: BoxDecoration(border: Border.all(color: AppColor.greyBorderColor), borderRadius: BorderRadius.circular(10)),
      child: ListTile(
        leading: audit.serviceType == "TASK"
            ? const FaIcon(
                FontAwesomeIcons.clipboardCheck,
                color: AppColor.primarySwatchColor,
              )
            : const FaIcon(
                FontAwesomeIcons.userSecret,
                color: AppColor.redColor,
              ),
        minLeadingWidth: 1,
        onTap: () {
          AuditEquipmentCubit.auditId = audit.auditId;
          AuditEquipmentCubit.auditServiceType = audit.serviceType;
          AuditEquipmentCubit.location = audit.auditLocationList;
          AuditEquipmentCubit.auditLocation =
              ApplicationUtil.getEndLocationFromLocation(locationId: audit.locationId!, locationMap: audit.auditLocationList);
          serviceRequestBloc.add(SaveEquipmentDataFromAuditStatus(audit.auditEquipmentList, audit.auditId!));
          InMemoryAudiData.serviceType = audit.serviceType == "TASK" ? "Task" : "Audit";
          Navigator.pushNamed(context, EquipmentListPage.routeName,
                  arguments: AuditRefId(
                      index: index,
                      refAuditId: audit.refAuditId!,
                      auditName: audit.description!,
                      auditStatus: audit.status!,
                      serviceType: audit.serviceType == "TASK" ? "Task" : "Audit"))
              .then((value) {
            if (value != null && value == 'refresh') {
              const CircularProgressIndicator();
              auditBloc.add(FetchAuditList(selectedLocationId, null, _getSelectedValue(InMemoryAudiData.selectedSegmentValueInMemory),
                  InMemoryAudiData.selectedAuditDescriptionInMemory,
                  refresh: true));
              if (kDebugMode) {
                print('pop till here');
              }
            }
          });
        },
        title: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              audit.schedule != null ? audit.schedule! : '',
              style: const TextStyle(
                color: Color(0xff101010),
              ),
            ),
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(audit.description.toString()),
            const SizedBox(
              height: 5,
            ),
            Text(
              auditLocation.join(" • ").toString(),
              style: const TextStyle(color: AppColor.blackTextColor),
            ),
          ],
        ),
        trailing: Container(
          padding: const EdgeInsets.all(10),
          decoration: const BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.red,
          ),
          child: Text(
            audit.auditEquipmentList.length.toString(),
            style: const TextStyle(color: Colors.white),
          ),
        ),
      ),
    );
  }

  _createNewAuditButton(BuildContext context) => TextButton(
        onPressed: () {
          if (selectedLocationId.isEmpty) {
            ApplicationUtil.showSnackBar(context: context, message: AppLocalizations.of(context)!.pleaseSelectLocation);
          } else {
            showDialog(
              barrierDismissible: false,
              context: context,
              builder: (BuildContext context) {
                return CreateAdhocAuditDialog(
                  auditBloc: auditBloc,
                  context: context,
                  apiBLoc: apiBLoc,
                  selectedLocationId: selectedLocationId,
                );
              },
            ).then((value) => setState(() {
                  print(value);
                }));
          }
        },
        child: Text(
          AppLocalizations.of(context)!.createAdhocAudit,
          style: const TextStyle(
            color: Colors.black,
            fontSize: 14,
          ),
        ),
        style: ButtonStyle(
          padding: MaterialStateProperty.all(const EdgeInsets.symmetric(vertical: 12, horizontal: 10)),
          shape: MaterialStateProperty.all(RoundedRectangleBorder(
            side: const BorderSide(color: AppColor.greenSentColor, width: 1, style: BorderStyle.solid),
            borderRadius: BorderRadius.circular(15),
          )),
        ),
      );

  _getAuditList() {
    return TextButton(
      onPressed: () {
        getAuditButtonPressed = true;
        print("InMemoryAudiData: \n"
            "selectedSegmentValueInMemory: ${InMemoryAudiData.selectedSegmentValueInMemory}\n"
            "selectedAuditDescriptionInMemory: ${InMemoryAudiData.selectedAuditDescriptionInMemory}"
            "\nlocation: ${InMemoryAudiData.location}");

        if (selectedLocationId.isEmpty) {
          ApplicationUtil.showSnackBar(context: context, message: AppLocalizations.of(context)!.pleaseSelectLocation);
        } else {
          auditBloc.add(ResetAuditListData());
          auditBloc.add(FetchAuditList(selectedLocationId, null, _getSelectedValue(InMemoryAudiData.selectedSegmentValueInMemory),
              InMemoryAudiData.selectedAuditDescriptionInMemory,
              refresh: false));
        }
      },
      child: Text(
        AppLocalizations.of(context)!.getServices,
        style: const TextStyle(color: Colors.black, fontSize: 14),
      ),
      style: ButtonStyle(
        padding: MaterialStateProperty.all(const EdgeInsets.symmetric(vertical: 12, horizontal: 10)),
        shape: MaterialStateProperty.all(RoundedRectangleBorder(
          side: const BorderSide(color: AppColor.greenSentColor, width: 1, style: BorderStyle.solid),
          borderRadius: BorderRadius.circular(15),
        )),
      ),
    );
  }

  _getNotificationBanner() {
    return ApplicationUtil.displayNotificationWidgetIfExist(context, AuditPage.routeName);
  }

  _getSelectedValue(int? selectedValue) {
    switch (selectedValue) {
      case 0:
        return "ALL";
      case 1:
        return "AUDIT";
      case 2:
        return "TASK";
      default:
        return "ALL";
    }
  }

  Widget buildSegment(String text, int index, int groupValue) {
    return Container(
      width: double.maxFinite,
      alignment: Alignment.center,
      child: Text(
        text,
        style: TextStyle(fontSize: 18, color: index == groupValue ? Colors.white : Theme.of(context).primaryColor),
      ),
    );
  }

  Future<List<AuditSchedules>> getAllScheduledServiceDescription(auditSchedules) async {
    auditSchedules = await ApiService().fetchAuditScheduleDescription();
    print(auditSchedules);
    return auditSchedules;
  }

  /* _getAuditSelection() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Theme.of(context).primaryColor),
      ),
      child: Column(
        children: [
          const SizedBox(
            height: 15,
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                child: Text(
                  AppLocalizations.of(context)!.selectServiceType,
                  style: TextStyle(color: Theme.of(context).primaryColor, fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ),
            ],
          ),
          const SizedBox(
            height: 15,
          ),
          const DottedDivider(
            color: AppColor.redColor,
          ),
          const SizedBox(
            height: 15,
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 5),
            child: Container(
              width: double.infinity,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                border: Border.all(color: Theme.of(context).primaryColor),
              ),
              child: CupertinoSlidingSegmentedControl<int>(
                thumbColor: Theme.of(context).primaryColor,
                groupValue: selectedSegmentValue,
                children: {
                  0: buildSegment("All", 0, selectedSegmentValue!),
                  1: buildSegment("Audits", 1, selectedSegmentValue!),
                  2: buildSegment("Tasks", 2, selectedSegmentValue!),
                },
                onValueChanged: (value) {
                  setState(() {
                    selectedSegmentValue = value;
                    InMemoryAudiData.selectedSegmentValueInMemory = selectedSegmentValue;
                    print(selectedSegmentValue);
                  });
                },
              ),
            ),
          ),
          FutureBuilder(
            future: getAllScheduledServiceDescription(auditSchedules),
            builder: (BuildContext context, AsyncSnapshot<dynamic> snapshot) {
              if (snapshot.hasData) {
                List<AuditSchedules> auditsSchedulesListTemporary = [];

                auditsSchedulesListTemporary = snapshot.data;
                if (auditSchedules.isEmpty) {
                  snapshot.data.forEach((element) {
                    auditSchedules.add(element);
                  });
                }
                int res = auditsSchedulesListTemporary.indexWhere((element) => element.description == InMemoryAudiData.selectedAuditDescriptionInMemory);
                if (res >= 0) {
                  selectedAuditDescription = auditsSchedulesListTemporary[res].description!;
                } else {
                  selectedAuditDescription = auditsSchedulesListTemporary[0].description!;
                }
                return AuditDescriptionDropdownWidget(
                  auditDescriptionList: auditsSchedulesListTemporary,
                  defaultAuditDescriptionType: selectedAuditDescription,
                  selectedAuditDescription: (selectedServiceTypeInReturn) {
                    InMemoryAudiData.selectedAuditDescriptionInMemory = selectedServiceTypeInReturn;
                    int res = auditsSchedulesListTemporary.indexWhere((element) => element.description == selectedServiceTypeInReturn);
                    if (res >= 0) {
                      InMemoryAudiData.selectedAuditDescriptionInMemory = auditsSchedulesListTemporary[res].description;
                    } else {
                      InMemoryAudiData.selectedAuditDescriptionInMemory = auditsSchedulesListTemporary[0].description;
                    }
                    selectedAuditDescription = selectedServiceTypeInReturn;
                  },
                );
              } else {
                return Container(
                  padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 5),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          color: Theme.of(context).primaryColor,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: DropdownButton(
                          underline: Container(),
                          isExpanded: true,
                          focusColor: Colors.white,
                          items: <String>[].map((String value) {
                            return DropdownMenuItem<String>(
                              value: value,
                              child: Text(value),
                            );
                          }).toList(),
                          icon: const Icon(
                            Icons.arrow_drop_down,
                            color: Colors.white,
                            size: 35, // Add this
                          ),
                          onChanged: (Object? value) {
                            print("");
                          },
                        ),
                      ),
                    ],
                  ),
                );
              }
            },
          ),
        ],
      ),
    );
  }*/
}
