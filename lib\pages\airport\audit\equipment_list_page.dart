import 'package:alink/bloc/api/api_bloc.dart';
import 'package:alink/bloc/audit/audit_equipment_cubit.dart';
import 'package:alink/bloc/service/service_request_bloc.dart';
import 'package:alink/cubit/pages/repair_detail/repair_detail_api_cubit.dart';
import 'package:alink/data/model/audit_id_model.dart';
import 'package:alink/data/model/audit_response.dart';
import 'package:alink/data/model/barcode_response.dart';
import 'package:alink/data/model/confirm_audit_model.dart';
import 'package:alink/data/model/service_request_detail.dart';
import 'package:alink/data/repository/api_service.dart';
import 'package:alink/pages/airport/audit/confirm_audit_page.dart';
import 'package:alink/pages/airport/service_request/add_service_request_page.dart';
import 'package:alink/pages/auth/login_page.dart';
import 'package:alink/service_locator.dart';
import 'package:alink/util/app_color.dart';
import 'package:alink/util/application_util.dart';
import 'package:alink/util/enums/app_enum.dart';
import 'package:alink/widget/app_expansion_tile.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';


import '../../../bloc/open_serviceRequest_bloc/open_service_request_bloc.dart';
import '../../../bloc/open_serviceRequest_bloc/open_service_request_event.dart';
import '../../../bloc/open_serviceRequest_bloc/open_service_request_state.dart';
import '../../../bloc/user_bloc.dart';
import '../../../data/model/singleton_model.dart';
import '../../../util/app_constant.dart';
import '../../../widget/scan_barcode_button_widget.dart';
import '../service_request/bar_code_result_detail_page.dart';
import '../service_request/service_result_page.dart';
import 'audit_barcode/audit_scanner_page.dart';
import '../../../logger/logger.dart';

class EquipmentListPage extends StatefulWidget {
  static const String routeName = "equipment-list-page";
  final AuditRefId auditRefId;

  const EquipmentListPage({Key? key, required this.auditRefId}) : super(key: key);

  @override
  _EquipmentListPageState createState() => _EquipmentListPageState();
}

class _EquipmentListPageState extends State<EquipmentListPage> {
  static const String className = '_EquipmentListPageState';

  late List<GlobalKey<AppExpansionTileState>> expansionKeyList;
  Logger logger = Logger();
  bool? isConditionAudit = false;
  bool isInValidBarcode = false;
  List<int> list = [];


  bool isInitiallyExpanded = false;

  @override
  void initState() {
    expansionKeyList = [];

    userBloc.add(GetLoggedInUser());

    ServiceRequestBloc.equipmentList = [];
    serviceRequestBloc.add(GetEquipmentDataFromAuditStatus(AuditEquipmentCubit.auditId!));
    super.initState();
  }

  ServiceRequestBloc get serviceRequestBloc => BlocProvider.of<ServiceRequestBloc>(context);

  ApiBloc get apiBloc => BlocProvider.of<ApiBloc>(context);

  RepairDetailApiCubit get repairDetailApiCubit => BlocProvider.of<RepairDetailApiCubit>(context);

  UserBloc get userBloc => BlocProvider.of<UserBloc>(context);

  AuditEquipmentCubit get auditEquipmentCubit => BlocProvider.of<AuditEquipmentCubit>(context);

  @override
  void didChangeDependencies() {
    if (kDebugMode) {
      print('called change depend');
    }
    super.didChangeDependencies();
  }

  @override
  Widget build(BuildContext context) {
    if (kDebugMode) {
      Logger.i("Class Name: " + className);
    }
    return Scaffold(
      body: SafeArea(
        child: Center(
          child: Stack(
            alignment: Alignment.center,
            children: [
              Container(
                constraints: const BoxConstraints(maxWidth: 500),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    ApplicationUtil.displayNotificationWidgetIfExist(context, EquipmentListPage.routeName),
                    _equipmentListAppBar(),
                    _getConditionAuditCheckBox(),
                    Expanded(
                      child: SingleChildScrollView(
                        physics: const BouncingScrollPhysics(
                          parent: AlwaysScrollableScrollPhysics(),
                        ),
                        child: Container(
                          margin: const EdgeInsets.symmetric(vertical: 5),
                          padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 15),
                          child: Column(
                            children: [
                              _equipmentListView(),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              _getValidateButton(ServiceRequestBloc.equipmentList),
              ScanBarCodeButton.getScanBarcodeButton(context, label: AppLocalizations.of(context)!.scanEquipment, onTap: () {
                serviceRequestBloc.add(DismissServiceRequestNotification());
                serviceRequestBloc.add(GetEquipmentDataFromAuditStatus(AuditEquipmentCubit.auditId!));
                Navigator.pushNamed(context, AuditBarCodeScannerPage.routeName);
              }),
              Container(
                alignment: Alignment.bottomRight,
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                child: ApplicationUtil.getBackButton(
                  context,
                  onBackPressed: () {
                    Navigator.of(context).pop();
                  },
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  _equipmentListAppBar() {
    List auditLocation = [];
    for (var element in AuditEquipmentCubit.location!) {
      element.forEach((key, value) {
        if (key == "NAME") {
          auditLocation.add(value);
        }
      });
    }
    String locationId = AuditEquipmentCubit.location!.last['LOCATION_ID'];
    locationId.replaceAll("-", " • ");
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Expanded(
          child: Container(
            margin: const EdgeInsets.only(left: 10, top: 5),
            child: Text(
              auditLocation.join(" • ").toString(),
              softWrap: true,
              style: const TextStyle(color: AppColor.blackTextColor, fontSize: AppConstant.toolbarTitleFontSize, fontWeight: FontWeight.bold),
            ),
          ),
        ),

      ],
    );
  }

  _equipmentListView() {
    return BlocConsumer<ServiceRequestBloc, ServiceRequestState>(listener: (context, state) {
      if (state is BarcodeDataUpdated || state is UpdatedEquipmentFromAuditTable) {
        serviceRequestBloc.add(GetEquipmentDataFromAuditStatus(AuditEquipmentCubit.auditId!));
      }
      if (state is FetchedEquipmentFromAuditTable) {
        if (state.equipmentList != null) {
          state.equipmentList!.toSet();
          if (kDebugMode) {
            print(className);
            print("in fetched equipment from audit table for event listener");
            for (var element in state.equipmentList!) {
              print("TAG-> " + element.tag.toString());
            }
          }
          ServiceRequestBloc.equipmentList = state.equipmentList!;
          if (kDebugMode) {
            print(ServiceRequestBloc.equipmentList.length.toString() + " inside listener " + className);
          }
        }
      }
    }, builder: (context, state) {
      if (state is FetchingEquipmentFromAuditTable) {
        return const Center(child: CircularProgressIndicator());
      } else if (state is FetchedEquipmentFromAuditTable) {
        Logger.i('${ApplicationUtil.getFormattedCurrentDateAndTime()} $className _equipmentListView = > ' "before getting equipmentList ui " +
            ServiceRequestBloc.equipmentList.length.toString());
        Logger.i(
            '${ApplicationUtil.getFormattedCurrentDateAndTime()} $className _equipmentListView = > ' "before getting equipmentList ui " + state.equipmentList!.length.toString());
        if (kDebugMode) {
          print(className);
          print("before getting equipmentList ui");
        }
        for (var element in state.equipmentList!) {
          Logger.i('${ApplicationUtil.getFormattedCurrentDateAndTime()} $className _equipmentListView = > ' "TAG-> " + element.tag.toString());
        }
        for (var element in ServiceRequestBloc.equipmentList) {
          Logger.i('${ApplicationUtil.getFormattedCurrentDateAndTime()} $className _equipmentListView = > ' "SR TAG-> " + element.tag.toString());
        }

        if (kDebugMode) {
          print("SR length before " + ServiceRequestBloc.equipmentList.length.toString());
          print("state equipments length before " + state.equipmentList!.length.toString());
        }
        state.equipmentList!.forEach((element) {
          !list.contains(element.equipmentId) && element.equipmentId != null ? list.add(element.equipmentId!.toInt()): null ;
        },);
        if(list.isNotEmpty){
          context.read<OpenServiceRequestBloc>().add(FetchOpenServiceRequestCount(list, true));
        }

        ServiceRequestBloc.equipmentList = state.equipmentList!;
        if (kDebugMode) {
          print(ServiceRequestBloc.equipmentList.length.toString() + " inside builder at line 218 " + className);
        }

        return _equipmentListUI((ServiceRequestBloc.equipmentList.toSet()).toList());
      } else {
        return _equipmentListUI((ServiceRequestBloc.equipmentList.toSet()).toList());
      }
    });
  }

  _equipmentListUI(List<AuditEquipment> equipmentList) {
    List<AuditEquipment> sortedList = [];
    sortedList.addAll(equipmentList.where((element) => element.status == "Scanned"));
    sortedList.addAll(equipmentList.where((element) => element.status == "Movement"));

    sortedList.sort((b, a) {
      if (a.scannedDateTime == null) {
        return -1;
      } else if (b.scannedDateTime == null) {
        return 1;
      }
      return a.scannedDateTime!.compareTo(b.scannedDateTime!);
    });
    //sortedList.reversed;
    sortedList.addAll(equipmentList.where((element) => element.status == "Unscanned" || element.status == "Inactive"));
    equipmentList = sortedList;
    return ListView.builder(
      padding: const EdgeInsets.all(0),
      shrinkWrap: true,
      physics: const ScrollPhysics(),
      itemCount: equipmentList.length,
      itemBuilder: (context, listTileIndex) {
        AuditEquipment equipment = equipmentList[listTileIndex];
        expansionKeyList.add(GlobalKey());

        if (listIsLast(listTileIndex, equipmentList)) {
          return Column(
            children: [
              getSingleListTileWidget(equipment, listTileIndex),
              const SizedBox(height: 80),
            ],
          );
        }
        return getSingleListTileWidget(equipment, listTileIndex);
      },
    );
  }

  _createServiceRequestWidget(AuditEquipment equipment, int listTileIndex) => Container(
        margin: const EdgeInsets.symmetric(horizontal: 10),
        padding: const EdgeInsets.symmetric(vertical: 10),
        decoration: BoxDecoration(border: Border.all(color: Colors.grey), borderRadius: BorderRadius.circular(15)),
        width: 130,
        height: 100,
        child: InkWell(
          onTap: () async {
            getIt<ServiceType>().type = ServiceRequestType.NOTIFICATION;
            BlocConsumer<ApiBloc, ApiState>(
              listener: (listenerContext, state) {
                if (state is BarcodeApiCalled && getIt<ServiceType>().type == ServiceRequestType.ADD) {
                  apiBloc.add(ResetBarCodeApi());

                  getIt<BarcodeResponse>().barcodeResponseInstance = state.barcodeResponse;
                  if (state.barcodeResponse.serviceRequestCount == null || state.barcodeResponse.serviceRequestCount == 0) {
                    Navigator.pushNamed(context, AddServiceRequestPage.routeName,
                        arguments: ServiceType(
                          type: ServiceRequestType.NOTIFICATION,
                        ));
                  } else {
                    Navigator.pushNamed(context, BardCodeResultPage.routeName,
                        arguments: ServiceType(
                          type: ServiceRequestType.NOTIFICATION,
                        ));
                  }
                } else if (state is BarcodeApiCalled && getIt<ServiceType>().type == ServiceRequestType.MOVE_EQUIPMENT) {
                  Navigator.pushNamed(context, AddServiceRequestPage.routeName, arguments: ServiceType(type: ServiceRequestType.MOVE_EQUIPMENT));
                }
              },
              builder: (context, state) {
                bool isCalling = state is BarcodeApiCalling;

                return AnimatedContainer(
                  duration: const Duration(milliseconds: 500),
                  height: isCalling ? 0 : 50,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      ElevatedButton(
                        onPressed: () {},
                        child: const Text("_okayText"),
                      ),
                    ],
                  ),
                );
              },
            );
            await initBarcodeData(equipment, listTileIndex);
          },
          child: Center(
            child: Column(
              children: [
                FaIcon(
                  FontAwesomeIcons.plus,
                  size: 40,
                  color: Theme.of(context).primaryColor,
                ),
                Text(
                  AppLocalizations.of(context)!.createNewNotification,
                  textAlign: TextAlign.center,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
          ),
        ),
      );

  _getExistingServiceRequestWidget(ServiceRequestDetail serviceRequestDetail) {
    if (kDebugMode) {
      print(serviceRequestDetail.toString());
    }
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 10),
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 5),
      decoration: BoxDecoration(border: Border.all(color: Colors.grey), borderRadius: BorderRadius.circular(15)),
      width: 130,
      height: 100,
      child: InkWell(
        onTap: () {
          if (kDebugMode) {
            print('OnTap Inkwell');
          }
          Navigator.pushNamed(context, BarcodeResultDetailPage.routeName,
              arguments: FetchSingleServiceRequestInAudit(id: serviceRequestDetail.requestId!, isCalledFromAudit: true, isTimedService: false));
        },
        child: Center(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                serviceRequestDetail.description!,
                textAlign: TextAlign.start,
                style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                overflow: TextOverflow.ellipsis,
                maxLines: 4,
              ),
              const SizedBox(
                height: 10,
              ),
              Expanded(
                child: Text(
                  serviceRequestDetail.taskTypeDesc == null ? serviceRequestDetail.requestType! : serviceRequestDetail.taskTypeDesc!,
                  textAlign: TextAlign.start,
                  style: const TextStyle(color: AppColor.greyTextColor, fontSize: 12),
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Flexible(
                    child: Text(
                      serviceRequestDetail.requestId.toString(),
                      textAlign: TextAlign.start,
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ),
                  _getStatusType(serviceRequestDetail),
                ],
              )
            ],
          ),
        ),
      ),
    );
  }

  getSingleListTileWidget(AuditEquipment equipment, int listTileIndex) {
    if(AuditEquipmentCubit.auditServiceType!=AppConstant.SERVICE_TYPE_TASK){
      return Container(
        margin: const EdgeInsets.only(top: 10, left: 5, right: 5),
        decoration: BoxDecoration(border: Border.all(color: AppColor.greyBorderColor), borderRadius: BorderRadius.circular(10)),
        child: AppExpansionTile(
            initiallyExpanded: isInitiallyExpanded,
            onExpansionChanged: (value) async {
              if (value) {
                if (kDebugMode) {
                  print("equipment");
                  print(equipment);
                }
                isInValidBarcode = false;
                for (var element in expansionKeyList) {
                  if (element.currentState != null) {
                    if (expansionKeyList[listTileIndex].currentState != element.currentState) {
                      element.currentState!.collapse();
                    }
                  }
                }
                if (equipment.equipmentId != null) {
                  repairDetailApiCubit.getRepairDetailListForEquipment(equipment.equipmentId, true,false);
                } else {
                  if (equipment.tag != null) {
                    repairDetailApiCubit.resetRepairDataToInitial();
                    try {
                      var response = await ApiService().getEquipmentDetailByBarcodeNumber(equipment.tag!, false);
                      if (response is BarcodeResponse) {
                        repairDetailApiCubit.getRepairDetailListForEquipment(response.equipment?.equipmentId, true,false);
                      } else {
                        isInValidBarcode = true;
                        repairDetailApiCubit.resetRepairDataToInitial();
                        ApplicationUtil.showSnackBar(context: context, message: AppLocalizations.of(context)!.barcodeNotfound + ". Barcode is: ${equipment.tag!}");
                      }
                    } catch (e) {
                      isInValidBarcode = true;
                      repairDetailApiCubit.resetRepairDataToInitial();
                      Logger.e('${ApplicationUtil.getFormattedCurrentDateAndTime()} $e');
                      ApplicationUtil.showSnackBar(context: context, message: AppLocalizations.of(context)!.barcodeNotfound + ". Barcode is: ${equipment.tag!}");
                    }
                  } else {
                    repairDetailApiCubit.resetRepairDataToInitial();
                  }
                }
              }
            },
            key: expansionKeyList[listTileIndex],
            leading: _getLeadingData(equipment, listTileIndex),
            title: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    (equipment.status == "Movement" && equipment.name == null) ? AppLocalizations.of(context)!.newEquipment : '(${equipment.name!})',
                    style: const TextStyle(
                      fontSize: 20,
                      color: Color(0xff101010),
                    ),
                  ),
                ),
                (equipment.isScanned && equipment.equipmentId != null) || equipment.status == "Scanned" || equipment.status == "Movement"
                    ? InkWell(
                    onTap: () {
                      serviceRequestBloc.add(UpdateEquipmentDataFromAuditStatus(AuditEquipmentCubit.auditId!, equipment.tag!, false));
                    },
                    child: Container(
                        margin: const EdgeInsets.only(left: 10),
                        child: const FaIcon(
                          FontAwesomeIcons.timesCircle,
                          size: 25,
                          color: AppColor.redColor,
                        )))
                    : Container(),
                BlocListener<ServiceRequestBloc, ServiceRequestState>(
                  listener: (context, state) {
                    if (state is ServiceRequestSaved) {
                      context.read<OpenServiceRequestBloc>().add(
                            FetchOpenServiceRequestCount(list, true),
                          );
                    }
                  },
                  child: BlocBuilder<OpenServiceRequestBloc, OpenServiceRequestState>(
                    buildWhen: (previous, current) {
                      return previous != current;
                    },
                    builder: (context, state) {
                      int count = 0;
                      if (state is OpenServiceRequestInitial) {
                        return Container();
                      } else if (state is OpenServiceRequestLoading) {
                        return Container();
                      } else if (state is OpenServiceRequestLoaded) {
                        for (var obj in state.count) {
                          if (obj["EQUIPMENT_ID"] == equipment.equipmentId) {
                            count = obj["SERVICE_REQUEST_COUNT"];
                          }
                        }
                        return (equipment.name != null && count != 0) ? _getServiceRequestCountWidget(count) : Container();
                      } else if (state is OpenServiceRequestError) {
                        return Center(child: Text('Error: ${state.message}'));
                      } else {
                        return Container();
                      }
                    },
                  ),
                ),

                /*equipment.status == "Movement"
                    ? InkWell(
                  onTap: () {
                    serviceRequestBloc.add(UpdateEquipmentDataFromAuditStatus(AuditEquipmentCubit.auditId!, equipment.tag!, false));
                  },
                  child: const FaIcon(
                    FontAwesomeIcons.timesCircle,
                    color: AppColor.redColor,
                    size: 25,
                  ),
                )
                    : Container()*/
              ],
            ),
            backgroundColor: Theme.of(context).colorScheme.secondary.withOpacity(0.025),
            children: <Widget>[
              const Divider(
                thickness: 1,
              ),
              _checkAndReturnEquipmentDetailExpansionDetailUI(equipment, listTileIndex)
            ]),
      );
    }
    else{
      return Container(
          margin: const EdgeInsets.only(top: 10, left: 5, right: 5),
          decoration: BoxDecoration(border: Border.all(color: AppColor.greyBorderColor), borderRadius: BorderRadius.circular(10)),
          child:ListTile(
            leading: _getLeadingData(equipment, listTileIndex),
            title: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    (equipment.status == "Movement" && equipment.name == null) ? AppLocalizations.of(context)!.newEquipment : '(${equipment.name!})',
                    style: const TextStyle(
                      fontSize: 20,
                      color: Color(0xff101010),
                    ),
                  ),
                ),
                (equipment.isScanned && equipment.equipmentId != null) || equipment.status == "Scanned" || equipment.status == "Movement"
                    ? InkWell(
                    onTap: () {
                      serviceRequestBloc.add(UpdateEquipmentDataFromAuditStatus(AuditEquipmentCubit.auditId!, equipment.tag!, false));
                    },
                    child: Container(
                        margin: const EdgeInsets.only(left: 10),
                        child: const FaIcon(
                          FontAwesomeIcons.timesCircle,
                          size: 25,
                          color: AppColor.redColor,
                        )))
                    : Container(),
                /*equipment.status == "Movement"
                    ? InkWell(
                  onTap: () {
                    serviceRequestBloc.add(UpdateEquipmentDataFromAuditStatus(AuditEquipmentCubit.auditId!, equipment.tag!, false));
                  },
                  child: const FaIcon(
                    FontAwesomeIcons.timesCircle,
                    color: AppColor.redColor,
                    size: 25,
                  ),
                )
                    : Container()*/
              ],
            ),
            hoverColor: Theme.of(context).colorScheme.secondary.withOpacity(0.025),
          ),
      );
    }

  }

  bool listIsLast(int index, List<AuditEquipment> equipmentList) => index == equipmentList.length - 1;

  _getValidateButton(List<AuditEquipment> equipmentList) {
    List<AuditEquipment> filteredList = [];
    List<AuditEquipment> unScannedList = [];

    for (var element in equipmentList) {
      if ((element.isScanned == true || element.status == "Movement") && (element.isScanned == true || (element.status == "Scanned" || element.status == "Inactive"))) {
        filteredList.add(element);
      } else if (element.isScanned == false) {
        unScannedList.add(element);
      }
    }
    if (kDebugMode) {
      print("filteredList");
      print(filteredList);
    }
    return Container(
      alignment: Alignment.bottomLeft,
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 18),
      child: ElevatedButton(
        style: ButtonStyle(
          backgroundColor: MaterialStateProperty.all(Colors.green),
          shape: MaterialStateProperty.all(
            const RoundedRectangleBorder(
              borderRadius: BorderRadius.all(
                Radius.circular(5),
              ),
            ),
          ),
        ),
        onPressed: () async {
          int scannedEquipment = 0;
          for (var element in equipmentList) {
            if (element.status == "Scanned") {
              scannedEquipment += 1;
            }
          }
          if (scannedEquipment == 0 && isConditionAudit!) {
            ApplicationUtil.showWarningAlertDialog(context,
                title: AppLocalizations.of(context)!.warning, desc: AppLocalizations.of(context)!.conditionAuditMessage, negativeLabel: AppLocalizations.of(context)!.okay);
          } else {
            if (filteredList.isNotEmpty) {
              apiBloc.add(ValidateAuditBarcodes(equipmentList: filteredList, auditId: AuditEquipmentCubit.auditId!));

              var res = await Navigator.pushNamed(context, ConfirmAuditPage.routeName,
                  arguments: ConfirmAuditModel(
                      isEquipmentsValidated: true,
                      refAuditId: widget.auditRefId.refAuditId,
                      auditName: widget.auditRefId.auditName,
                      isConditionAudit: isConditionAudit,
                      scannedUnscannedCount: scannedEquipment,
                  serviceType: widget.auditRefId.serviceType));
              if (res == true) {
                setState(() {});
              }
            } else {
              apiBloc.add(ValidateAuditBarcodes(equipmentList: const [], auditId: AuditEquipmentCubit.auditId!));

              var res = await Navigator.pushNamed(context, ConfirmAuditPage.routeName,
                  arguments: ConfirmAuditModel(
                      isEquipmentsValidated: true,
                      refAuditId: widget.auditRefId.refAuditId,
                      auditName: widget.auditRefId.auditName,
                      isConditionAudit: isConditionAudit,
                      scannedUnscannedCount: scannedEquipment,
                      serviceType: widget.auditRefId.serviceType));
              if (res == true) {
                setState(() {});
              }
            }
          }
        },
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 18),
          child: Text(
            AppLocalizations.of(context)!.validate,
            style: const TextStyle(color: Colors.white),
          ),
        ),
      ),
    );
  }

  _getLeadingIcon(AuditEquipment equipment, int index) {
    if (equipment.name == null || equipment.status == "Movement") {
      return Container(
        padding: EdgeInsets.only(right: 5),
        child: const FaIcon(
          FontAwesomeIcons.solidMagic,
          color: Colors.pink,
          size: 23,
        ),
      );
    } else {
      if (equipment.isScanned || equipment.status == "Scanned") {
        return const FaIcon(
          FontAwesomeIcons.checkCircle,
          color: Color(0xff259B24),
        );
      } else if (!equipment.isScanned) {
        return const FaIcon(FontAwesomeIcons.barcode);
      }
    }
  }

  _getTag(AuditEquipment equipment, int index) {
    if (index == 0 && equipment.isScanned == true) {
      if (equipment.isScanned == true) {
        if (equipment.scannedDateTime == null) {
          return Text(
            equipment.tag!,
            style: const TextStyle(height: 1.2, fontSize: 18, fontWeight: FontWeight.bold, color: AppColor.blackTextColor),
          );
        } else {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Text(
                equipment.tag!,
                style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: AppColor.blackTextColor),
              ),
              Container(
                alignment: Alignment.centerLeft,
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Padding(
                  padding: EdgeInsets.all(5),
                  child: Center(
                    child: Text(
                      'LAST SCANNED',
                      style: TextStyle(fontWeight: FontWeight.bold, fontSize: 10, color: Colors.white),
                    ),
                  ),
                ),
              ),
              const SizedBox(
                height: 2,
              )
            ],
          );
        }
      } else {
        return const SizedBox();
      }
    } else {
        return Text(
          equipment.tag!,
          style: const TextStyle(height: 1.2, fontSize: 18, fontWeight: FontWeight.bold, color: AppColor.blackTextColor),
        );
    }
  }

  _checkAndReturnEquipmentDetailExpansionDetailUI(AuditEquipment equipment, int listTileIndex) {
    return Container(
      height: 130,
      padding: const EdgeInsets.only(top: 5, bottom: 10),
      child: BlocConsumer<RepairDetailApiCubit, RepairDetailState>(
        listener: (context, state) {
          if (state is FetchRepairsListError) {
            if (state.errorMessage == ApiResponse.INVALID_AUTH) {
              Navigator.pushNamedAndRemoveUntil(context, LoginPage.routeName, (route) => false, arguments: true);
            }
          }
        },
        builder: (context, state) {
          if (state is FetchedRepairsListForEquipment) {
            if (state.serviceRequestDetailList.isEmpty) {
              return Row(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  _createServiceRequestWidget(equipment, listTileIndex),
                ],
              );
            }
            return ListView.builder(
              physics: const BouncingScrollPhysics(parent: AlwaysScrollableScrollPhysics()),
              scrollDirection: Axis.horizontal,
              itemCount: state.serviceRequestDetailList.length,
              itemBuilder: (context, index) {
                ServiceRequestDetail serviceRequestDetail = state.serviceRequestDetailList[index];
                if (index == 0) {
                  return Row(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [_createServiceRequestWidget(equipment, listTileIndex), _getExistingServiceRequestWidget(serviceRequestDetail)],
                  );
                } else {
                  return _getExistingServiceRequestWidget(serviceRequestDetail);
                }
              },
            );
          } else {
            return Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                _createServiceRequestWidget(equipment, listTileIndex),
                const SizedBox(
                  width: 50,
                ),
                isInValidBarcode != true ? const CircularProgressIndicator() : Container()
              ],
            );
          }
        },
      ),
    );
  }

  Future<void> initBarcodeData(AuditEquipment equipment, int listTileIndex) async {
    if (equipment.equipmentId == null) {
      try {
        BarcodeResponse barcodeResponse = await ApiService().getEquipmentDetailByBarcodeNumber(equipment.tag!, false);
        getIt<BarcodeResponse>().barcodeResponseInstance = BarcodeResponse(equipment:  barcodeResponse.equipment, auditId: AuditEquipmentCubit.auditId);
        if (getIt<BarcodeResponse>().barcodeResponseInstance is BarcodeResponse) {
          if (getIt<BarcodeResponse>().barcodeResponseInstance!.equipment!.isInActiveEquipment == true) {
            ApplicationUtil.showSnackBar(context: context, message: AppLocalizations.of(context)!.isInActiveEquipmentCannotCreateNotification);
          } else {
            expansionKeyList[listTileIndex].currentState!.collapse();
            Navigator.pushNamed(context, AddServiceRequestPage.routeName, arguments: ServiceType(type: ServiceRequestType.NOTIFICATION));
          }
        } else {
          ApplicationUtil.showSnackBar(context: context, message: AppLocalizations.of(context)!.barcodeNotfound + ". Barcode is: ${equipment.tag!}");
        }
      } catch (e) {
        Logger.e('${ApplicationUtil.getFormattedCurrentDateAndTime()} $e');
        ApplicationUtil.showSnackBar(context: context, message: AppLocalizations.of(context)!.barcodeNotfound + ". Barcode is: ${equipment.tag!}");
      }
    } else {
      getIt<BarcodeResponse>().barcodeResponseInstance = BarcodeResponse(
          equipment: Equipment(
            tag: equipment.tag,
            bomId: equipment.bomId,
            equipmentId: equipment.equipmentId,
            locationId: equipment.locationId,
            name: equipment.name,
            categoryName: equipment.categoryName,
            location: AuditEquipmentCubit.location,
          ),
          auditId: AuditEquipmentCubit.auditId);
      expansionKeyList[listTileIndex].currentState!.collapse();
      Navigator.pushNamed(context, AddServiceRequestPage.routeName, arguments: ServiceType(type: ServiceRequestType.NOTIFICATION));
    }
  }

  _getStatusType(ServiceRequestDetail serviceRequestDetail) {
    if (serviceRequestDetail.requestType != "NOTIFICATION") {
      if (serviceRequestDetail.remainingRequestTime != null) {
        if (serviceRequestDetail.remainingRequestTime! > 0) {
          return Container(
            width: 60,
            padding: const EdgeInsets.symmetric(vertical: 5),
            decoration: BoxDecoration(
              color: AppColor.orangeColor,
              borderRadius: BorderRadius.circular(5),
            ),
            child: Text(
              serviceRequestDetail.status!,
              textAlign: TextAlign.center,
              style: const TextStyle(color: Colors.white, fontSize: 10, fontWeight: FontWeight.bold),
            ),
          );
        } else {
          return Container(
            width: 60,
            padding: const EdgeInsets.symmetric(vertical: 5),
            decoration: BoxDecoration(
              color: AppColor.redColor,
              borderRadius: BorderRadius.circular(5),
            ),
            child: Text(
              AppLocalizations.of(context)!.overdue,
              textAlign: TextAlign.center,
              style: const TextStyle(color: Colors.white, fontSize: 10, fontWeight: FontWeight.bold),
            ),
          );
        }
      }
    } else {
      return Container(
        width: 70,
        padding: const EdgeInsets.symmetric(vertical: 5),
        decoration: BoxDecoration(
          color: const Color.fromRGBO(97, 97, 97, 100),
          borderRadius: BorderRadius.circular(5),
        ),
        child: Text(
          serviceRequestDetail.requestType.toString(),
          textAlign: TextAlign.center,
          style: const TextStyle(color: Colors.white, fontSize: 9, fontWeight: FontWeight.bold),
        ),
      );
    }
    return Container();
  }

  _getConditionAuditCheckBox() {
    return BlocBuilder<UserBloc, UserState>(
      builder: (context, state) {
        if (state is LoginUserFetched && state.userData.MODULE!.contains("ENABLE_CONDITION_AUDIT")) {
          return Container(
            margin: const EdgeInsets.only(left: 0, top: 0),
            child: CheckboxListTile(
              visualDensity: const VisualDensity(horizontal: -4, vertical: -4),
              title: Text("Enable Conditional ${widget.auditRefId.serviceType}"),
              value: isConditionAudit,
              onChanged: (newValue) {
                setState(() {
                  isConditionAudit = newValue;
                });
                if (isConditionAudit!) {
                  ApplicationUtil.showSnackBar(context: context, message: "Enable Conditional ${widget.auditRefId.serviceType}");
                }
              },
              controlAffinity: ListTileControlAffinity.trailing,
            ),
          );
        } else {
          return Container();
        }
      },
    );
  }

  _getLeadingData(AuditEquipment equipment, int listTileIndex) {
    return Row(mainAxisAlignment: MainAxisAlignment.start, crossAxisAlignment: CrossAxisAlignment.center, mainAxisSize: MainAxisSize.min, children: [
      _getLeadingIcon(equipment, listTileIndex),
      equipment.name != null
          ? const SizedBox(
              width: 5,
            )
          : const SizedBox(),
      _getTag(equipment, listTileIndex),
    ]);
  }


  _getServiceRequestCountWidget(int openServiceRequestCount) {
    String countText = '$openServiceRequestCount';
    int countLength = countText.length;
    double containerWidth = countLength == 1 ? 30.0 : 20.0 + (countLength - 1) * 10.0;

    return Container(
        width: containerWidth, // Set width dynamically

        decoration: BoxDecoration(
          shape: countLength == 1 ? BoxShape.circle :BoxShape.rectangle,
          borderRadius:countLength == 1 ? null : BorderRadius.circular(20.0),
          border: Border.all(color: AppColor.primarySwatchColor, width: 2.0), // Circular border
        ),
        margin: const EdgeInsets.only(left: 10),
        child: Center(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 2.0,horizontal: 2.0),
            child: Text('$openServiceRequestCount' ,style: const TextStyle(color: AppColor.primarySwatchColor,fontWeight: FontWeight.bold),),
          ),
        ));
  }


}
