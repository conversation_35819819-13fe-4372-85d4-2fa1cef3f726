import 'dart:async';

import 'package:alink/bloc/api/api_bloc.dart';
import 'package:alink/bloc/repair_db_bloc/repair_bloc.dart';
import 'package:alink/bloc/repair_list_api/repair_list_api_bloc.dart';
import 'package:alink/bloc/service/service_request_bloc.dart';
import 'package:alink/cubit/filter/filter_cubit.dart';
import 'package:alink/cubit/pages/repair_detail/repair_detail_api_cubit.dart';
import 'package:alink/data/model/barcode_response.dart';
import 'package:alink/data/model/filter_data.dart';
import 'package:alink/data/model/repair_service_request.dart';
import 'package:alink/data/model/service_request_detail.dart';
import 'package:alink/database/database.dart';
import 'package:alink/pages/airport/repair/assign_equipment/assign_equipment_page.dart';
import 'package:alink/pages/airport/repair/repair_detail_page.dart';
import 'package:alink/pages/airport/repair/replace_bar_code/confirm_barcode_replace_page.dart';
import 'package:alink/pages/airport/repair/service_request_filter_page.dart';
import 'package:alink/pages/airport/service_request/add_service_request_page.dart';
import 'package:alink/pages/airport/service_request/afs_service_repair_page.dart';
import 'package:alink/pages/auth/login_page.dart';
import 'package:alink/scanner/barcode/ai_scanner_page.dart';
import 'package:alink/service_locator.dart';
import 'package:alink/util/app_color.dart';
import 'package:alink/util/app_constant.dart';
import 'package:alink/util/application_util.dart';
import 'package:alink/util/enums/app_enum.dart';
import 'package:alink/widget/image_view.dart';
import 'package:alink/widget/scan_barcode_button_widget.dart';
import 'package:alink/widget/tooltip_shape.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../data/model/singleton_model.dart';
import '../../../data/repository/api_service.dart';
import '../../../logger/logger.dart';

class RepairListPage extends StatefulWidget {
  static const routeName = "repair-list";

  const RepairListPage({Key? key}) : super(key: key);

  @override
  _RepairListPageState createState() => _RepairListPageState();
}

class _RepairListPageState extends State<RepairListPage> {
  static const String className = '_RepairListPageState';
  var serviceRequestCount = 0;
  final scrollController = ScrollController();
  String query = '';

  void setupScrollController(context) {
    scrollController.addListener(() {
      if (scrollController.position.atEdge) {
        if (scrollController.position.pixels != 0) {
          repairListBloc.add(FetchRepairServiceRequestList(refresh: false, query: query));
        }
      }
    });
  }

  RepairListApiBloc get repairListBloc => BlocProvider.of<RepairListApiBloc>(context);

  ApiBloc get apiBloc => BlocProvider.of<ApiBloc>(context);
  int? customerId = getIt<SharedPreferences>().getInt('customerId');


  RepairBloc get repairBloc => BlocProvider.of<RepairBloc>(context);

  FilterCubit get filterCubit => BlocProvider.of<FilterCubit>(context);

  RepairDetailApiCubit get repairDetailApiCubit => BlocProvider.of<RepairDetailApiCubit>(context);

  @override
  void initState() {
    repairListBloc.add(FetchRepairServiceRequestList(refresh: true, query: query));
    //BlocProvider.of<RepairBloc>(context).add(SendPendingRepairToServer());
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    Logger.i("Class Name: " + className);
    setupScrollController(context);
    return Scaffold(
      body: SafeArea(
        child: Center(
          child: Stack(
            alignment: Alignment.center,
            children: [
              Container(
                constraints: const BoxConstraints(maxWidth: 500),
                child: Column(
                  children: [
                    ApplicationUtil.displayNotificationWidgetIfExist(context, RepairListPage.routeName),
                    _repairAppbar(),
                    Container(
                      margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          BlocBuilder<RepairListApiBloc, RepairListApiState>(
                            builder: (context, state) {
                              //print(state);
                              return Text(
                                AppLocalizations.of(context)!.asOn + ' ' + ApplicationUtil.getFormattedDateFromDateTime(dateTime: DateTime.now()),
                                style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 5),
                        alignment: Alignment.center,
                        child: BlocConsumer<RepairListApiBloc, RepairListApiState>(
                          listener: (context, state) {
                            if (state is FetchedRepairServiceError) {
                              if (state.errorMessage == ApiResponse.INVALID_AUTH) {
                                Navigator.pushNamedAndRemoveUntil(context, LoginPage.routeName, (route) => false, arguments: true);
                              }
                            }
                          },
                          builder: (context, state) {
                            if (state is FetchingRepairServiceRequestList && state.isFirstFetch) {
                              return _loadingIndicator();
                            }

                            List<RepairServiceRequest>? serviceRequestList = [];
                            bool isLoading = false;

                            if (state is FetchingRepairServiceRequestList) {
                              serviceRequestList = state.oldList;
                              if (serviceRequestList.length != serviceRequestCount) {
                                isLoading = true;
                              }
                            } else if (state is FetchedRepairServiceRequestList) {
                              serviceRequestCount = state.repairServiceRequestList.serviceRequestCount;
                              serviceRequestList = state.repairServiceRequestList.repairServiceRequestList;
                            } else if (state is FetchedRepairServiceError) {
                              return Center(
                                child: Padding(
                                  padding: const EdgeInsets.all(20.0),
                                  child: Text(
                                    state.errorMessage,
                                    textAlign: TextAlign.center,
                                    style: const TextStyle(color: AppColor.greyBorderColor, fontSize: 18),
                                  ),
                                ),
                              );
                            }
                            return RefreshIndicator(
                              onRefresh: () async {
                                repairListBloc.add(FetchRepairServiceRequestList(refresh: true, query: query));
                                return await null;
                              },
                              child: serviceRequestList!.isNotEmpty
                                  ? ListView.builder(
                                      padding: const EdgeInsets.all(0),
                                      controller: scrollController,
                                      //itemCount: serviceRequestList.length,
                                      itemCount: (serviceRequestList.length + 1) + (isLoading ? 1 : 0),
                                      itemBuilder: (context, index) {
                                        if (index < serviceRequestList!.length) {
                                          return _getSingleServiceRequestItem(serviceRequestList[index], index);
                                        } else if (index == serviceRequestList.length) {
                                          return const SizedBox(height: 70);
                                        } else {
                                          Timer(const Duration(milliseconds: 30), () {
                                            scrollController.jumpTo(scrollController.position.maxScrollExtent);
                                          });
                                          return _loadingIndicator();
                                        }
                                      },
                                    )
                                  : Center(
                                      child: Text(
                                        AppLocalizations.of(context)!.noServiceFound,
                                      ),
                                    ),
                            );
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              _getScanToRepairButton(),
            ],
          ),
        ),
      ),
      floatingActionButton: ApplicationUtil.getBackButton(
        context,
        onBackPressed: () {
          InMemoryFilterData.clear();
          Navigator.pop(context);
        },
      ),
    );
  }

  bool listIsLast(int index, List<RepairServiceRequest> serviceRequestList) => index == serviceRequestList.length - 1;

  Widget _loadingIndicator() {
    return Container(
      padding: const EdgeInsets.all(8.0),
      child: const Center(child: CircularProgressIndicator()),
    );
  }

  Widget _getSingleServiceRequestItem(RepairServiceRequest serviceRequest, int index) {
    //print("index");
    //print(index);
    return StreamBuilder<List<RepairData>>(
        stream: repairBloc.repairDao.getPendingRepairFromDbAsStream(),
        builder: (context, snapshot) {
          bool isPresentInDb = false;

          if (snapshot.hasData) {
            List<RepairData> repairData = snapshot.data!.where((element) => element.requestId == serviceRequest.requestId).toList();
            if (repairData.isNotEmpty) {
              isPresentInDb = true;
            }
          }

          return InkWell(
            onTap: () async {
              /*if (isPresentInDb) {
                ApplicationUtil.showSnackBar(
                    context: context,
                    message: 'Repair service is still in progress');
              } else {
                await Navigator.pushNamed(context, RepairDetailPage.routeName,
                    arguments: serviceRequest);
              }*/
            },
            child: Card(
              elevation: 2,
              child: ClipPath(
                clipper: ShapeBorderClipper(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(3),
                  ),
                ),
                child: Container(
                  decoration: BoxDecoration(
                    border: isPresentInDb
                        ? const Border(
                            left: BorderSide(color: Colors.orange, width: 5),
                          )
                        : null,
                  ),
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                    child: Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    serviceRequest.equipmentName ?? '',
                                    style: const TextStyle(fontSize: 24, color: AppColor.blackTextColor),
                                  ),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment: CrossAxisAlignment.center,
                                    children: [
                                      FaIcon(
                                        FontAwesomeIcons.solidBarcode,
                                        color: Theme.of(context).primaryColor,
                                      ),
                                      const SizedBox(
                                        width: 5,
                                      ),
                                      Text(
                                        serviceRequest.tag ?? '',
                                        style:
                                            const TextStyle(height: 1.2, fontSize: 18, fontWeight: FontWeight.bold, color: AppColor.blackTextColor),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(
                                    height: 5,
                                  ),
                                  Text(
                                    serviceRequest.taskTypeDesc == null ? serviceRequest.requestType! : serviceRequest.taskTypeDesc!,
                                    overflow: TextOverflow.ellipsis,
                                    style: const TextStyle(fontSize: 16, color: AppColor.blackTextColor),
                                  ),
                                  const SizedBox(
                                    height: 2,
                                  ),
                                ],
                              ),
                            ),
                            Flexible(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      const SizedBox(
                                        width: 10,
                                      ),
                                      Flexible(
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            _getStatusType(serviceRequest),
                                          ],
                                        ),
                                      )
                                    ],
                                  ),
                                ],
                              ),
                            )
                          ],
                        ),
                        const SizedBox(
                          height: 5,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Flexible(
                              child: Text(
                                '${serviceRequest.description}',
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: const TextStyle(fontSize: 16, color: AppColor.blackTextColor),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(
                          height: 5,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: Text(
                                '${ApplicationUtil.getEndLocationFromLocation(locationId: serviceRequest.equipmentLocationId!, locationMap: serviceRequest.location!)['NAME']}',
                                style: const TextStyle(fontSize: 16, color: AppColor.blackTextColor),
                              ),
                            ),
                            _getStatusFromDatabase(isPresentInDb),
                            const SizedBox(width: 10),
                            if (serviceRequest.requestType == 'SCHEDULED')
                              Tooltip(
                                message: AppLocalizations.of(context)!.scheduleService,
                                child: FaIcon(
                                  FontAwesomeIcons.clock,
                                  color: Theme.of(context).primaryColor,
                                  size: 20,
                                ),
                              ),
                            const SizedBox(width: 10),
                            _getImageCount(serviceRequest),
                          ],
                        )
                      ],
                    ),
                  ),
                ),
              ),
            ),
          );
        });
  }

  _getStatusType(RepairServiceRequest serviceRequest) {
    if (serviceRequest.remainingRequestTime != null) {
      if (serviceRequest.remainingRequestTime! > 0) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Container(
              width: 130,
              padding: const EdgeInsets.symmetric(
                vertical: 5,
              ),
              decoration: BoxDecoration(
                color: AppColor.orangeColor,
                borderRadius: BorderRadius.circular(5),
              ),
              child: Text(
                serviceRequest.status!,
                textAlign: TextAlign.center,
                style: const TextStyle(color: Colors.white, fontSize: 13, fontWeight: FontWeight.bold),
              ),
            ),
            const SizedBox(
              height: 5,
            ),
            Container(
              width: 130,
              padding: const EdgeInsets.symmetric(vertical: 2),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor,
                borderRadius: BorderRadius.circular(5),
              ),
              child: Text(
                ApplicationUtil.getHourAndMinuteFromMinute(serviceRequest.remainingRequestTime!) + ' ' + AppLocalizations.of(context)!.remaining,
                textAlign: TextAlign.center,
                style: const TextStyle(color: Colors.white, fontSize: 12, fontWeight: FontWeight.bold),
              ),
            ),
            const SizedBox(
              height: 5,
            ),
            serviceRequest.safetyIssue != null
                ? Image.asset(
                    width: 25,
                    height: 25,
                    "assets/images/hazard.png",
                  )
                : Container(
                    width: 0,
                  ),
          ],
        );
      } else {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Container(
              width: 130,
              padding: const EdgeInsets.symmetric(vertical: 5),
              decoration: BoxDecoration(
                color: AppColor.redColor,
                borderRadius: BorderRadius.circular(5),
              ),
              child: Text(
                AppLocalizations.of(context)!.overdue,
                textAlign: TextAlign.center,
                style: const TextStyle(color: Colors.white, fontSize: 13, fontWeight: FontWeight.bold),
              ),
            ),
            const SizedBox(
              height: 5,
            ),
            Container(
              width: 130,
              padding: const EdgeInsets.symmetric(vertical: 2),
              decoration: BoxDecoration(
                color: AppColor.redColor,
                borderRadius: BorderRadius.circular(5),
              ),
              child: Text(
                ApplicationUtil.getHourAndMinuteFromMinute(serviceRequest.remainingRequestTime!) + ' ' + AppLocalizations.of(context)!.overdue,
                textAlign: TextAlign.center,
                style: const TextStyle(color: Colors.white, fontSize: 12, fontWeight: FontWeight.bold),
              ),
            ),
            const SizedBox(
              height: 5,
            ),
            serviceRequest.safetyIssue != null
                ? Image.asset(
                    width: 25,
                    height: 25,
                    "assets/images/hazard.png",
                  )
                : Container(
                    width: 0,
                  ),
          ],
        );
      }
    }
    return Container();
  }

  _getStatusFromDatabase(bool isPresentInDb) {
    if (isPresentInDb) {
      return Text(AppLocalizations.of(context)!.processing);
    }
    return Container();
  }

  _repairAppbar() => Container(
        margin: const EdgeInsets.only(left: 10),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            BlocBuilder<RepairListApiBloc, RepairListApiState>(
              builder: (context, state) {
                if (state is FetchedRepairServiceRequestList) {
                  RepairServiceRequestCount repairCountResponse;
                  //int repairList = repairCountResponse.repairListCount;
                  int count = state.repairServiceRequestList.serviceRequestCount;
                  return Text(
                    count > 0 ? '${AppLocalizations.of(context)!.serviceRequests} ($count)' : '${AppLocalizations.of(context)!.serviceRequests} ',
                    style: const TextStyle(color: AppColor.blackTextColor, fontSize: AppConstant.toolbarTitleFontSize, fontWeight: FontWeight.bold),
                  );
                }
                return Text(
                  '${AppLocalizations.of(context)!.serviceRequests} ',
                  style: const TextStyle(color: AppColor.blackTextColor, fontSize: AppConstant.toolbarTitleFontSize, fontWeight: FontWeight.bold),
                );
              },
            ),
            Expanded(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Flexible(
                    child: IconButton(
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(minWidth: 50),
                      iconSize: AppConstant.toolbarIconSize,
                      onPressed: () {
                        repairListBloc.add(FetchRepairServiceRequestList(refresh: true, query: query));
                      },
                      icon: FaIcon(
                        FontAwesomeIcons.solidRedo,
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                  ),
                  Flexible(
                    child: IconButton(
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                      onPressed: () async {
                        await Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => const ServiceRequestFilterPage(),
                          ),
                        ).then((value) {
                          if (value != null && value is String) {
                            query = value;
                          }
                        });
                        repairListBloc.add(FetchRepairServiceRequestList(refresh: true, query: query));
                      },
                      icon: FaIcon(
                        FontAwesomeIcons.solidFilter,
                        color: Theme.of(context).primaryColor,
                        size: 20,
                      ),
                    ),
                  ),
                  Flexible(
                    child: _getMenuOption(),
                  )
                ],
              ),
            )
          ],
        ),
      );

  _getMenuOption() => BlocListener<ApiBloc, ApiState>(
        listener: (context, state) {
          if (state is BarcodeApiCalling) {
            SchedulerBinding.instance.addPostFrameCallback((_) {
              ApplicationUtil.showLoaderDialog(context, AppLocalizations.of(context)!.fetching + '..');
            });
          } else if (state is BarcodeApiCalled) {
            apiBloc.add(ResetBarCodeApi());
            Navigator.pop(context);
            getIt<BarcodeResponse>().barcodeResponseInstance = state.barcodeResponse;
            if (getIt<ServiceType>().type == ServiceRequestType.MOVE_EQUIPMENT) {
              Navigator.pushNamed(context, AddServiceRequestPage.routeName, arguments: ServiceType(type: ServiceRequestType.MOVE_EQUIPMENT));
            } else if (getIt<ServiceType>().type == ServiceRequestType.ASSIGN_EQUIPMENT) {
              ApplicationUtil.showSnackBar(context: context, message: AppLocalizations.of(context)!.barcodeAssignedToEquipment);
            } else if (getIt<ServiceType>().type == ServiceRequestType.DIRECT_REPAIR) {
              repairDetailApiCubit.getSingleRepairDetailByEquipmentId(
                  equipmentId: state.barcodeResponse.equipment!.equipmentId, bothRequestType: true, isTimedService: false);
            }
          } else if (state is BarcodeApiError) {
            if (getIt<ServiceType>().type == ServiceRequestType.ASSIGN_EQUIPMENT && state.errorMessage == ApiResponse.BAR_CODE_NOT_FOUND) {
              Navigator.pop(context);

              Navigator.pushNamed(context, AssignEquipmentPage.routeName,
                  arguments: ServiceType(type: ServiceRequestType.ASSIGN_EQUIPMENT, barcodeNumber: state.barcodeNumber));
            } else {
              ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text(state.errorMessage)));
              Navigator.pop(context);
            }
          }
        },
        child: PopupMenuButton<String>(
          padding: const EdgeInsets.all(0),
          offset: const Offset(0, 50),
          shape: const TooltipShape(),
          icon: FaIcon(
            FontAwesomeIcons.ellipsisV,
            color: Theme.of(context).primaryColor,
          ),
          onSelected: (value) async {
            if (value == 'move_equipment') {
              getIt<ServiceType>().type = ServiceRequestType.MOVE_EQUIPMENT;
              var result = await Navigator.pushNamed(
                context,
                AIBarCodeScannerPage.routeName,
              );
              if (result is String && result != "") {
                apiBloc.add(CallBarCodeApi(barcodeNumber: result, isTimedService: false));
              }
              //_createNewServiceRequest();
            } else if (value == 'assign_equipment') {
              getIt<ServiceType>().type = ServiceRequestType.ASSIGN_EQUIPMENT;
              var result = await Navigator.pushNamed(
                context,
                AIBarCodeScannerPage.routeName,
              );
              if (result is String && result != "") {
                apiBloc.add(CallBarCodeApi(barcodeNumber: result, isTimedService: false));
              }
            } else if (value == 'replace_barcode') {
              var barcode = await Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const AIBarCodeScannerPage(
                      title: "Scan old barcode",
                    ),
                  ));
              if (barcode != null && barcode != "") {
                ApplicationUtil.showLoaderDialog(context, 'Please wait while the equipment details are being fetched...');
                dynamic response = await ApiService().getEquipmentDetailByBarcodeNumber(barcode, false);
                if (response is BarcodeResponse) {
                  getIt<BarcodeResponse>().barcodeResponseInstance = BarcodeResponse(
                    equipment: Equipment(
                        tag: response.equipment!.tag,
                        equipmentId: response.equipment!.equipmentId,
                        locationId: response.equipment!.locationId,
                        name: response.equipment!.name,
                        location: response.equipment!.location,
                        categoryName: response.equipment!.categoryName),
                  );
                  getIt<ServiceType>().type = ServiceRequestType.REPLACE_BARCODE;
                  Navigator.of(context).pop();
                  ApplicationUtil.showConfirmDialog(
                    context,
                    title: 'Confirm barcode replacement',
                    description: 'Please confirm the replacement of barcode ${response.equipment!.tag}. Ready to scan new barcode?',
                    continueString: 'Continue',
                    cancelString: 'Cancel',
                    onContinue: () async {
                      var result = await Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const AIBarCodeScannerPage(
                              title: "Scan new barcode",
                            ),
                          ));
                      if (result is String && result != "") {
                        print('--------------' + result);
                        dynamic response = await ApiService().getEquipmentDetailByBarcodeNumber(result, false);
                        if (response is! BarcodeResponse) {
                          // Navigator.pop(context);
                          Navigator.pushNamed(context, ConfirmBarcodeReplacementPage.routeName,
                              arguments: ServiceType(type: ServiceRequestType.REPLACE_BARCODE, barcodeNumber: result));
                        } else {
                          ApplicationUtil.showWarningAlertDialog(
                            context,
                            title: "Duplicate Barcode Assignment Error",
                            desc: "The scanned barcode is already assigned to an existing equipment. Please provide a new barcode to proceed.",
                            negativeLabel: "Cancel",
                          );
                        }
                      }
                    },
                  );
                } else {
                  Navigator.of(context).pop();
                  ApplicationUtil.showSnackBar(context: context, message: "Barcode not found");
                }
              } else {
                Navigator.of(context).pop();
              }
            }
          },
          itemBuilder: (BuildContext context) {
            var options = {
              {'value': 'move_equipment', 'name': AppLocalizations.of(context)!.moveEquipment, 'icon': FontAwesomeIcons.lightPersonCarry},
              {'value': 'assign_equipment', 'name': AppLocalizations.of(context)!.assignNewEquipment, 'icon': FontAwesomeIcons.lightPlusCircle},
              {'value': 'replace_barcode', 'name': AppLocalizations.of(context)!.replaceBarcode, 'icon': FontAwesomeIcons.lightBarcode}
            };
            return options.map((Map<String, dynamic> map) {
              return PopupMenuItem<String>(
                value: map['value'],
                child: Row(
                  children: [
                    FaIcon(
                      map['icon'],
                      color: Theme.of(context).primaryColor,
                    ),
                    const SizedBox(
                      width: 10,
                    ),
                    Text(map['name']),
                  ],
                ),
              );
            }).toList();
          },
        ),
      );

  _getScanToRepairButton() => Container(
        alignment: Alignment.bottomCenter,
        child: BlocConsumer<RepairDetailApiCubit, RepairDetailState>(
          listener: (context, state) async {
            if (state is FetchSingleRepairsDetailEquipmentIdError) {
              if (state.errorMessage == ApiResponse.INVALID_AUTH) {
                Navigator.pushNamedAndRemoveUntil(context, LoginPage.routeName, (route) => false, arguments: true);
              }
            } else if (state is FetchedSingleRepairsDetailByEquipmentId) {
              if (state.serviceRequestDetailList.isEmpty) {
                /// Check if user is afs user then allow user to create service and repar
                bool? afsUser = getIt<SharedPreferences>().getBool('isAfs');
                if (afsUser != null && afsUser == true) {
                  /// GOTO SERVICE PAGE IF THERE IS NO EXISTING SERVICE
                  showAlertDialog(context);
                } else {
                  ApplicationUtil.showSnackBar(context: context, message:customerId == 15 ?  AppLocalizations.of(context)!.thereIsNoServiceRequestWithThisAsset : AppLocalizations.of(context)!.thereIsNoServiceRequestWithThisEquipment);
                }
              } else {
                ServiceRequestDetail serviceRequestDetail = state.serviceRequestDetailList[0];
                RepairServiceRequest serviceRequest = RepairServiceRequest(
                    requestId: serviceRequestDetail.requestId!,
                    customerId: serviceRequestDetail.customerId,
                    equipmentName: serviceRequestDetail.equipmentName,
                    equipmentId: serviceRequestDetail.equipmentId,
                    requestType: serviceRequestDetail.requestType,
                    currentLocationId: serviceRequestDetail.currentLocationId,
                    newLocationId: serviceRequestDetail.newLocationId,
                    taskType: serviceRequestDetail.taskType,
                    taskTypeDesc: serviceRequestDetail.taskTypeDesc,
                    description: serviceRequestDetail.description,
                    createdBy: serviceRequestDetail.createdBy,
                    createdAt: serviceRequestDetail.createdDate,
                    status: serviceRequestDetail.status,
                    remainingRequestTime: serviceRequestDetail.remainingRequestTime,
                    equipmentCategoryName: serviceRequestDetail.equipmentCategoryName,
                    location: serviceRequestDetail.location,
                    equipmentLocationId: serviceRequestDetail.equipmentLocationId,
                    tag: serviceRequestDetail.tag);
                //todo : remove result check and use .whencomplete() method for Navigator.push()
                var result = await Navigator.pushNamed(context, RepairDetailPage.routeName,
                    arguments: RepairDetailPageParams(repairServiceRequest: serviceRequest, isTimedService: false));
                if (result is String) {
                  ApplicationUtil.showSnackBar(context: context, message: result);
                  if (result == AppLocalizations.of(context)!.cancelServiceRequestSuccess) {
                    Future.delayed(const Duration(milliseconds: 500));
                  }
                  repairListBloc.add(
                    FetchRepairServiceRequestList(refresh: true, query: query),
                  );
                  setState(() {});
                }
              }
            }
          },
          builder: (context, state) {
            if (state is FetchingSingleRepairsDetailByEquipmentId) {
              return const SizedBox(width: 30, height: 30, child: CircularProgressIndicator());
            }
            return ScanBarCodeButton.getScanBarcodeButton(
              context,
              onTap: () async {
                getIt<ServiceType>().type = ServiceRequestType.DIRECT_REPAIR;
                var result = await Navigator.pushNamed(context, AIBarCodeScannerPage.routeName) ?? "";
                if (result is String && result != "") {
                  apiBloc.add(CallBarCodeApi(barcodeNumber: result.toString(), isTimedService: false));
                }
              },
              label: AppLocalizations.of(context)!.scanToRepair,
            );
            //return scanToRepairButton();
          },
        ),
      );

/*  scanToRepairButton() => ElevatedButton.icon(
        onPressed: () async {
          getIt<ServiceType>().type = ServiceRequestType.DIRECT_REPAIR;
          var result = await Navigator.pushNamed(
              context, AIBarCodeScannerPage.routeName);
          apiBloc.add(CallBarCodeApi(barcodeNumber: result as String));
        },
        icon: const FaIcon(FontAwesomeIcons.barcode),
        label: Text(
          AppLocalizations.of(context)!.scanToRepair,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
      );*/

  void showAlertDialog(BuildContext ctx) {
    AlertDialog alert = AlertDialog(
      title: Text(AppLocalizations.of(context)!.noServiceFound),
      content: Text(
         customerId == 15 ? AppLocalizations.of(context)!.noServiceRequestDescForStadium : AppLocalizations.of(context)!.noServiceRequestDesc,
        style: const TextStyle(height: 1.2),
      ),
      actions: [
        ElevatedButton(
          child: Text(AppLocalizations.of(context)!.continueString),
          onPressed: () {
            Navigator.of(ctx).pop();
            getIt<ServiceType>().type = ServiceRequestType.DIRECT_REPAIR;

            /// RESET SERVICE REQUEST BLOC
            BlocProvider.of<ServiceRequestBloc>(context).add(ResetServiceRequestBloc());
            Navigator.pushNamed(context, AfsServiceRequestAndRepairPage.routeName, arguments: ServiceType(type: ServiceRequestType.DIRECT_REPAIR))
                .then((shouldRefresh) {
              if (shouldRefresh is bool && shouldRefresh == true) {
                repairListBloc.add(FetchRepairServiceRequestList(refresh: true, query: query));
              }
            });
          },
        ),
        ElevatedButton(
          child: Text(AppLocalizations.of(context)!.cancel),
          onPressed: () {
            Navigator.of(ctx).pop();
          },
        ),
      ],
    );
    showDialog(
      barrierDismissible: false,
      context: ctx,
      builder: (BuildContext context) {
        return alert;
      },
    );
  }

  _getImageCount(RepairServiceRequest serviceRequest) {
    if (serviceRequest.documentCount != null && serviceRequest.documentCount != 0) {
      return InkWell(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            FaIcon(
              FontAwesomeIcons.image,
              size: 20,
              color: Theme.of(context).primaryColor,
            ),
            const SizedBox(
              width: 2,
            ),
            _getImageCountText(serviceRequest),
          ],
        ),
        onTap: () async {
          /*Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ImagePageView(
                serviceRequest: serviceRequest,
              ),
            ),
            */ /*MaterialPageRoute(
              builder: (context) => GalleryExample(
                serviceRequest: serviceRequest,
              ),
            ),*/ /*
          );*/
          ApplicationUtil.showLoaderDialog(context, AppLocalizations.of(context)!.pleaseWait);
          try {
            var response = await ApiService().getServiceRequestListByEquipmentId(serviceRequest.equipmentId!, true, false);
            if (response is List<ServiceRequestDetail>) {
              List<ServiceRequestDetail> serviceRequestDetailList = response;
              var srToShowPhotos;
              serviceRequestDetailList.forEach((element) {
                if (element.requestId == serviceRequest.requestId) {
                  srToShowPhotos = element;
                }
              });
              Navigator.of(context).pop();
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => ImagePageView(
                    serviceRequest: srToShowPhotos,
                  ),
                ),
              );
            } else {
              Navigator.of(context).pop();
              ApplicationUtil.showWarningAlertDialog(context,
                  title: AppLocalizations.of(context)!.errorOccurWhileFetching,
                  desc: AppLocalizations.of(context)!.netWorkErrorTryAgain,
                  negativeLabel: AppLocalizations.of(context)!.okay);
            }
          } catch (e) {
            Navigator.of(context).pop();
            ApplicationUtil.showWarningAlertDialog(context,
                title: AppLocalizations.of(context)!.error,
                desc: AppLocalizations.of(context)!.netWorkErrorTryAgain,
                negativeLabel: AppLocalizations.of(context)!.okay);
          }
        },
      );
    }
    {
      return const SizedBox();
    }
  }

  _getImageCountText(RepairServiceRequest serviceRequest) {
    if (serviceRequest.documentCount != null && serviceRequest.documentCount! > 1) {
      return Text(
        "+" + (serviceRequest.documentCount! - 1).toString(),
        textAlign: TextAlign.center,
        style: TextStyle(
          color: Theme.of(context).primaryColor,
        ),
      );
    } else {
      return const Text('');
    }
  }
}
