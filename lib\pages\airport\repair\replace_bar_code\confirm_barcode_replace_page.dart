import 'package:alink/bloc/service/service_request_bloc.dart';
import 'package:alink/data/model/barcode_response.dart';
import 'package:alink/database/database.dart';
import 'package:alink/logger/logger.dart';
import 'package:alink/pages/dashboard_page.dart';
import 'package:alink/service_locator.dart';
import 'package:alink/util/app_color.dart';
import 'package:alink/util/application_util.dart';
import 'package:alink/util/enums/app_enum.dart';
import 'package:alink/widget/buttons.dart';
import 'package:alink/widget/seperator.dart';
import 'package:drift/drift.dart' as moor;
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:location/location.dart' as gps;
import 'package:shared_preferences/shared_preferences.dart';

class ConfirmBarcodeReplacementPage extends StatefulWidget {
  static const routeName = 'confirm-barcode';
  final ServiceType type;
  const ConfirmBarcodeReplacementPage({Key? key, required this.type})
      : super(key: key);

  @override
  _ConfirmBarcodeReplacementPageState createState() =>
      _ConfirmBarcodeReplacementPageState();
}

class _ConfirmBarcodeReplacementPageState
    extends State<ConfirmBarcodeReplacementPage> {
  static const String className = '_ConfirmBarcodeReplacementPageState';

  BarcodeResponse? barcodeResponse;
  @override
  void initState() {
    barcodeResponse = getIt<BarcodeResponse>().barcodeResponseInstance;
    super.initState();
  }

  ServiceRequestBloc get serviceRequestBloc =>
      BlocProvider.of<ServiceRequestBloc>(context);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Center(
          child: Container(
            constraints: const BoxConstraints(maxWidth: 500),
            child: Column(
              children: [
                ApplicationUtil.displayNotificationWidgetIfExist(
                    context, ConfirmBarcodeReplacementPage.routeName),
                const SizedBox(
                  height: 10,
                ),
                Expanded(
                    child: SingleChildScrollView(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 15),
                    child: Column(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                              vertical: 10, horizontal: 15),
                          width: double.infinity,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(15),
                            border:
                                Border.all(color: Theme.of(context).primaryColor),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const SizedBox(
                                height: 10,
                              ),
                              Text(
                                AppLocalizations.of(context)!
                                    .confirmBarcodeReplacement,
                                style: TextStyle(
                                    color: Theme.of(context).primaryColor,
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold),
                              ),
                              const SizedBox(
                                height: 10,
                              ),
                              const DottedDivider(
                                color: AppColor.redColor,
                              ),
                              const SizedBox(
                                height: 10,
                              ),
                              RichText(
                                text: TextSpan(
                                  style: const TextStyle(
                                    height: 1.2,
                                    fontSize: 16.0,
                                    color: Colors.black,
                                  ),
                                  children: <TextSpan>[
                                    const TextSpan(
                                      text: 'Are you sure you want to associate barcode number ',
                                      style: TextStyle(
                                        color: AppColor.greyTextColor,
                                      ),
                                    ),
                                    TextSpan(
                                      text: "${widget.type.barcodeNumber}",
                                      style: TextStyle(
                                          color: Theme.of(context).primaryColor),
                                    ),
                                    const TextSpan(
                                        text: ' with ',
                                        style: TextStyle(
                                            color: AppColor.greyTextColor)),
                                    TextSpan(
                                      text:
                                          '${barcodeResponse!.equipment!.categoryName} ${barcodeResponse!.equipment!.name} ',
                                      style: TextStyle(
                                          color: Theme.of(context).primaryColor),
                                    ),
                                    const TextSpan(
                                      text: "?",
                                      style: TextStyle(
                                          color: AppColor.greyTextColor),
                                    )
                                  ],
                                ),
                              ),
                              const SizedBox(
                                height: 10,
                              ),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  BlocConsumer<ServiceRequestBloc,
                                      ServiceRequestState>(
                                    listener: (context, state) {
                                      if (state is ServiceRequestError) {
                                        ApplicationUtil.showSnackBar(
                                            context: context,
                                            message: state.error);
                                      }
                                      if (state is ServiceRequestSaved) {
                                        //ApplicationUtil.showToast(msg: 'Service Request Created');
                                        sendDataToServer();
                                        // Navigator.popAndPushNamed(context, DashboardPage.routeName);
                                        Navigator.pushNamedAndRemoveUntil(
                                            context,
                                            DashboardPage.routeName,
                                            (Route<dynamic> route) => false);
                                      }
                                    },
                                    builder: (context, state) {
                                      if (state is ServiceRequestInitial) {
                                        return _buildFinishButton();
                                      } else if (state is ServiceRequestSaving ||
                                          state is ServiceRequestUpdating) {
                                        return _buildProgressButton();
                                      } else {
                                        return _buildFinishButton();
                                      }
                                    },
                                  ),
                                  ElevatedButton(
                                    style: ApplicationUtil.customButtonStyle(context),
                                    onPressed: () {
                                      Navigator.pop(context);
                                    },
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 12, horizontal: 5),
                                      child: Text(
                                        AppLocalizations.of(context)!.cancel,
                                        style: const TextStyle(
                                            color: Colors.white, fontSize: 18),
                                      ),
                                    ),
                                  )
                                ],
                              )
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                ))
              ],
            ),
          ),
        ),
      ),
    );
  }

  void saveToDatabaseAndSendServiceRequest(gps.LocationData? locationData) {
    int? userId = getIt<SharedPreferences>().getInt('userId');

    serviceRequestBloc.add(
      SaveServiceRequest(
        serviceRequestCompanion: ServiceRequestCompanion(
          locationId: moor.Value(barcodeResponse!.equipment!.locationId),
          createdAt: moor.Value(DateTime.now()),
          equipmentBarcodeNumber: moor.Value(barcodeResponse!.equipment!.tag),
          equipmentId:
              moor.Value(barcodeResponse!.equipment!.equipmentId.toString()),
          description: const moor.Value('Replace barcode'),
          createdBy: moor.Value(userId),
          status: const moor.Value(0),
          customerId: moor.Value(barcodeResponse!.equipment!.customerId),
          requestType: const moor.Value("NEW_BARCODE"),
          newBarcode: moor.Value(widget.type.barcodeNumber),
          latitude: moor.Value(locationData?.latitude),
          longitude: moor.Value(locationData?.longitude),
        ),
      ),
    );
  }

  void sendDataToServer() {
    serviceRequestBloc.add(SendPendingServiceRequestToServer());
  }

  Widget _buildFinishButton() {
    return AppButton.getAcceptGreenButton(
        onTap: () async {
          gps.LocationData? locationData;
          Logger.i("Started onTap in confirm_barcode_replace_page");
          var locationDetails = await ApplicationUtil.getGeoLocatorLocation();
          if (locationDetails != null &&
              locationDetails.runtimeType != String) {
            locationData = locationDetails;
          } else if (locationDetails == "WEB") {
            Logger.i("Web platform detected - skipping location validation continue submit");
          } else if (locationDetails == "TIMEOUT") {
            Logger.i("Timeout detected - skipping location validation continue submit");
          }
          saveToDatabaseAndSendServiceRequest(locationData);
        },
        label: AppLocalizations.of(context)!.confirm,
        color: AppColor.greenSentColor);
  }

  Widget _buildProgressButton() {
    return Center(
      child: Container(
        width: 30,
        height: 30,
        margin: const EdgeInsets.only(left: 40),
        child: const CircularProgressIndicator(),
      ),
    );
  }
}
