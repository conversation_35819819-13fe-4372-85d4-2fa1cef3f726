import 'package:alink/cubit/filter/filter_cubit.dart';
import 'package:alink/data/model/customization.dart';
import 'package:alink/data/model/filter_data.dart';
import 'package:alink/data/model/location_detail.dart';
import 'package:alink/data/model/part_item_response.dart';
import 'package:alink/pages/airport/repair/search_item_part_page.dart';
import 'package:alink/service_locator.dart';
import 'package:alink/util/app_color.dart';
import 'package:alink/util/app_constant.dart';
import 'package:alink/util/application_util.dart';
import 'package:alink/widget/location_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import '../../../data/model/singleton_model.dart';
import '../../../logger/logger.dart';

class ServiceRequestFilterPage extends StatefulWidget {
  static const String routeName = "service-request-filter";

  const ServiceRequestFilterPage({Key? key}) : super(key: key);

  @override
  _ServiceRequestFilterPageState createState() => _ServiceRequestFilterPageState();
}

class _ServiceRequestFilterPageState extends State<ServiceRequestFilterPage> {
  static const String className = '_ServiceRequestFilterPageState';

  String? selectedItem;
  UnitType? selectedUnitType;
  TaskType? selectedTaskType;
  List<UnitType>? unitTypeList;
  List<TaskType>? taskTypeList;
  late List<LocationDetail> locationList;
  late TextEditingController barcodeNumberController;
  late TextEditingController partNumberController;
  String selectedLocationId = '';
  bool isSafetyIssue = false;

  @override
  void initState() {
    unitTypeList = [];
    taskTypeList = [];
    locationList = [];
    /*terminalList!.add(Location(name: 'Select', locationId: 'select', gate: []));
    gateList!.add(Gate(name: 'Select', locationId: 'select'));*/
    barcodeNumberController = TextEditingController();
    partNumberController = TextEditingController();
    filterCubit.getFilterData();
    super.initState();
  }

  FilterCubit get filterCubit => BlocProvider.of<FilterCubit>(context);

  @override
  Widget build(BuildContext context) {
    Logger.i("Class Name: " + className);
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        body: SafeArea(
          child: Center(
            child: Container(
              constraints: const BoxConstraints(maxWidth: 500),
              child: Column(
                children: [
                  _filterAppBar(),
                  Expanded(
                    child: SingleChildScrollView(
                      child: BlocConsumer<FilterCubit, FilterState>(
                        listener: (context, state) {
                          if (state is FetchedFilterData) {
                            FilterData filterData = state.filterList;
                            unitTypeList = filterData.unitTypeList;
                            taskTypeList = _getTaskTypeFromCustomization();
                            locationList = filterData.locationList;
                            prePopulateData();
                            /*selectedTerminal = Location(
                                name: selectedTerminal!.name,
                                locationId: selectedTerminal!.locationId,
                                gate: []);*/
                          }
                        },
                        builder: (context, state) {
                          if (state is FetchedFilterData) {
                            return Container(
                              margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                              padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 15),
                              width: double.infinity,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                                border: Border.all(color: Theme.of(context).primaryColor),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  _getLocationWidget(),
                                  const SizedBox(
                                    height: 10,
                                  ),
                                  _getSafetyIssueFilterCheckbox(),
                                  const SizedBox(
                                    height: 10,
                                  ),
                                  _barcodeNumberFilter(),
                                  const SizedBox(
                                    height: 10,
                                  ),
                                  _partNumberFilter(),
                                  const SizedBox(
                                    height: 10,
                                  ),
                                  _unitTypeFilter(),
                                  const SizedBox(
                                    height: 10,
                                  ),
                                  _taskTypeFilter(),
                                  const SizedBox(
                                    height: 10,
                                  ),
                                  _applyFilterButton(),
                                  const SizedBox(
                                    height: 10,
                                  ),

                                  //_clearFilterButton(),
                                ],
                              ),
                            );
                          }
                          return const Center(
                            child: CircularProgressIndicator(),
                          );
                        },
                      ),
                    ),
                  )
                ],
              ),
            ),
          ),
        ),
        floatingActionButton: ApplicationUtil.getBackButton(
          context,
          onBackPressed: () {
            Navigator.of(context).pop('');
          },
        ),
      ),
    );
  }

  _unitTypeDropdown() => Container(
        decoration: BoxDecoration(color: Theme.of(context).primaryColor, borderRadius: BorderRadius.circular(10)),
        child: DropdownButton(
          underline: Container(),
          value: selectedUnitType,
          hint: Container(
            margin: const EdgeInsets.symmetric(horizontal: 10),
            alignment: Alignment.centerLeft,
            child: Text(
              AppLocalizations.of(context)!.select,
              style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
            ),
          ),
          icon: const Icon(
            // Add this
            Icons.arrow_drop_down,
            color: Colors.white,
            size: 35, // Add this
          ),
          isExpanded: true,
          items: unitTypeList!.map(
            (val) {
              return DropdownMenuItem(
                value: val,
                child: Text(val.name!),
              );
            },
          ).toList(),
          selectedItemBuilder: (BuildContext ctxt) {
            return unitTypeList!.map<Widget>((item) {
              return DropdownMenuItem(
                  value: item,
                  child: Container(
                    margin: const EdgeInsets.only(left: 20),
                    child: Text("${item.name}", style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold)),
                  ));
            }).toList();
          },
          onChanged: (value) {
            setState(() {
              selectedUnitType = value as UnitType;
              InMemoryFilterData.unitType = selectedUnitType;
            });
          },
        ),
      );

  _taskTypeDropdown() => Container(
        decoration: BoxDecoration(color: Theme.of(context).primaryColor, borderRadius: BorderRadius.circular(10)),
        child: DropdownButton(
          underline: Container(),
          value: selectedTaskType,
          hint: Container(
            margin: const EdgeInsets.symmetric(horizontal: 10),
            alignment: Alignment.centerLeft,
            child: Text(
              AppLocalizations.of(context)!.select,
              style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
            ),
          ),
          icon: const Icon(
            // Add this
            Icons.arrow_drop_down,
            color: Colors.white,
            size: 35, // Add this
          ),
          isExpanded: true,
          items: taskTypeList!.map(
            (val) {
              return DropdownMenuItem(
                value: val,
                child: Text(val.choiceName!),
              );
            },
          ).toList(),
          selectedItemBuilder: (BuildContext ctxt) {
            return taskTypeList!.map<Widget>((item) {
              return DropdownMenuItem(
                  child: Container(
                    margin: const EdgeInsets.only(left: 20),
                    child: Text("${item.choiceName}", style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold)),
                  ),
                  value: item);
            }).toList();
          },
          onChanged: (value) {
            setState(() {
              selectedTaskType = value as TaskType;
              InMemoryFilterData.taskType = selectedTaskType;
            });
          },
        ),
      );

  _filterAppBar() => Container(
        margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            FaIcon(
              FontAwesomeIcons.solidFilter,
              color: Theme.of(context).primaryColor,
              size: 20,
            ),
            const SizedBox(
              width: 10,
            ),
            Text(
              AppLocalizations.of(context)!.filterServiceRequest,
              style: const TextStyle(color: AppColor.blackTextColor, fontSize: AppConstant.toolbarTitleFontSize, fontWeight: FontWeight.bold),
            ),
          ],
        ),
      );

  _barcodeNumberFilter() => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppLocalizations.of(context)!.barcodeNo,
            style: const TextStyle(color: AppColor.blackTextColor, fontSize: 14),
          ),
          const SizedBox(
            height: 5,
          ),
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              border: Border.all(color: AppColor.greyBorderColor),
            ),
            constraints: const BoxConstraints(minHeight: 30),
            child: TextFormField(
              controller: barcodeNumberController,
              textInputAction: TextInputAction.done,
              keyboardType: TextInputType.multiline,
              style: const TextStyle(fontSize: 18, height: 1.2),
              decoration: InputDecoration(
                border: InputBorder.none,
                contentPadding: const EdgeInsets.all(10),
                hintStyle: TextStyle(
                  color: Colors.grey[500],
                  fontSize: 18,
                ),
                hintText: AppLocalizations.of(context)!.enterBarcodeNo,
              ),
              onChanged: (value) {
                InMemoryFilterData.barcodeNumber = barcodeNumberController.text;
              },
            ),
          )
        ],
      );

  _partNumberFilter() => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppLocalizations.of(context)!.partNo,
            style: const TextStyle(color: AppColor.blackTextColor, fontSize: 14),
          ),
          const SizedBox(
            height: 5,
          ),
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              border: Border.all(color: AppColor.greyBorderColor),
            ),
            constraints: const BoxConstraints(minHeight: 30),
            child: TextFormField(
              controller: partNumberController,
              textInputAction: TextInputAction.done,
              keyboardType: TextInputType.multiline,
              readOnly: true,
              style: const TextStyle(fontSize: 18, height: 1.2),
              decoration: InputDecoration(
                border: InputBorder.none,
                contentPadding: const EdgeInsets.all(10),
                hintStyle: TextStyle(
                  color: Colors.grey[500],
                  fontSize: 18,
                ),
                hintText: AppLocalizations.of(context)!.selectPartno,
              ),
              onTap: () async {
                var res = await Navigator.pushNamed(
                  context,
                  SearchItemPartPage.routeName,
                  arguments: SearchItemParam(isSingleSelection: true),
                );
                PartItemResponse? partItem = res as PartItemResponse?;
                partNumberController.text = partItem!.manufacturerPartNo.toString();
                print('data param');
                print(res);
              },
              onChanged: (value) {
                InMemoryFilterData.partNo = partNumberController.text;
              },
            ),
          )
        ],
      );

  _unitTypeFilter() => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppLocalizations.of(context)!.unitType,
            style: const TextStyle(color: AppColor.blackTextColor, fontSize: 14),
          ),
          const SizedBox(
            height: 5,
          ),
          _unitTypeDropdown(),
        ],
      );

  _taskTypeFilter() => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppLocalizations.of(context)!.taskType,
            style: const TextStyle(color: AppColor.blackTextColor, fontSize: 14),
          ),
          const SizedBox(
            height: 5,
          ),
          _taskTypeDropdown(),
        ],
      );

  _applyFilterButton() => SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          style: ButtonStyle(
            backgroundColor: MaterialStateProperty.all(Colors.green),
            shape: MaterialStateProperty.all(
              const RoundedRectangleBorder(
                borderRadius: BorderRadius.all(
                  Radius.circular(14),
                ),
              ),
            ),
          ),
          onPressed: () {
            //filter data
            String query = '';
            if (selectedLocationId.isNotEmpty) {
              query += '&location=$selectedLocationId';
            }
            if (selectedTaskType != null) {
              query += '&tasktype=${selectedTaskType!.choiceValue}';
            }

            if (selectedUnitType != null) {
              query += '&unittype=${selectedUnitType!.categoryId}';
            }
            if (barcodeNumberController.text.isNotEmpty) {
              query += '&barcode=${barcodeNumberController.text}';
            }
            if (partNumberController.text.isNotEmpty) {
              query += '&partno=${partNumberController.text}';
            }
            if (isSafetyIssue) {
              query += '&safety=Y';
            }
            print(query);
            Navigator.of(context).pop(query.toString());
          },
          child: Container(
              padding: const EdgeInsets.symmetric(vertical: 14),
              child: Text(
                AppLocalizations.of(context)!.applyFilter,
                style: const TextStyle(fontSize: 16),
              )),
        ),
      );

  void prePopulateData() {
    if (InMemoryFilterData.location != null) {
      selectedLocationId = InMemoryFilterData.location!;
    }
    if (InMemoryFilterData.barcodeNumber != null) {
      barcodeNumberController.text = InMemoryFilterData.barcodeNumber!;
    }
    if (InMemoryFilterData.partNo != null) {
      partNumberController.text = InMemoryFilterData.partNo!;
    }
    if (InMemoryFilterData.unitType != null) {
      selectedUnitType = InMemoryFilterData.unitType;
    }
    if (InMemoryFilterData.taskType != null) {
      selectedTaskType = InMemoryFilterData.taskType;
    }
    isSafetyIssue = InMemoryFilterData.safety;
  }

  _clearFilterButton() => SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          style: ButtonStyle(
            backgroundColor: MaterialStateProperty.all(AppColor.redColor),
            shape: MaterialStateProperty.all(
              const RoundedRectangleBorder(
                borderRadius: BorderRadius.all(
                  Radius.circular(14),
                ),
              ),
            ),
          ),
          onPressed: () {
            InMemoryFilterData.clear();
            Navigator.of(context).pop('');
          },
          child: Container(
              padding: const EdgeInsets.symmetric(vertical: 14),
              child: Text(
                AppLocalizations.of(context)!.clearFilter,
                style: const TextStyle(fontSize: 16),
              )),
        ),
      );

  List<TaskType>? _getTaskTypeFromCustomization() {
    List<TaskType> taskTypeList = [];
    Map<String, dynamic>? data = getIt<Customization>().customizationData;
    if (data != null) {
      if (data.containsKey("CHOICE")) {
        if (data['CHOICE'].containsKey('TASK_TYPES')) {
          List<dynamic> serviceRequestChoiceDataList = data['CHOICE']['TASK_TYPES'];
          for (var choiceMap in serviceRequestChoiceDataList) {
            TaskType taskType = TaskType(choiceValue: choiceMap['CHOICE_VALUE'], choiceName: choiceMap['CHOICE_NAME']);
            taskTypeList.add(taskType);
          }
        }
      }
    }
    return taskTypeList;
  }

  _getLocationWidget() {
    return LocationDropdownWidget(
        title: AppLocalizations.of(context)!.selectLocation,
        locationList: locationList,
        defaultLocationId: selectedLocationId,
        onReset: _callReset,
        onLocationSelected: (location, name, category) {
          selectedLocationId = location;
          InMemoryFilterData.location = selectedLocationId;
        });
  }

  _callReset() {
    InMemoryFilterData.clear();
    barcodeNumberController.text = '';
    partNumberController.text = '';
    selectedUnitType = null;
    selectedTaskType = null;
    isSafetyIssue = false;
    setState(() {});
  }

  _getSafetyIssueFilterCheckbox() {
    return ListTile(
      contentPadding: EdgeInsets.zero,
      visualDensity: const VisualDensity(horizontal: -3, vertical: -4),
      title: Text(
        AppLocalizations.of(context)!.safetyIssue,
        style: const TextStyle(fontSize: 18, color: AppColor.blackTextColor, fontWeight: FontWeight.bold),
      ),
      leading: Image.asset(
        width: 25,
        height: 25,
        "assets/images/hazard.png",
      ),
      trailing: Padding(
        padding: const EdgeInsets.all(10),
        child: Checkbox(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(2.0),
            ),
            side: MaterialStateBorderSide.resolveWith(
                  (states) => BorderSide(width: 1.5, color: AppColor.redColor),
            ),
            value: isSafetyIssue,
            checkColor: Colors.white,
            activeColor: AppColor.redColor,
            //fillColor: MaterialStateProperty.resolveWith(getColor),
            onChanged: (bool? value) {
              setState(() {
                isSafetyIssue = value!;
              });
            }),
      ),
    );
  }

  Color getColor(Set<MaterialState> states) {
    const Set<MaterialState> interactiveStates = <MaterialState>{
      MaterialState.pressed,
      MaterialState.hovered,
      MaterialState.focused,
    };

    return AppColor.redColor;
  }
}
