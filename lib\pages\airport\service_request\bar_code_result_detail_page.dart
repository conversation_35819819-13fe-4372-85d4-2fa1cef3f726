import 'dart:convert';

import 'package:alink/bloc/api/api_bloc.dart';
import 'package:alink/data/model/barcode_response.dart';
import 'package:alink/data/model/service_request_detail.dart';
import 'package:alink/data/repository/api_service.dart';
import 'package:alink/pages/airport/service_request/add_service_request_page.dart';
import 'package:alink/pages/auth/login_page.dart';
import 'package:alink/pages/full_image_view.dart';
import 'package:alink/service_locator.dart';
import 'package:alink/util/app_color.dart';
import 'package:alink/util/application_util.dart';
import 'package:alink/util/enums/app_enum.dart';
import 'package:alink/widget/seperator.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';


import '../../../data/model/singleton_model.dart';
import '../../../logger/logger.dart';

class BarcodeResultDetailPage extends StatefulWidget {
  static const routeName = 'bar-code-result-detail';

  //final int? serviceRequestId;
  final FetchSingleServiceRequestInAudit fetchSingleServiceRequestInAudit;

  const BarcodeResultDetailPage({Key? key, required this.fetchSingleServiceRequestInAudit}) : super(key: key);

  @override
  _BarcodeResultDetailPageState createState() => _BarcodeResultDetailPageState();
}

class _BarcodeResultDetailPageState extends State<BarcodeResultDetailPage> {
  static const String className = '_BarcodeResultDetailPageState';
  late FToast fToast;

  ApiBloc get apiBloc => BlocProvider.of<ApiBloc>(context);
  late Equipment equipment;
  Logger logger = Logger();

  @override
  void initState() {
    fToast = FToast();
    fToast.init(context);
    if (widget.fetchSingleServiceRequestInAudit.id == null) {
      int? serviceRequestCount = getIt<BarcodeResponse>().barcodeResponseInstance!.serviceRequestCount;
      equipment = getIt<BarcodeResponse>().barcodeResponseInstance!.equipment!;
      int? serviceRequestId = getIt<BarcodeResponse>().barcodeResponseInstance!.equipment!.equipmentId;
      apiBloc.add(FetchSingleServiceDetail(widget.fetchSingleServiceRequestInAudit.isCalledFromAudit,  false,widget.fetchSingleServiceRequestInAudit.isTimedService,
          requestId: serviceRequestId!));
    } else {
      apiBloc.add(FetchSingleServiceDetail(widget.fetchSingleServiceRequestInAudit.isCalledFromAudit, true,widget.fetchSingleServiceRequestInAudit.isTimedService,
          requestId: widget.fetchSingleServiceRequestInAudit.id));
    }

    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    Logger.i("Class Name: " + className);
    return Scaffold(
      body: SafeArea(
        child: Center(
          child: Container(
            color: Colors.white,
            constraints: const BoxConstraints(maxWidth: 500),
            child: Column(
              children: [
                ApplicationUtil.displayNotificationWidgetIfExist(context, BarcodeResultDetailPage.routeName),
                const SizedBox(
                  height: 10,
                ),
                Expanded(
                  child: BlocConsumer<ApiBloc, ApiState>(
                    listener: (context, state) {
                      if (state is SingleServiceDetailError) {
                        if (state.errorMessage == ApiResponse.INVALID_AUTH) {
                          Navigator.pushNamedAndRemoveUntil(context, LoginPage.routeName, (route) => false, arguments: true);
                        }
                      }
                    },
                    builder: (context, state) {
                      if (state is SingleServiceDetailError) {
                        return Center(child: Text(state.errorMessage));
                      } else if (state is FetchedSingleServiceDetail) {
                        List<ServiceRequestDetail> serviceRequestDetail = state.serviceRequestDetail;
                        return SingleChildScrollView(
                          physics: const BouncingScrollPhysics(parent: AlwaysScrollableScrollPhysics()),
                          child: Column(
                            children: [
                              ListView.builder(
                                  padding: const EdgeInsets.all(0),
                                  shrinkWrap: true,
                                  physics: const ScrollPhysics(),
                                  itemCount: serviceRequestDetail.length,
                                  itemBuilder: (context, index) {
                                    return Container(
                                      margin: const EdgeInsets.symmetric(horizontal: 10),
                                      child: Column(
                                        children: [
                                          Container(
                                            padding: const EdgeInsets.all(10),
                                            width: double.infinity,
                                            decoration: BoxDecoration(
                                              borderRadius: BorderRadius.circular(15),
                                              border: Border.all(color: Theme.of(context).primaryColor),
                                            ),
                                            child: Column(
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              children: [
                                                const SizedBox(
                                                  height: 10,
                                                ),
                                                Row(
                                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                  children: [
                                                    _getServiceNumberAndStatus(serviceRequestDetail[index]),
                                                    _getBarcodeNumber(serviceRequestDetail[index]),
                                                  ],
                                                ),
                                                const SizedBox(
                                                  height: 10,
                                                ),
                                                const DottedDivider(
                                                  color: AppColor.redColor,
                                                ),
                                                const SizedBox(
                                                  height: 10,
                                                ),
                                                _getTerminalDetail(serviceRequestDetail[index]),
                                                //_getEquipmentLocation(serviceRequestDetail),
                                                Column(
                                                  crossAxisAlignment: CrossAxisAlignment.start,
                                                  children: [
                                                    const SizedBox(
                                                      height: 10,
                                                    ),
                                                    serviceRequestDetail[index].taskTypeDesc != null
                                                        ? Text(
                                                            serviceRequestDetail[index].taskTypeDesc!,
                                                            style: const TextStyle(color: AppColor.greyTextColor, fontSize: 20, fontWeight: FontWeight.bold),
                                                          )
                                                        : Text(
                                                            serviceRequestDetail[index].requestType!,
                                                            style: const TextStyle(color: AppColor.greyTextColor, fontSize: 20, fontWeight: FontWeight.bold),
                                                          ),
                                                    Text(
                                                      AppLocalizations.of(context)!.terminalServices,
                                                      style: const TextStyle(color: AppColor.greyTextColor, fontSize: 12, fontWeight: FontWeight.bold),
                                                    ),
                                                  ],
                                                ),
                                                const SizedBox(
                                                  height: 10,
                                                ),
                                                Text(
                                                  '${serviceRequestDetail[index].requestedName != null ? serviceRequestDetail[index].requestedName!.toUpperCase() : ''} REQUESTED',
                                                  style: const TextStyle(color: AppColor.greyTextColor, fontSize: 12, fontWeight: FontWeight.bold),
                                                ),
                                                const SizedBox(
                                                  height: 10,
                                                ),
                                                Text(
                                                  serviceRequestDetail[index].description!,
                                                  style: const TextStyle(color: AppColor.greyTextColor, fontSize: 18, fontWeight: FontWeight.bold),
                                                ),
                                                const SizedBox(
                                                  height: 10,
                                                ),
                                                _getPhotosUI(serviceRequestDetail[index], index),
                                                //_getImages(serviceRequestDetail),
                                                const SizedBox(
                                                  height: 10,
                                                ),
                                                (serviceRequestDetail[index].requestType == "NOTIFICATION" && widget.fetchSingleServiceRequestInAudit.fromIdentifyConcern == true)
                                                    ? _getConvertToServiceRequestButton(serviceRequestDetail[index])
                                                    : const SizedBox(),
                                                const SizedBox(
                                                  height: 10,
                                                ),
                                              ],
                                            ),
                                          ),
                                          const SizedBox(
                                            height: 10,
                                          ),
                                        ],
                                      ),
                                    );
                                  }),
                              const SizedBox(
                                height: 10,
                              ),
                              widget.fetchSingleServiceRequestInAudit.id == null ? _getCreateNewButton() : const SizedBox(),
                            ],
                          ),
                        );
                      } else {
                        return const Center(child: CircularProgressIndicator());
                      }
                    },
                  ),
                ),
                const SizedBox(
                  height: 30,
                ),
              ],
            ),
          ),
        ),
      ),
      floatingActionButton: ApplicationUtil.getBackButton(
        context,
        onBackPressed: () {
          Navigator.pop(context);
        },
      ),
    );
  }

  _getServiceNumberAndStatus(ServiceRequestDetail serviceRequestDetail) => Row(
        children: [
          serviceRequestDetail.requestType == "NOTIFICATION"
              ? const FaIcon(
                  FontAwesomeIcons.solidBell,
                  color: AppColor.orangeColor,
                  size: 30,
                )
              : const FaIcon(
                  FontAwesomeIcons.solidWrench,
                  color: AppColor.redColor,
                  size: 30,
                ),
          const SizedBox(
            width: 10,
          ),
          Text(
            '${serviceRequestDetail.requestId}: ${serviceRequestDetail.status}',
            style: const TextStyle(color: AppColor.greyTextColor, fontSize: 22, fontWeight: FontWeight.bold),
          ),
        ],
      );

  _getBarcodeNumber(ServiceRequestDetail serviceRequestDetail) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        FaIcon(
          FontAwesomeIcons.solidBarcode,
          color: Theme.of(context).primaryColor,
          size: 30,
        ),
        const SizedBox(
          width: 5,
        ),
        Text(
          serviceRequestDetail.tag ?? '',
          style: const TextStyle(height: 1.2, color: AppColor.greyTextColor, fontSize: 22, fontWeight: FontWeight.bold),
        )
      ],
    );
  }

  _getTerminalDetail(ServiceRequestDetail serviceRequestDetail) => Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          //_getCluster(),
          // TODO CLUSTER DETAIL
          Flexible(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  (serviceRequestDetail.equipmentName) != null ? serviceRequestDetail.equipmentName! : 'null',
                  style: const TextStyle(color: AppColor.greyTextColor, fontSize: 16, fontWeight: FontWeight.bold),
                ),
                Text(
                  serviceRequestDetail.equipmentCategoryName!,
                  style: const TextStyle(color: AppColor.greyTextColor, fontSize: 12),
                ),
              ],
            ),
          ),
          _getEquipmentLocation(serviceRequestDetail),
          /*Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _getGateName(serviceRequestDetail),
                style: const TextStyle(
                    color: AppColor.greyTextColor,
                    fontSize: 16,
                    fontWeight: FontWeight.bold),
              ),
              Text(
                AppLocalizations.of(context)!.gate,
                style: TextStyle(color: AppColor.greyTextColor, fontSize: 12),
              ),
            ],
          ),*/
          Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                _getDate(serviceRequestDetail),
                style: const TextStyle(color: AppColor.greyTextColor, fontSize: 16, fontWeight: FontWeight.bold),
              ),
              serviceRequestDetail.requestType == ServiceRequestType.NOTIFICATION.name ? Container() : _getStatusAndTime(serviceRequestDetail.remainingTime!, serviceRequestDetail),
            ],
          ),
        ],
      );

  _getEquipmentLocation(ServiceRequestDetail serviceRequestDetail) {
    if (serviceRequestDetail.location != null) {
      String endLocationName = '', endLocationCategory = '';
      for (var element in serviceRequestDetail.location!) {
        element.forEach((root, value) {
          if (element /*[root]*/ ['LOCATION_ID'] == serviceRequestDetail.equipmentLocationId) {
            endLocationName = element /*[root]*/ ['NAME'];
            endLocationCategory = element['CATEGORY'];
          }
        });
      }

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            endLocationName,
            style: const TextStyle(color: AppColor.greyTextColor, fontSize: 16, fontWeight: FontWeight.bold),
          ),
          Text(
            endLocationCategory,
            style: const TextStyle(color: AppColor.greyTextColor, fontSize: 12),
          ),
        ],
      );
    } else {
      return Container();
    }
  }

  _getPhotos(ServiceRequestDetail serviceRequestDetail, int index2) => Container(
        height: 90,
        child: ListView.builder(
          scrollDirection: Axis.horizontal,
          itemCount: serviceRequestDetail.document!.length,
          itemBuilder: (context, index) {
            var base64Img = serviceRequestDetail.document![index].dOCUMENTBLOB;
            return _getSingleImage(base64Img!, index, index2);
          },
        ),
      );

  _getCreateNewButton() => SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          style: ElevatedButton.styleFrom(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(15.0),
            ),
          ),
          onPressed: () {
            Navigator.pushNamed(context, AddServiceRequestPage.routeName, arguments: ServiceType(type: ServiceRequestType.ADD));
          },
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 14),
            child: Text(
              AppLocalizations.of(context)!.createNew,
              style: const TextStyle(color: Colors.white, fontSize: 18),
            ),
          ),
        ),
      );

  _getSingleImage(String base64img, int index, int index2) => InkWell(
        onTap: () {
          Navigator.pushNamed(context, ImageViewPage.routeName, arguments: ImageWithTag(base64: base64img, index: index));
        },
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 5),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey),
            borderRadius: BorderRadius.circular(10),
          ),
          child: ClipRRect(
            borderRadius: const BorderRadius.all(Radius.circular(10)),
            child: Hero(
              tag: index + index2,
              child: Image.memory(
                base64Decode(base64img),
                fit: BoxFit.cover,
                height: 90,
                width: 90,
              ),
            ),
          ),
        ),
      );

  _getCluster() => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: const [
          Text(
            '99999',
            style: TextStyle(color: AppColor.greyTextColor, fontSize: 20, fontWeight: FontWeight.bold),
          ),
          Text(
            'CLUSTER',
            style: TextStyle(color: AppColor.greyTextColor, fontSize: 12),
          ),
        ],
      );

  String _getDate(ServiceRequestDetail serviceRequestDetail) {
    return ApplicationUtil.getFormattedDateFromDate(createdDate: serviceRequestDetail.createdDate!);
  }

/*
  Future<String> _getUserName() async {
    SharedPreferences pref = getIt<SharedPreferences>();
    return (pref.getString('firstName') ?? '').toUpperCase() +
        ' ' +
        (pref.getString('lastName') ?? '').toUpperCase();
  }*/

  _getStatusAndTime(int remainingTime, ServiceRequestDetail serviceRequestDetail) {
    if (remainingTime > 0 && serviceRequestDetail.status != "PAUSE") {
      return Container(
        width: 110,
        padding: const EdgeInsets.only(top: 6, bottom: 4),
        decoration: BoxDecoration(
          color: Theme.of(context).primaryColor,
          borderRadius: BorderRadius.circular(5),
        ),
        child: Center(
          child: Text(
            ' ${ApplicationUtil.getHourAndMinuteFromMinute(remainingTime)} ${AppLocalizations.of(context)!.remaining} ',
            overflow: TextOverflow.ellipsis,
            style: const TextStyle(color: Colors.white, fontSize: 11),
          ),
        ),
      );
    } else if (serviceRequestDetail.status != "PAUSE") {
      return Container(
        width: 110,
        padding: const EdgeInsets.only(top: 6, bottom: 4),
        decoration: BoxDecoration(
          color: AppColor.redColor,
          borderRadius: BorderRadius.circular(5),
        ),
        child: Center(
          child: Text(
            ' ${ApplicationUtil.getHourAndMinuteFromMinute(remainingTime)} ${AppLocalizations.of(context)!.overdue} ',
            overflow: TextOverflow.ellipsis,
            style: const TextStyle(color: Colors.white, fontSize: 11),
          ),
        ),
      );
    } else if (serviceRequestDetail.status == "PAUSE") {
      return Container(
        width: 110,
        padding: const EdgeInsets.only(top: 6, bottom: 4),
        decoration: BoxDecoration(
          color: AppColor.deepOrangeColor,
          borderRadius: BorderRadius.circular(5),
        ),
        child: Center(
          child: Text(
            ' ${serviceRequestDetail.status}',
            overflow: TextOverflow.ellipsis,
            style: const TextStyle(color: Colors.white, fontSize: 11),
          ),
        ),
      );
    }
  }

  _getPhotosUI(ServiceRequestDetail serviceRequestDetail, int index) {
    if (serviceRequestDetail.document != null && serviceRequestDetail.document!.isNotEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '${AppLocalizations.of(context)!.photos} (${serviceRequestDetail.document!.length})',
            style: const TextStyle(
              fontSize: 12,
              color: AppColor.greyTextColor,
            ),
          ),
          const SizedBox(
            height: 10,
          ),
          _getPhotos(serviceRequestDetail, index),
          const SizedBox(
            height: 10,
          ),
        ],
      );
    }
    return Container();
  }

  /*_getImages(ServiceRequestDetail serviceRequestDetail) => serviceRequestDetail.document!.isNotEmpty
      ? Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(
              height: 20,
            ),
            Text(
              '${AppLocalizations.of(context)!.photos} (${serviceRequestDetail.document!.length})',
              style: const TextStyle(
                fontSize: 12,
                color: AppColor.greyTextColor,
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            _getPhotos(serviceRequestDetail),
          ],
        )
      : Container();*/

  _getConvertToServiceRequestButton(ServiceRequestDetail serviceRequestDetail) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15.0),
          ),
        ),
        onPressed: () async {
          var res = await ApiService().convertToServiceRequest(requestId: serviceRequestDetail.requestId!);
          Logger.i("${ApplicationUtil.getFormattedCurrentDateAndTime()} Convert Notification to Service Request method called. response from server  $res status code: ${res}");
          if (res.statusCode == 204 || res.statusCode == 200) {
            _showSuccessToast(true);
            apiBloc.add(FetchSingleServiceDetail(widget.fetchSingleServiceRequestInAudit.isCalledFromAudit, widget.fetchSingleServiceRequestInAudit.isTimedService, true,
                requestId: widget.fetchSingleServiceRequestInAudit.id));
          } else {
            _showSuccessToast(false);
          }
        },
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 14),
          child: Text(
            AppLocalizations.of(context)!.convertNotificationToServiceRequest,
            style: const TextStyle(color: Colors.white, fontSize: 18),
          ),
        ),
      ),
    );
  }

  _showSuccessToast(bool result) {
    Widget toast = Center(
        child: Container(
      width: MediaQuery.of(context).size.width - 50,
      height: 120,
      padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 12.0),
      decoration: result
          ? BoxDecoration(
              borderRadius: BorderRadius.circular(25.0),
              color: Colors.greenAccent,
            )
          : BoxDecoration(
              borderRadius: BorderRadius.circular(25.0),
              color: Colors.orange,
            ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          result
              ? const Icon(
                  FontAwesomeIcons.checkCircle,
                  size: 50,
                  color: Colors.white,
                )
              : const Icon(
                  FontAwesomeIcons.timesCircle,
                  size: 50,
                  color: Colors.white,
                ),
          const SizedBox(
            width: 12.0,
          ),
          Text(
            result ? "Converted successfully" : "Failed to convert!",
            style: const TextStyle(fontSize: 22, color: Colors.white),
          ),
        ],
      ),
    ));

    fToast.showToast(
      child: toast,
      gravity: ToastGravity.TOP,
      toastDuration: const Duration(seconds: 2),
    );
  }
}
