import 'package:alink/data/model/barcode_response.dart';
import 'package:alink/pages/airport/service_request/add_service_request_page.dart';
import 'package:alink/pages/airport/service_request/bar_code_result_detail_page.dart';
import 'package:alink/service_locator.dart';
import 'package:alink/util/app_color.dart';
import 'package:alink/util/application_util.dart';
import 'package:alink/util/enums/app_enum.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import '../../../data/model/singleton_model.dart';
import '../../../logger/logger.dart';

class BardCodeResultPage extends StatefulWidget {
  static const routeName = 'bar-code-result';
  final ServiceType? serviceRequestData;
  const BardCodeResultPage({Key? key, this.serviceRequestData})
      : super(key: key);

  @override
  _BardCodeResultPageState createState() => _BardCodeResultPageState();
}

class _BardCodeResultPageState extends State<BardCodeResultPage> {
  static const String className = '_BardCodeResultPageState';

  late BarcodeResponse barcodeResponse;
  @override
  void initState() {
    barcodeResponse = getIt<BarcodeResponse>().barcodeResponseInstance!;

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    Logger.i("Class Name: " + className);
    return Scaffold(
        body: SafeArea(
          child: Center(
            child: Container(
              constraints: const BoxConstraints(maxWidth: 500),
              child: Column(
                children: [
                  ApplicationUtil.displayNotificationWidgetIfExist(
                      context, BardCodeResultPage.routeName),
                  Expanded(
                    child: SingleChildScrollView(
                      physics: const BouncingScrollPhysics(
                          parent: AlwaysScrollableScrollPhysics()),
                      child: Container(
                        margin: const EdgeInsets.symmetric(horizontal: 15),
                        child: Column(
                          children: [
                            const SizedBox(
                              height: 30,
                            ),
                            Container(
                              padding: const EdgeInsets.all(10),
                              width: double.infinity,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(15),
                                border: Border.all(color: Colors.black45),
                              ),
                              child: _getServiceRequestExistContent(),
                            ),
                            const SizedBox(
                              height: 30,
                            ),
                            _getCreateNewServiceButton()
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        floatingActionButton: ApplicationUtil.getBackButton(
          context,
          onBackPressed: () {
            Navigator.pop(context);
          },
        ));
  }

  _getViewDetailButton() => Column(
        children: [
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              style: ApplicationUtil.customButtonStyle(context),
              onPressed: () {
                var equipment = getIt<BarcodeResponse>().barcodeResponseInstance!.equipment!;
                Navigator.pushNamed(context, BarcodeResultDetailPage.routeName,arguments:FetchSingleServiceRequestInAudit(id: equipment.equipmentId!,fromIdentifyConcern: true,
                    isTimedService: widget.serviceRequestData!.isTimedService!) );
              },
              child: Container(
                padding:
                    const EdgeInsets.symmetric(vertical: 14, horizontal: 15),
                child: Text(
                  AppLocalizations.of(context)!.viewDetails,
                  style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold),
                ),
              ),
            ),
          )
        ],
      );

  _getCreateNewServiceButton() => SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          style: ElevatedButton.styleFrom(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(15.0),
            ),
          ),
          onPressed: () {
            Navigator.pushNamed(
              context,
              AddServiceRequestPage.routeName,
              arguments: ServiceType(
                type: ServiceRequestType.ADD,
              ),
            );
          },
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 14),
            child: Text(
              AppLocalizations.of(context)!.createNew,
              style: const TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold),
            ),
          ),
        ),
      );

  _getServiceRequestExistContent() => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(
            height: 10,
          ),
          Text(
            AppLocalizations.of(context)!.serviceRequestExist,
            style: const TextStyle(
                color: AppColor.greyTextColor,
                fontSize: 22,
                fontWeight: FontWeight.bold),
          ),
          const SizedBox(
            height: 20,
          ),
          RichText(
            text: TextSpan(
              style: const TextStyle(
                fontSize: 16.0,
                color: Colors.black,
              ),
              children: <TextSpan>[
                const TextSpan(
                  text: 'Number of ',
                  style: TextStyle(color: Colors.grey),
                ),
                TextSpan(
                  text: " Service requests / Notifications ",
                  style: TextStyle(color: Theme.of(context).primaryColor),
                ),
                const TextSpan(
                    text: 'for this equipment: ',
                    style: TextStyle(color: Colors.grey)),
                TextSpan(
                  text: getCount(),
                  style: TextStyle(color: Theme.of(context).primaryColor),
                ),
              ],
            ),
          ),
          const SizedBox(
            height: 30,
          ),
          _getViewDetailButton(),
          const SizedBox(
            height: 15,
          ),
        ],
      );

  String getCount() {
    if (barcodeResponse != null) {
      return barcodeResponse.serviceRequestCount.toString();
    } else {
      return "";
    }
  }
}
