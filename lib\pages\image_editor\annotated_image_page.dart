import 'dart:convert';
import 'dart:typed_data';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:image_painter/image_painter.dart';

class AnnotatedImagePage extends StatefulWidget {
  static const routeName = '/edit-image';
  final base64Img;

  const AnnotatedImagePage({Key? key, this.base64Img}) : super(key: key);

  @override
  _AnnotatedImagePageState createState() => _AnnotatedImagePageState();
}

class _AnnotatedImagePageState extends State<AnnotatedImagePage> {
  String? base64Image;
  final _imageKey = GlobalKey<ImagePainterState>();
  final _key = GlobalKey<ScaffoldState>();

  @override
  Widget build(BuildContext context) {
    base64Image = widget.base64Img;
    return Scaffold(
      appBar: AppBar(
          systemOverlayStyle: const SystemUiOverlayStyle(
              statusBarColor: Colors.white, // transparent status bar
              statusBarIconBrightness: Brightness.dark // dark text for status bar
              ),
          //backgroundColor: Colors.white,
          actions: [
            IconButton(
              icon: const FaIcon(FontAwesomeIcons.check),
              onPressed: returnImage,
            ),
          ]),
      key: _key,
      body: ImagePainter.memory(
        base64Decode(base64Image!),
        key: _imageKey,
        scalable: false,
        defaultColor: Colors.red,
        initialPaintMode: PaintMode.freeStyle,
      ),
    );
  }

  void returnImage() async {
    if (kIsWeb) {
      final image = await _imageKey.currentState!.widgetToImage();
      final image2 = await _imageKey.currentState!.exportImage();
      //final compressedImage = await compressUintList(image!);
      //downloadImage(image);
      //Imp.AppImplementation.downloadImage(image);
      String img64 = base64Encode(image2!);
      Navigator.pop(context, img64);
    } else {
      final image = await _imageKey.currentState!.exportImage();
      final compressedImage = await compressUintList(image!);
      String img64 = base64Encode(compressedImage);
      Navigator.pop(context, img64);
      //downloadImageForMobiles(compressedImage);
    }
  }

  Future<Uint8List> compressUintList(Uint8List uint8List) async {
    var result = await FlutterImageCompress.compressWithList(
      uint8List,
      quality: 50,
    );
    return result;
  }

/* Future<void> downloadImage(Uint8List image) async {
    if (image != null) {
       try {
        final base64data = base64Encode(image);
        js.context.callMethod(
            'compressAndDownloadImage', ['data:image/jpeg;base64,$base64data']);
       */ /* final a =
            html.AnchorElement(href: 'data:image/jpeg;base64,$base64data');
        a.download = '${DateTime.now().millisecondsSinceEpoch}.jpg';
        a.click();
        a.remove();*/ /*
      } catch (e) {
        print(e);
      }
    }
  }*/

/* void downloadImageForMobiles(Uint8List image) async {
    final directory = (await getApplicationDocumentsDirectory()).path;
    await Directory('$directory/sample').create(recursive: true);
    final fullPath =
        '$directory/sample/${DateTime.now().millisecondsSinceEpoch}.png';
    final imgFile = File('$fullPath');
    imgFile.writeAsBytesSync(image);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        backgroundColor: Colors.grey[700],
        padding: const EdgeInsets.only(left: 10),
        content: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text("Image Exported successfully.",
                style: TextStyle(color: Colors.white)),
            TextButton(
                onPressed: () => OpenFile.open("$fullPath"),
                child: Text("Open", style: TextStyle(color: Colors.blue[200])))
          ],
        ),
      ),
    );
  }*/
}
