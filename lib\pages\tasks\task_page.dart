import 'dart:math';

import 'package:alink/data/repository/api_service.dart';
import 'package:alink/database/database.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/painting.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:provider/provider.dart';

import '../../bloc/task/task_bloc.dart';
import '../../data/model/barcode_response.dart';
import '../../data/model/task.dart';
import '../../isolate/http_isolate_task.dart';
import '../../provider/task_type_provider.dart';
import '../../util/app_color.dart';
import '../../util/app_constant.dart';
import '../../util/application_util.dart';
import '../../widget/seperator.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class TaskPage extends StatefulWidget {
  static const routeName = "TaskPage";
  BarcodeResponse? barcodeResponse;
  String? errorMessage;

  TaskPage({super.key, this.barcodeResponse, this.errorMessage});

  @override
  State<TaskPage> createState() => _TaskPageState();
}

class _TaskPageState extends State<TaskPage> {
  List<String> tasklist = ['Select'];
  String selectedValue = '';
   TaskTypeProvider? taskTypeProvider;

  @override
  void initState() {
    selectedValue = tasklist[0];
    taskTypeProvider = Provider.of<TaskTypeProvider>(context, listen: false);
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  TaskBloc get taskBloc => BlocProvider.of<TaskBloc>(context);


  @override
  Widget build(BuildContext context) {
    tasklist.clear();
    tasklist.add("Select");
    var data = taskTypeProvider!.task_types[widget.barcodeResponse!.equipment!.categoryId!];
    data.forEach((element) => {
      tasklist.add(element["TASK_TYPE"])
    });

    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(10.0),
          child: Center(
            child: Container(
              constraints: const BoxConstraints(maxWidth: 500),
              child: widget.errorMessage == null
                  ? Column(
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              AppLocalizations.of(context)!.task,
                              style: const TextStyle(
                                color: AppColor.blackTextColor,
                                fontSize: AppConstant.toolbarTitleFontSize,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            getCard(),
                          ],
                        )
                      ],
                    )
                  : Container(
                      child: Text(widget.errorMessage!),
                    ),
            ),
          ),
        ),
      ),
      floatingActionButton: ApplicationUtil.getBackButton(
        context,
        onBackPressed: () {
          Navigator.pop(context);
        },
      ),
    );
  }

  Widget getCard() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Theme.of(context).primaryColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 10),
          Text(
            AppLocalizations.of(context)!.selectTask,
            style: TextStyle(
              color: Theme.of(context).primaryColor,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 10),
          const DottedDivider(color: AppColor.redColor),
          const SizedBox(height: 10),
          Text(
            '${AppLocalizations.of(context)!.selectTaskToBeCompletedFor}${widget.barcodeResponse!.equipment!.name}',
            style: const TextStyle(
              color: AppColor.greyTextColor,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 10),
          getDropDownWidget(),
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              getSubmitButton(),
              const SizedBox(width: 18),
              getCancelButton(),
            ],
          ),
        ],
      ),
    );
  }

  getDropDownWidget() {
    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor,
              borderRadius: BorderRadius.circular(10),
            ),
            child: DropdownButton(
              hint: Container(
                  margin: const EdgeInsets.only(left: 20),
                  child: Text(
                    selectedValue,
                    style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                  )),
              value: selectedValue,
              underline: Container(),
              isExpanded: true,
              focusColor: Colors.white,
              items: tasklist.map((String value) {
                return DropdownMenuItem<String>(
                  value: value,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0),
                    child: Text(
                      value,
                    ),
                  ),
                );
              }).toList(),
              selectedItemBuilder: (BuildContext ctx) {
                return tasklist.map<Widget>((item) {
                  return DropdownMenuItem(
                      child: Container(
                        margin: const EdgeInsets.only(left: 20),
                        child: Text(item, style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold)),
                      ),
                      value: item);
                }).toList();
              },
              icon: const Icon(
                Icons.arrow_drop_down,
                color: Colors.white,
                size: 35, // Add this
              ),
              onChanged: (Object? value) {
                setState(() {
                  selectedValue = value.toString();
                });
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget getSubmitButton() {

    return BlocListener<TaskBloc, TaskState>(
      listener: (context, state) {
        if (state is SavingTaskInDatabase) {
          ApplicationUtil.showLoaderDialog(context, "Please wait");
        } else if (state is SavedTaskInDatabase) {
          // Add the next event after successfully saving the task
          debugPrint("Saving task in database");
          HttpIsolateTask().sendTaskThroughIsolate();
          Navigator.pop(context); // Close any dialogs or loaders
          Navigator.pop(context);
          ApplicationUtil.showSnackBar(context: context, message: "Task Completed successfully");
          debugPrint("Saving task in database");
        } else if (state is SaveTaskInDatabaseError) {
          Navigator.pop(context); // Close the loader dialog if open
          showDialog(
            context: context,
            builder: (context) {
              return AlertDialog(
                title: const Text("Alert"),
                content: Text(state.errorMessage),
                actions: [
                  ElevatedButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    child: const Text("OK"),
                  ),
                ],
              );
            },
          );
        }
      },
      child: SizedBox(
        width: 140,
        child: ElevatedButton(
          style: ButtonStyle(
            padding: MaterialStateProperty.all(
              const EdgeInsets.symmetric(vertical: 14, horizontal: 20),
            ),
            backgroundColor: MaterialStateProperty.all(AppColor.greenSentColor),
            shape: MaterialStateProperty.all<RoundedRectangleBorder>(
              RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(15.0),
              ),
            ),
          ),
          onPressed: () {
            if(selectedValue != "Select"){
              taskBloc.add(
                SaveTaskinDataBase(
                  task:Task(
                    TASK_ID:Random().nextInt(100),
                    EQUIPMENT_ID: widget.barcodeResponse!.equipment!.equipmentId!,
                    TASK_TYPE: selectedValue
                  )
                ),
              );
            }else{
              showDialog(
                context: context,
                builder: (context) {
                  return AlertDialog(
                    title: const Text("Alert"),
                    content: const Text("Please select a task type"),
                    actions: [
                      ElevatedButton(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        child: const Text("OK"),
                      ),
                    ],
                  );
                },
              );
            }

          },
          child: Text(
            AppLocalizations.of(context)!.submit,
            style: const TextStyle(color: Colors.white, fontSize: 16),
          ),
        ),
      ),
    );


  }

  Widget getCancelButton() {
    return SizedBox(
      width: 140,
      child: ElevatedButton(
        style: ButtonStyle(
          padding: MaterialStateProperty.all(
            const EdgeInsets.symmetric(vertical: 14, horizontal: 20),
          ),
          backgroundColor: MaterialStateProperty.all(AppColor.primarySwatchColor),
          shape: MaterialStateProperty.all<RoundedRectangleBorder>(
            RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(15.0),
            ),
          ),
        ),
        onPressed: () async {
          Navigator.pop(context);
        },
        child: Text(
          AppLocalizations.of(context)!.cancel,
          style: const TextStyle(color: Colors.white, fontSize: 16),
        ),
      ),
    );
  }
}
