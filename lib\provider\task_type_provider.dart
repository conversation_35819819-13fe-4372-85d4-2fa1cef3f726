import 'package:alink/data/repository/api_service.dart';
import 'package:flutter/cupertino.dart';

class TaskTypeProvider extends ChangeNotifier{
  Map<String, dynamic> _taskTypes = {};

  Map<String, dynamic> get task_types => _taskTypes;

  Future<void> getTaskTypes() async {
    final result = await ApiService().getTaskTypeList();
    if (result.containsKey('error')) {
      debugPrint('Error fetching task types: ${result['error']}');
      _taskTypes = {"error": result['error']};
    } else {
      _taskTypes = result;
    }
    notifyListeners();
  }
}
