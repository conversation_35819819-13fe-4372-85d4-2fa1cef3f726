class AppConstant {
  static String INTERNET="1";
  static const BARCODE = "Barcode Demo";
  static const IMAGE_CAPTURE = "Image capture";
  static const LIST_DETAIL = "List Detail Screen";
  static const DASHBOARD = "DASHBOARD";
  static const String MANUAL_BAR_CODE_READ = "MANUAL";
  static const baseUrl = "https://jsonkeeper.com/b/OOPC";
  static const CAMERA = "Camera";
  static const ASK_EVERYTIME = "Ask Everytime";
  static const LIMIT = 10;
  static const PART_LIST_LIMIT = 20;
  static const double toolbarTitleFontSize = 22;
  static const double toolbarIconSize = 20;
  //static String BASE_URL = "http://192.168.98.240:3000";
  static String BASE_URL = "https://api.alinkhub.com";
  static String BASE_URL_NO_HTTPS = "api.alinkhub.com";
  static const RESET_PASSWORD_SENT_MSG = "An email containing instructions to reset password is sent. Please check your inbox.";
  static const VERBOSE = "Verbose";
  static const DEBUG = "Debug";
  static const INFO = "Info";
  static const WARNING = "Warning";
  static const ERROR = "Error";
  static const DB_VERSION="1.0";
  static const SERVICE_TYPE_TASK="TASK";
  static const SERVICE_TYPE_AUDIT="AUDIT";
  static List<AppUrl> baseUrlList = [
    AppUrl(url: 'https://api.alinkhub.com', description: 'Production', isActive: true),
    AppUrl(url: 'https://api.sbox.alinkhub.com', description: 'Sandbox', isActive: false),
    /*AppUrl(
      url: 'http://**************:3000',
      description: 'Venk',
      isActive: false,
    ),*/
    /*AppUrl(
      url: 'http://**************:3000',
      description: 'Development',
      isActive: false,
    ),*/
    /*AppUrl(
      url: 'http://*************:3000',
      description: 'Temporary',
      isActive: false,
    ),*/
  ];
}

class Module {
  static const SERVICE_REQUEST = "SERVICE_REQUEST";
  static const EVENT_TASKS = "EVENT_TASKS";
}

class ContactHelper {
  static const SUPPORT_EMAIL = "<EMAIL>";
  static const SUPPORT_SUBJECT = "Log Report";
}

class AppUrl {
  String url;
  String description;
  bool isActive;
  AppUrl({required this.url, required this.description, required this.isActive});
}
