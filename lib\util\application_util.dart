import 'dart:io';
import 'dart:async';

import 'package:alink/bloc/api/api_bloc.dart';
import 'package:alink/bloc/repair_db_bloc/repair_bloc.dart';
import 'package:alink/bloc/repair_list_api/repair_list_api_bloc.dart';
import 'package:alink/bloc/service/service_request_bloc.dart';
import 'package:alink/cubit/internet/internet_cubit.dart';
import 'package:alink/data/model/audit_location.dart';
import 'package:alink/pages/airline/audit/lopa_audit_page.dart';
import 'package:alink/pages/airline/model/lopa_model.dart';
import 'package:alink/pages/airline/repair/lopa_repair_page.dart';
import 'package:alink/pages/airport/audit/equipment_list_page.dart';
import 'package:alink/pages/airport/repair/repair_list_page.dart';
import 'package:alink/pages/airport/timed_service_requests/timed_service_requests.dart';
import 'package:alink/pages/dashboard_page.dart';
import 'package:alink/service_locator.dart';
import 'package:alink/util/app_color.dart';
import 'package:alink/util/app_constant.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_email_sender/flutter_email_sender.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:geolocator/geolocator.dart' as geolocator;
import 'package:intl/intl.dart';
import 'package:location/location.dart' as gps;
import 'package:location/location.dart';

import 'package:package_info_plus/package_info_plus.dart';
import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../logger/logger.dart';
import 'enums/app_enum.dart';

class ApplicationUtil {
  static Logger logger = Logger();
 static int? customer_id = getIt<SharedPreferences>().getInt('customerId');

  static getBackButton(context, {required VoidCallback onBackPressed}) {
    return FloatingActionButton(
      onPressed: () => onBackPressed(),
      child: Container(margin: const EdgeInsets.only(right: 5), child: const FaIcon(FontAwesomeIcons.chevronLeft)),
    );
  }

  static MaterialColor createMaterialColor(Color color) {
    List strengths = <double>[.05];
    final swatch = <int, Color>{};
    final int r = color.red, g = color.green, b = color.blue;

    for (int i = 1; i < 10; i++) {
      strengths.add(0.1 * i);
    }
    for (var strength in strengths) {
      final double ds = 0.5 - strength;
      swatch[(strength * 1000).round()] = Color.fromRGBO(
        r + ((ds < 0 ? r : (255 - r)) * ds).round(),
        g + ((ds < 0 ? g : (255 - g)) * ds).round(),
        b + ((ds < 0 ? b : (255 - b)) * ds).round(),
        1,
      );
    }
    return MaterialColor(color.value, swatch);
  }

  static String getGreetingMessageBaseOnTime(BuildContext context) {
    var hour = DateTime.now().hour;
    if (hour < 12) {
      return '${AppLocalizations.of(context)!.goodMorning} ';
    }
    if (hour < 18) {
      return '${AppLocalizations.of(context)!.goodAfternoon} ';
    }
    return '${AppLocalizations.of(context)!.goodEvening} ';
  }

  static displayNotificationWidgetIfExist(BuildContext context, String routeName) {
    print('displayNotificationWidgetIfExist');
    /* _serviceRequestNotification(),
    _adminMessageNotification(),*/
    InternetCubit netCubit = BlocProvider.of<InternetCubit>(context);
    ServiceRequestBloc serviceRequestBloc = BlocProvider.of<ServiceRequestBloc>(context, listen: false);
    final serviceRequestState = BlocProvider.of<ServiceRequestBloc>(context, listen: true).state;
    final repairState = BlocProvider.of<RepairBloc>(context, listen: true).state;
    return BlocConsumer<InternetCubit, InternetState>(
      listener: (context, state) {
        print(state);
        if (state is InternetConnected) {
          if (routeName == DashboardPage.routeName) {
            print('===========INTERNET STATUS DASHBOARD=========');
            serviceRequestBloc.add(SendPendingServiceRequestToServer());
          }
        }
      },
      builder: (context, state) {
        print('repairState');
        print(repairState);
        if (state is InternetConnected) {
          AppConstant.INTERNET = "1";
          //Service Request Notification
          if (serviceRequestState is ServiceRequestNotificationSent &&
              (routeName == DashboardPage.routeName || routeName == LopaAuditPage.routeName || routeName == LopaRepairPage.routeName)) {
            return _serviceRequestNotification(kToolbarHeight + 25, serviceRequestState, context);
          } else if (serviceRequestState is ServiceRequestNotificationSent && routeName == EquipmentListPage.routeName) {
            return _serviceRequestNotification(kToolbarHeight + 25, serviceRequestState, context);
          } else if (repairState is SentPendingRepairToServer &&
              routeName == RepairListPage.routeName &&
              getIt<ServiceType>().type != ServiceRequestType.SCHEDULED) {
            BlocProvider.of<RepairListApiBloc>(context).add(FetchRepairServiceRequestList(refresh: true));
            BlocProvider.of<ApiBloc>(context).add(GetServiceRequestCount());
            return _pendingRepairNotification(kToolbarHeight + 25, context);
          } else if (repairState is SentPendingRepairToServer &&
              routeName == TimedServiceRequests.routeName &&
              getIt<ServiceType>().type == ServiceRequestType.SCHEDULED) {
            String query = "&requestType=SCHEDULED";
            if (InMemoryAudiData.timedServiceLocation != null || InMemoryAudiData.timedServiceLocation != "") {
              query = "&location=${InMemoryAudiData.timedServiceLocation}&requestType=SCHEDULED";
            }
            BlocProvider.of<RepairListApiBloc>(context).add(
              FetchRepairServiceRequestList(refresh: true, query: query),
            );
            return _pendingRepairNotification(kToolbarHeight + 25, context);
          } else if (serviceRequestState is ServiceRequestErrorNotificationSent) {
            return _errorNotification(kToolbarHeight + 25, serviceRequestState.message, context);
          } else {
            return SizedBox(
              height: MediaQuery.of(context).padding.top + 10,
            );
          }
        } else if (state is InternetDisconnected || state is InternetLoading) {
          AppConstant.INTERNET = "0";
          return networkMessageNotification(kToolbarHeight + 25, context);
          // return _networkMessageNotification();
        }

        return Container();
      },
    );
  }

  static networkMessageNotification(double networkMessageNotificationHeight, BuildContext context) {
    return Material(
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        alignment: Alignment.centerLeft,
        width: double.infinity,
        color: const Color(0xffB71C1C),
        height: networkMessageNotificationHeight,
        child: Container(
            margin: const EdgeInsets.only(left: 10, top: 20),
            padding: const EdgeInsets.all(10),
            child: Text(
              AppLocalizations.of(context)!.noNetworkConnected,
              style: const TextStyle(fontSize: 16, color: Colors.white),
            )),
      ),
    );
  }

  static _serviceRequestNotification(double serviceRequestNotificationHeight, ServiceRequestNotificationSent state, BuildContext context) {
    final serviceRequestBloc = BlocProvider.of<ServiceRequestBloc>(context);
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      alignment: Alignment.centerLeft,
      width: double.infinity,
      color: const Color(0xff022F49),
      height: serviceRequestNotificationHeight,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: Container(
              margin: const EdgeInsets.only(left: 10, top: 20),
              padding: const EdgeInsets.all(10),
              child: RichText(
                text: TextSpan(
                  style: const TextStyle(
                    fontSize: 16.0,
                    color: Colors.black,
                  ),
                  children: <TextSpan>[
                    TextSpan(text: state.message, style: const TextStyle(color: Colors.white)),
                  ],
                ),
              ),
            ),
          ),
          Container(
              margin: const EdgeInsets.only(left: 10, top: 20),
              child: IconButton(
                  onPressed: () {
                    serviceRequestBloc.add(DismissServiceRequestNotification());
                  },
                  icon: const FaIcon(
                    FontAwesomeIcons.lightTimesCircle,
                    color: Colors.white,
                  )))
        ],
      ),
    );
  }

  static adminMessageNotification() {
    return Material(
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        alignment: Alignment.centerLeft,
        width: double.infinity,
        color: const Color(0xffB60022),
        height: 100,
        child: Container(
            margin: const EdgeInsets.only(left: 10, top: 20),
            padding: const EdgeInsets.all(10),
            child: const Text(
              'Admin message: Fire Emergency at terminal T1,Gate G3, Be on high alert',
              style: TextStyle(fontSize: 16, color: Colors.white),
            )),
      ),
    );
  }

  static closeApplication() {
    if (Platform.isAndroid) {
      SystemNavigator.pop();
    } else if (Platform.isIOS) {
      exit(0);
    }
  }

  static showLoaderDialog(BuildContext context, String message) {
    AlertDialog alert = AlertDialog(
      content: Row(
        children: [
          const CircularProgressIndicator(),
          const SizedBox(
            width: 10,
          ),
          Flexible(
            child: Container(
              margin: const EdgeInsets.only(left: 7),
              child: Text(message,softWrap: true,),
            ),
          ),
        ],
      ),
    );
    showDialog(
      barrierDismissible: false,
      context: context,
      builder: (BuildContext context) {
        return alert;
      },
    );
  }

  static showSnackBar({required BuildContext context, required String message}) {
    ScaffoldMessenger.of(context).showSnackBar(SnackBar(
      content: Text(message),
      duration: const Duration(milliseconds: 1500),
    ));
  }

  static String getFormattedDateFromDate({String format = "yyyy-MM-dd", required String createdDate}) {
    return DateFormat(format).format(DateTime.parse(createdDate).toLocal());
  }

  static String getFormattedDateFromDateTime({String format = "dd-MMM-yyyy,HH:mm", required DateTime dateTime}) {
    return DateFormat(format).format(dateTime.toLocal());
  }

  static String getFormattedCurrentDateAndTime() {
    return DateFormat('dd-MM-yyyy,HH:mm:ss').format(DateTime.now().toLocal());
  }

  static String getHourAndMinuteFromMinute(int remainingTime) {
    if (remainingTime != null) {
      var d = Duration(minutes: remainingTime.abs());
      List<String> parts = d.toString().split(':');
      return '${parts[0].padLeft(2, '0')}:${parts[1].padLeft(2, '0')}';
    }
    return '00:00';
  }

  static String getHourAndMinuteOrDayFromMinute(int remainingTime) {
    if (remainingTime != null) {
      if (remainingTime > 1440 || remainingTime < -1440) {
        int day = (remainingTime.abs() / 1440).floor();
        if (day > 1) {
          return "${day.round()} DAYS";
        } else {
          return "${day.round()} DAY";
        }
      } else {
        var hm = Duration(minutes: remainingTime.abs());
        print("in days: $hm");
        List<String> parts = hm.toString().split(':');
        return '${parts[0].padLeft(2, '0')}:${parts[1].padLeft(2, '0')}';
      }
    }
    return '00:00';
  }

  static String encodeQueryParameters(Map<String, dynamic> params) {
    return params.entries.map((e) => '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value)}').join('&');
  }

  static void launchEmail(BuildContext context) async {
    final dir = await getApplicationDocumentsDirectory();
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    //database path
    final path = p.join(dir.path, 'db.sqlite');
    File file = File(path);
    final tempDirectory = await getTemporaryDirectory();
    file.copy(tempDirectory.path + '/db.sqlite');

    //backup log path
    final backupPath = await getBackUpFilePath();
    File backUpFile = File(backupPath);
    final tempBackUpFileDirectory = await getTemporaryDirectory();
    if (backUpFile.existsSync()) {
      backUpFile.copy(tempBackUpFileDirectory.path + '/backup.txt');
    }

    var mailBody = getMailBody(packageInfo);
    //zipfiles();
    final Email email;
    try {
      if (backUpFile.existsSync()) {
        email = Email(
          body: mailBody,
          subject: 'Log',
          recipients: [
            '<EMAIL>', /*'<EMAIL>', '<EMAIL>'*/
          ],
          //<EMAIL>
          attachmentPaths: [Logger.logPath!+'/logger.txt', tempDirectory.path + '/db.sqlite', tempBackUpFileDirectory.path + '/backup.txt'],
          //attachmentPaths: [zipFile.path],
          isHTML: false,
        );
      } else {
        email = Email(
          body: mailBody,
          subject: 'Log',
          recipients: [
            '<EMAIL>', /*'<EMAIL>', '<EMAIL>'*/
          ],
          //<EMAIL>
          attachmentPaths: [
            Logger.logPath!+'/logger.txt',
            tempDirectory.path + '/db.sqlite',
          ],
          //attachmentPaths: [zipFile.path],
          isHTML: false,
        );
      }

      await FlutterEmailSender.send(email);
    } on PlatformException catch (e) {
      ApplicationUtil.showSnackBar(context: context, message: e.message!);
    } catch (e) {
      print("=========================ERROR=========================");
      ApplicationUtil.showSnackBar(context: context, message: e.toString());
    }
  }

  static _pendingRepairNotification(double repairHeight, BuildContext context) {
    final repairBloc = BlocProvider.of<RepairBloc>(context);
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      alignment: Alignment.centerLeft,
      width: double.infinity,
      color: const Color(0xff022F49),
      height: repairHeight,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: Container(
              margin: const EdgeInsets.only(left: 10, top: 20),
              padding: const EdgeInsets.all(10),
              child: RichText(
                text: TextSpan(
                  style: const TextStyle(
                    fontSize: 16.0,
                    color: Colors.black,
                  ),
                  children: <TextSpan>[
                    TextSpan(
                      text: '${AppLocalizations.of(context)!.repair} ',
                      style: const TextStyle(color: Colors.white),
                    ),
                    TextSpan(text: '${AppLocalizations.of(context)!.completedSuccessfully}', style: const TextStyle(color: Colors.white)),
                  ],
                ),
              ),
            ),
          ),
          Container(
              margin: const EdgeInsets.only(left: 10, top: 20),
              child: IconButton(
                  onPressed: () {
                    repairBloc.add(ResetRepairUI());
                  },
                  icon: const FaIcon(
                    FontAwesomeIcons.lightTimesCircle,
                    color: Colors.white,
                  )))
        ],
      ),
    );
  }

  static void showMissingPhotosWarningDialog(BuildContext context,
      {required String message, required VoidCallback onSubmit, required VoidCallback onCancel}) {
    showDialog(
      barrierDismissible: false,
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text("${AppLocalizations.of(context)!.warning}.."),
          content: Text(message),
          actions: [
            ElevatedButton(
              style: ButtonStyle(backgroundColor: MaterialStateProperty.all(AppColor.redColor)),
              child: Text(AppLocalizations.of(context)!.submit),
              onPressed: () {
                onSubmit();
                Navigator.pop(context);
              },
            ),
            ElevatedButton(
              child: Text(AppLocalizations.of(context)!.cancel),
              onPressed: () {
                onCancel();
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }
  static ButtonStyle customButtonStyle(BuildContext context) {
    return ButtonStyle(
      backgroundColor: MaterialStateProperty.all(const Color(0xff727272)),
      shape: MaterialStateProperty.all(
        RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(15.0),
          side: BorderSide(color: Theme.of(context).primaryColor),
        ),
      ),
    );
  }

  static showConfirmDialog(BuildContext ctx,
      {required String title,
      required String description,
      required String continueString,
      Color? continueColor,
      required String cancelString,
      required VoidCallback onContinue}) {
    showDialog(
      barrierDismissible: false,
      context: ctx,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: Text(description),
          actions: [
            ElevatedButton(
              style: ButtonStyle(backgroundColor: MaterialStateProperty.all(continueColor ?? Theme.of(context).primaryColor)),
              child: Text(continueString),
              onPressed: () {
                Navigator.of(context).pop();
                onContinue();
              },
            ),
            ElevatedButton(
              child: Text(cancelString),
              onPressed: () {
                Navigator.of(ctx).pop();
              },
            ),
          ],
        );
      },
    );
  }

  static showConfirmDialogWithInput(
    BuildContext context, {
    required String title,
    required String description,
    required String continueString,
    Color? continueColor,
    required String cancelString,
    required VoidCallback onContinue,
    required TextEditingController textEditingController,
  }) {
    AlertDialog alert = AlertDialog(
      title: Text(
        title,
        style: TextStyle(fontSize: 18, color: Theme.of(context).primaryColor, fontWeight: FontWeight.bold),
      ),
      content: Container(
        width: double.maxFinite,
        constraints: const BoxConstraints(maxWidth: 500),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              description,
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(
              height: 10,
            ),
            RichText(
              textAlign: TextAlign.start,
              text: TextSpan(style: const TextStyle(fontSize: 18), children: <TextSpan>[
                const TextSpan(
                  text: '* ',
                  style: TextStyle(color: AppColor.redColor, fontWeight: FontWeight.bold),
                ),
                TextSpan(
                  text: AppLocalizations.of(context)!.addComment,
                  style: TextStyle(color: Theme.of(context).primaryColor, fontWeight: FontWeight.bold),
                ),
              ]),
            ),
            const SizedBox(
              height: 5,
            ),
            TextField(
              controller: textEditingController,
              decoration: InputDecoration(
                border: const OutlineInputBorder(
                  borderRadius: BorderRadius.all(
                    Radius.circular(5.0),
                  ),
                ),
                hintText: AppLocalizations.of(context)!.duplicateService,
                //labelText: 'Leave comment',
              ),
              minLines: 2,
              maxLines: 3,
            ),
          ],
        ),
      ),
      actions: [
        ElevatedButton(
          style: ButtonStyle(
            backgroundColor: MaterialStateProperty.all(continueColor ?? AppColor.redColor),
          ),
          child: Text(continueString),
          onPressed: () {
            if (textEditingController.text.isNotEmpty) {
              onContinue();
              Navigator.of(context).pop();
            } else {
              ApplicationUtil.showSnackBar(
                context: context,
                message: AppLocalizations.of(context)!.commentIsRequired,
              );
            }
          },
        ),
        ElevatedButton(
          child: Text(cancelString),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
      ],
    );
    showDialog(
      barrierDismissible: false,
      context: context,
      builder: (BuildContext context) {
        return alert;
      },
    );
  }

  static initLocationPermission() async {
    gps.Location location = gps.Location();

    bool _serviceEnabled;
    gps.PermissionStatus _permissionGranted;

    _serviceEnabled = await location.serviceEnabled();
    if (!_serviceEnabled) {
      _serviceEnabled = await location.requestService();
      if (!_serviceEnabled) {
        return null;
      }
    }

    _permissionGranted = await location.hasPermission();
    if (_permissionGranted == gps.PermissionStatus.denied) {
      _permissionGranted = await location.requestPermission();
      if (_permissionGranted != gps.PermissionStatus.granted) {
        return null;
      }
    }
    return true;
  }

  static Future<gps.LocationData?> getLocation() async {
    print('location fetching start');
    gps.Location location = gps.Location();

    bool _serviceEnabled;
    gps.PermissionStatus _permissionStatus;
    try {
      _serviceEnabled = await location.serviceEnabled();
      Logger.i('location fetching _serviceEnabled');
    } catch (e) {
      Logger.e("Error checking location service status: $e");
      return null;
    }
    if (!_serviceEnabled) {
      _serviceEnabled = await location.requestService();
      if (!_serviceEnabled) {
        return null;
      }
    }

    _permissionStatus = await location.hasPermission();
    if(_permissionStatus == gps.PermissionStatus.granted){
      location.changeSettings(accuracy: LocationAccuracy.balanced);
    }
    if (_permissionStatus == gps.PermissionStatus.denied) {
      Logger.i('location permission denied');
      Logger.i('trying to request permission');
      _permissionStatus = await location.requestPermission();
      if (_permissionStatus != gps.PermissionStatus.granted) {
        Logger.i('location permission granted');
        return null;
      }
    }
    print("before request");
    try {
      gps.LocationData locationData = await location.getLocation();
      Logger.i('location fetched $locationData');
      return locationData;
    } catch (e) {
      Logger.e("Unable to request permission $e");
    }
  }

  static void zipfiles() {
    //ZIP FILE
    /* final sourceDir = Directory(tempDirectory.path);

    Logger.i("LOG SIZE:" + logFile.lengthSync().toString());
    Logger.i("LOG PATH:" + logFile.path);

    File dbFile = File(tempDirectory.path + '/db.sqlite');
    Logger.i("DATABASE SIZE:" + dbFile.lengthSync().toString());
    Logger.i("DATABASE PATH:" + dbFile.path);

    final files = [File(logFile.path), File(dbFile.path)];
    final zipFile = File(tempDirectory.path + '/db_log.zip');
    try {
      await ZipFile.createFromFiles(
          sourceDir: sourceDir, files: files, zipFile: zipFile);
    } catch (e) {
      Logger.e(e.toString());
    }*/
  }

  static Future<String> getBackUpFilePath() async {
    final directory = await getApplicationDocumentsDirectory();
    return directory.path + '/backup.txt';
  }

  static showUnsavedWarningAlertDialog(BuildContext ctx) {
    AlertDialog alert = AlertDialog(
      title: Text(
        AppLocalizations.of(ctx)!.unsavedChange,
      ),
      content: Container(
          width: double.maxFinite,
          constraints: const BoxConstraints(maxWidth: 500),
          child: Text(
            AppLocalizations.of(ctx)!.youWillLoseYourData,
          )),
      actions: [
        ElevatedButton(
          style: ButtonStyle(backgroundColor: MaterialStateProperty.all(AppColor.redColor)),
          child: Text(
            AppLocalizations.of(ctx)!.goBack,
          ),
          onPressed: () {
            Navigator.of(ctx).pop();
            Navigator.pop(ctx, false);
          },
        ),
        ElevatedButton(
          child: Text(
            AppLocalizations.of(ctx)!.cancel,
          ),
          onPressed: () {
            Navigator.of(ctx).pop();
          },
        ),
      ],
    );
    showDialog(
      barrierDismissible: false,
      context: ctx,
      builder: (BuildContext context) {
        return alert;
      },
    );
  }

  static _errorNotification(networkErrorNotificationHeight, String message, BuildContext context) {
    final serviceRequestBloc = BlocProvider.of<ServiceRequestBloc>(context);

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      alignment: Alignment.centerLeft,
      width: double.infinity,
      color: Colors.red.shade900,
      height: networkErrorNotificationHeight,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: Container(
              margin: const EdgeInsets.only(left: 10, top: 20),
              padding: const EdgeInsets.all(10),
              child: RichText(
                text: TextSpan(
                  style: const TextStyle(
                    fontSize: 16.0,
                    color: Colors.black,
                  ),
                  children: <TextSpan>[
                    TextSpan(
                      text: message,
                      style: const TextStyle(color: Colors.white),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Container(
              margin: const EdgeInsets.only(left: 10, top: 20),
              child: IconButton(
                  onPressed: () {
                    serviceRequestBloc.add(DismissServiceRequestErrorNotification());
                  },
                  icon: const FaIcon(
                    FontAwesomeIcons.lightTimesCircle,
                    color: Colors.white,
                  )))
        ],
      ),
    );
  }

  static showWarningAlertDialog(BuildContext context,
      {String? title,
      String? desc,
      String? positiveLabel,
      String? negativeLabel,
      Function? onPositiveClickListener,
      Function? onNegativeClickListener,
      Color? positiveLabelColor}) {
    AlertDialog alert = AlertDialog(
      title: title != null ? Text(title) : null,
      content: Container(width: double.maxFinite, constraints: const BoxConstraints(maxWidth: 500), child: desc != null ? Text(desc) : null),
      actions: [
        if (positiveLabel != null) ...[
          TextButton(
            child: Text(
              positiveLabel,
              style: TextStyle(color: positiveLabelColor),
            ),
            onPressed: () {
              if (onPositiveClickListener != null) {
                onPositiveClickListener();
              }
            },
          ),
        ],
        if (negativeLabel != null) ...[
          TextButton(
            child: Text(negativeLabel),
            onPressed: () {
              if (onNegativeClickListener != null) {
                onNegativeClickListener();
              }
              Navigator.of(context).pop();
            },
          ),
        ],
      ],
    );
    showDialog(
      barrierDismissible: false,
      context: context,
      builder: (BuildContext ctx) {
        return alert;
      },
    );
  }

  static Map<String, dynamic> getEndLocationFromLocation({required String locationId, required List<Map<String, dynamic>>? locationMap}) {
    Map<String, dynamic> resultLocation = {};
    var terminal = '', location_id = '', name = '';
    for (var element in locationMap!) {
      element.forEach((root, value) {
        if (element /*[root]*/ ['CATEGORY'] == "Terminal") {
          terminal = element['NAME'];
        }
        if (element /*[root]*/ ['LOCATION_ID'] == locationId) {
          resultLocation = {"NAME": element['NAME'], "TERMINAL": terminal, "LOCATION_ID": element['LOCATION_ID']};
        } else {
          //print(element['LOCATION_ID']);
        }
      });
    }

    /*locationMap.forEach((root, value) {
      if (locationMap[root]['LOCATION_ID'] == locationId) {
        resultLocation = {
          "NAME": locationMap[root]['NAME'],
          "LOCATION_ID": locationMap[root]['LOCATION_ID']
        };
      }
    });*/
    return resultLocation;
  }

  static getMailBody(PackageInfo packageInfo) {
    var bodyString = StringBuffer();
    bodyString.write('Attaching log for ${getFormattedDateFromDateTime(
      dateTime: DateTime.now(),
    )}\n');
    bodyString.write("______________________________\n---Application Details---\n");
    bodyString.write("Application Name: ${packageInfo.appName}\n");
    bodyString.write("Application Build Number: ${packageInfo.buildNumber}\n");
    bodyString.write("Application Version: ${packageInfo.version}\n");
    bodyString.write("Application Package Name: ${packageInfo.packageName}\n");
    bodyString.write("______________________________");
    return bodyString.toString();
  }

  static printInfoLog({required String methodName, required int statusCode, required int contentLength, required String conversationId}) {
    Logger.i(' ${ApplicationUtil.getFormattedCurrentDateAndTime()} $methodName RESPOND = > ' +
        "STATUS CODE: $statusCode, LENGTH: $contentLength, CONVERSATION ID: "
            "$conversationId");
    print(' ${ApplicationUtil.getFormattedCurrentDateAndTime()} $methodName RESPOND = > ' +
        "STATUS CODE: $statusCode, LENGTH: $contentLength, CONVERSATION ID: "
            "$conversationId");
  }
  static getIcons(){
    if(customer_id == 15){
      return  Image.asset("assets/images/seats.png",width: 30,height: 30,)
      /* FaIcon(
        FontAwesomeIcons.airbnb,
        color: Color(0xff3F51B5),
      )*/;
    }else{
      return const FaIcon(
        FontAwesomeIcons.planeAlt,
        color: Color(0xff3F51B5),
      );
    }
  }

  static String getFleetString(){
    if(customer_id == 15){
      return "Location";
    }else{
      return "Fleet";
    }

  }

  static String getSubFleetString(){
    if(customer_id == 15){
      return "Sub Location";
    }else{
      return "SubFleet";
    }

  }

  static String getTailString(){
    if(customer_id == 15){
      return "Section";
    }else{
      return "Tail";
    }

  }

  static String getAirCraftString(){
    if(customer_id == 15){
      return "Stadium";
    }else{
      return "Select AirCraft";
    }

  }

  static String getEquipmentType(){
    if(customer_id == 15){
      return "Seat";
    } else{
      return "Seat";
    }
  }
  //If any "Precise" Location permission is not given for mobile and location permission is not given for web then, Location Detail will be null
  static Future<dynamic> getGeoLocatorLocation() async {
    bool serviceEnabled;
    geolocator.LocationPermission permission;
    ///check if the locationis enabled if not return null
    if(kIsWeb) {
      serviceEnabled = await geolocator.Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        Logger.i('Location services are disabled.');
        return "WEB";
      } else {
        permission = await geolocator.Geolocator.checkPermission();
        if (permission == geolocator.LocationPermission.denied ||
            permission == geolocator.LocationPermission.deniedForever) {
          Logger.i('Location permissions are denied or deniedForever');
          return "WEB";
        } else {
          try {
            geolocator.Position position =
                await geolocator.Geolocator.getCurrentPosition(
              desiredAccuracy: geolocator.LocationAccuracy.high,
            );
            Logger.i(
                "Got the Position in application_util- getGeoLocatorLocation $position");
            // Map Position object to gps.LocationData
            final gps.LocationData locationData = gps.LocationData.fromMap({
              'latitude': position.latitude,
              'longitude': position.longitude,
              'accuracy': position.accuracy,
              'altitude': position.altitude,
              'speed': position.speed,
              'speed_accuracy': position.speedAccuracy,
              'heading': position.heading,
              'time': position.timestamp.millisecondsSinceEpoch.toDouble(),
              'isMock': position.isMocked ? 1 : 0,
              'verticalAccuracy': null,
              'headingAccuracy': position.headingAccuracy,
              'elapsedRealtimeNanos': null,
              'elapsedRealtimeUncertaintyNanos': null,
              'satelliteNumber': null,
              'provider': null,
            });
            return locationData;
          } catch (e) {
            Logger.e("Error fetching location $e");
            return "WEB";
          }
        }
      }
    }
    /// Check if location services are enabled
    serviceEnabled = await geolocator.Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      Logger.i('Location services are disabled.');
      return null;
    }

    /// Check and request permission
    permission = await geolocator.Geolocator.checkPermission();
    if (permission == geolocator.LocationPermission.denied || 
        permission == geolocator.LocationPermission.deniedForever) {
      permission = await geolocator.Geolocator.requestPermission();

      /// Location Permission Denaied again
      if (permission == geolocator.LocationPermission.denied ||
          permission == geolocator.LocationPermission.deniedForever) {
        print('Location permissions are denied or deniedForever');
        return null;
      }
    }

    /// Fetch current position
    geolocator.Position position;
    try {
      if (!await isPreciseLocation()) {
        return null;
      }
      position = await geolocator.Geolocator.getCurrentPosition(
        desiredAccuracy: geolocator.LocationAccuracy.best,
      ).timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          throw TimeoutException('Location request timed out');
        },
      );
      if(position.accuracy > 500){
        Logger.i("Location accuracy is more than 500m, returning null");
        return null;

      }
      Logger.i("Got the Position in application_util- getGeoLocatorLocation $position");
    } on TimeoutException catch (e) {
      Logger.e("Location request timed out: $e");
      Logger.e("Location request timed out: duration-${e.duration}, message-${e.message}");
      return "TIMEOUT";
    } catch (e) {
      Logger.e("Error fetching location $e");
      return null;
    }

    // Map Position object to gps.LocationData
    final gps.LocationData locationData = gps.LocationData.fromMap({
      'latitude': position.latitude,
      'longitude': position.longitude,
      'accuracy': position.accuracy,
      'altitude': position.altitude,
      'speed': position.speed,
      'speed_accuracy': position.speedAccuracy,
      'heading': position.heading,
      'time': position.timestamp?.millisecondsSinceEpoch.toDouble(),
      'isMock': position.isMocked ? 1 : 0,
      'verticalAccuracy': null,
      'headingAccuracy': position.headingAccuracy,
      'elapsedRealtimeNanos': null,
      'elapsedRealtimeUncertaintyNanos': null,
      'satelliteNumber': null,
      'provider': null,
    });
    return locationData;
  }

  //Get the Location Accuracy Permission 
  static Future<bool> isPreciseLocation() async {
    try {
      geolocator.LocationAccuracyStatus accuracyStatus =
          await geolocator.Geolocator.getLocationAccuracy();
      if (accuracyStatus == geolocator.LocationAccuracyStatus.precise) {
        return true;
      }
      else{
        return false;
      }
    } catch (e) {
      return false;
    }
  }
}

class ImageWithTag {
  String base64;
  int index;

  ImageWithTag({required this.base64, required this.index});
}

class ApiResponse {
  static const INVALID_AUTH = "Invalid Authentication Credentials";

  //static const AUTH_DATA_NOT_PROVIDED = "Auth data not provided";
  static const INTER_SERVER_ERROR = "Internal error";
  static const BAR_CODE_NOT_FOUND = "Barcode not found";
  static const USER_NOT_FOUND = "User not found. Make sure that this email id is associated with a company in the ALink admin portal.";
  static const INVALID_CREDENTISLS = "Invalid credentials. Username or password may be incorrect.";
}

class FleetLocation {
  String fleetName;
  String subFleetName;
  String tailName;

  FleetLocation({required this.fleetName, required this.subFleetName, required this.tailName});
}

class LopaServiceRequestParameter {
  FleetLocation fleetLocation;
  List<SeatCategory> seatCategoryList;
  bool isDirectRepair;
  bool isNotification;

  LopaServiceRequestParameter(
      {required this.fleetLocation, required this.seatCategoryList, this.isDirectRepair = false, this.isNotification = false});
}
