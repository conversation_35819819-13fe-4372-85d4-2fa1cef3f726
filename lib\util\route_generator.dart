import 'package:alink/bloc/api/api_bloc.dart';
import 'package:alink/bloc/part_items/part_items_bloc.dart';
import 'package:alink/bloc/repair_list_api/repair_list_api_bloc.dart';
import 'package:alink/cubit/pages/login/forgot_password_cubit.dart';
import 'package:alink/cubit/pages/login/login_cubit.dart';
import 'package:alink/cubit/pages/repair_detail/repair_button_cubit.dart';
import 'package:alink/cubit/pages/settings/settings_cubit.dart';
import 'package:alink/data/model/audit_id_model.dart';
import 'package:alink/data/model/confirm_audit_model.dart';
import 'package:alink/data/model/repair_service_request.dart';
import 'package:alink/data/repository/api_repository.dart';
import 'package:alink/data/repository/api_service.dart';
import 'package:alink/pages/airline/aircraft_selection_page.dart';
import 'package:alink/pages/airline/audit/lopa_audit_page.dart';
import 'package:alink/pages/airline/repair/lopa_repair_page.dart';
import 'package:alink/pages/airline/service_request/lopa_service_request_detail_page.dart';
import 'package:alink/pages/airline/service_request/lopa_service_request_page.dart';
import 'package:alink/pages/airport/audit/audit_barcode/audit_scanner_page.dart';
import 'package:alink/pages/airport/audit/audit_page.dart';
import 'package:alink/pages/airport/audit/confirm_audit_page.dart';
import 'package:alink/pages/airport/audit/equipment_list_page.dart';
import 'package:alink/pages/airport/repair/assign_equipment/assign_equipment_page.dart';
import 'package:alink/pages/airport/repair/repair_detail_page.dart';
import 'package:alink/pages/airport/repair/repair_list_page.dart';
import 'package:alink/pages/airport/repair/replace_bar_code/confirm_barcode_replace_page.dart';
import 'package:alink/pages/airport/repair/search_item_part_page.dart';
import 'package:alink/pages/airport/repair/service_request_filter_page.dart';
import 'package:alink/pages/airport/service_request/add_service_request_page.dart';
import 'package:alink/pages/airport/service_request/afs_service_repair_page.dart';
import 'package:alink/pages/airport/service_request/bar_code_result_detail_page.dart';
import 'package:alink/pages/airport/service_request/service_result_page.dart';
import 'package:alink/pages/auth/login_page.dart';
import 'package:alink/pages/dashboard_page.dart';
import 'package:alink/pages/full_image_view.dart';
import 'package:alink/pages/image_editor/annotated_image_page.dart';
import 'package:alink/pages/image_editor/crop_image_page.dart';
import 'package:alink/pages/image_editor/edit_image_home_page.dart';
import 'package:alink/pages/onboard_page.dart';
import 'package:alink/pages/settings_page.dart';
import 'package:alink/scanner/barcode/ai_scanner_page.dart';
import 'package:alink/util/application_util.dart';
import 'package:alink/util/enums/app_enum.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../data/model/singleton_model.dart';
import '../pages/airport/timed_service_requests/timed_service_requests.dart';

class RouteGenerator {
  final _partItemBloc = PartItemsBloc(apiRepository: ApiRepository(apiService: ApiService()));

  Route<dynamic> generateRoute(RouteSettings settings) {
    // Getting arguments passed in while calling Navigator.pushNamed

    final args = settings.arguments;

    switch (settings.name) {
      case '/':

      case LoginPage.routeName:
        {
          bool isRestarted = false;
          if (args is bool) {
            isRestarted = args;
          }
          return PageRouteBuilder(
            pageBuilder: (context, animation, secondaryAnimation) => MultiBlocProvider(
              providers: [
                BlocProvider<LoginCubit>(
                  create: (context) => LoginCubit(),
                ),
                BlocProvider<ForgotPasswordCubit>(
                  create: (context) => ForgotPasswordCubit(apiRepository: ApiRepository(apiService: ApiService())),
                ),
              ],
              child: LoginPage(
                isRestarted: isRestarted,
              ),
            ),
            transitionsBuilder: (context, animation, secondaryAnimation, child) {
              const begin = Offset(0.0, 1.0);
              const end = Offset.zero;
              const curve = Curves.ease;

              var tween = Tween(begin: begin, end: end).chain(CurveTween(curve: curve));

              return SlideTransition(
                position: animation.drive(tween),
                child: child,
              );
            },
          );
        }
      case OnBoardingPage.routeName:
        return MaterialPageRoute(builder: (_) => OnBoardingPage());
      case DashboardPage.routeName:
        return MaterialPageRoute(builder: (_) => const DashboardPage());
      case EditImageHomePage.routeName:
        {
          if (args is String) {
            return MaterialPageRoute(
              builder: (_) => EditImageHomePage(
                argumentImage: args,
              ),
            );
          }
          return _errorRoute();
        }
      case AnnotatedImagePage.routeName:
        {
          if (args is String) {
            return MaterialPageRoute(
              builder: (_) => AnnotatedImagePage(
                base64Img: args,
              ),
            );
          }
          return _errorRoute();
        }
      case CropImagePage.routeName:
        {
          if (args is String) {
            return MaterialPageRoute(
              builder: (_) => CropImagePage(
                base64Img: args,
              ),
            );
          }
          return _errorRoute();
        }
      case AIBarCodeScannerPage.routeName:
        return MaterialPageRoute(builder: (_) => const AIBarCodeScannerPage());
      case AuditBarCodeScannerPage.routeName:
        return MaterialPageRoute(builder: (_) => AuditBarCodeScannerPage());
      case BardCodeResultPage.routeName:
        {
          if (args is ServiceType) {
            return MaterialPageRoute(
              builder: (_) => BardCodeResultPage(
                serviceRequestData: args,
              ),
            );
          }
          return _errorRoute();
        }
      case ServiceRequestFilterPage.routeName:
        return MaterialPageRoute(builder: (_) => const ServiceRequestFilterPage());
      case SearchItemPartPage.routeName:
        return MaterialPageRoute(
            builder: (_) => BlocProvider.value(
                  value: _partItemBloc,
                  child: SearchItemPartPage(singleSelectionParam: (args ?? SearchItemParam()) as SearchItemParam),
                ));
      case BarcodeResultDetailPage.routeName:
        return MaterialPageRoute(builder: (_) {
          return BarcodeResultDetailPage(fetchSingleServiceRequestInAudit: args as FetchSingleServiceRequestInAudit);
        });

      case TimedServiceRequests.routeName:
        return MaterialPageRoute(
          builder: (ctx) => MultiBlocProvider(
            providers: [
              BlocProvider<ApiBloc>(
                create: (context) {
                  var apiRepository = RepositoryProvider.of<ApiRepository>(context);
                  return ApiBloc(apiRepository: apiRepository);
                },
              ),
              BlocProvider<RepairListApiBloc>(
                create: (context) {
                  var apiRepository = RepositoryProvider.of<ApiRepository>(context);
                  return RepairListApiBloc(apiRepository: apiRepository);
                },
              ),
              /*BlocProvider.value(
                value: FilterCubit(
                    apiRepository: RepositoryProvider.of<ApiRepository>(ctx)),
              )*/
            ],
            child: const TimedServiceRequests(),
          ),
        );
      case AuditPage.routeName:
        return MaterialPageRoute(
          builder: (_) => const AuditPage(),
        );
      case RepairListPage.routeName:
        return MaterialPageRoute(
          builder: (ctx) => MultiBlocProvider(
            providers: [
              BlocProvider<ApiBloc>(
                create: (context) {
                  var apiRepository = RepositoryProvider.of<ApiRepository>(context);
                  return ApiBloc(apiRepository: apiRepository);
                },
              ),
              BlocProvider<RepairListApiBloc>(
                create: (context) {
                  var apiRepository = RepositoryProvider.of<ApiRepository>(context);
                  return RepairListApiBloc(apiRepository: apiRepository);
                },
              ),
              /*BlocProvider.value(
                value: FilterCubit(
                    apiRepository: RepositoryProvider.of<ApiRepository>(ctx)),
              )*/
            ],
            child: const RepairListPage(),
          ),
        );
      case RepairDetailPage.routeName:
        {
          if (args is RepairDetailPageParams) {
            return MaterialPageRoute(
                builder: (_) => MultiBlocProvider(
                      providers: [
                        BlocProvider<RepairButtonCubit>(
                          create: (context) => RepairButtonCubit(),
                        ),
                      ],
                      child: RepairDetailPage(repairServiceRequest:  args as RepairDetailPageParams),
                    ));
          }
          return _errorRoute();
        }
      case AddServiceRequestPage.routeName:
        {
          if (args is ServiceType) {
            return MaterialPageRoute(
              builder: (_) => BlocProvider<RepairButtonCubit>(
                create: (context) => RepairButtonCubit(),
                child: AddServiceRequestPage(
                  serviceRequestData: args,
                ),
              ),
            );
          }
          return _errorRoute();
        }
      case SettingsPage.routeName:
        return MaterialPageRoute(
            builder: (context) => BlocProvider<SettingsCubit>(
                  create: (context) => SettingsCubit(),
                  child: const SettingsPage(),
                ));
      case ImageViewPage.routeName:
        {
          if (args is ImageWithTag) {
            return MaterialPageRoute(
              builder: (_) => ImageViewPage(
                imageWithTag: args,
              ),
            );
          }
          return _errorRoute();
        }
      case ConfirmBarcodeReplacementPage.routeName:
        {
          if (args is ServiceType) {
            return MaterialPageRoute(
              builder: (_) => ConfirmBarcodeReplacementPage(
                type: args,
              ),
            );
          }
          return _errorRoute();
        }
      case AssignEquipmentPage.routeName:
        {
          if (args is ServiceType) {
            return MaterialPageRoute(
              builder: (_) => BlocProvider.value(
                value: _partItemBloc,
                child: AssignEquipmentPage(
                  type: args,
                ),
              ),
            );
          }
          return _errorRoute();
        }
      case EquipmentListPage.routeName:
        {
          if (args is AuditRefId) {
            return MaterialPageRoute(
              builder: (_) => EquipmentListPage(
                auditRefId: args,
              ),
            );
          }
          return _errorRoute();
        }
      case ConfirmAuditPage.routeName:
        if (args is ConfirmAuditModel) {
          return MaterialPageRoute(
            builder: (_) => ConfirmAuditPage(
              confirmAuditModel: args,
            ),
          );
        } else {
          return _errorRoute();
        }
      case AfsServiceRequestAndRepairPage.routeName:
        {
          if (args is ServiceType) {
            return MaterialPageRoute(
              builder: (_) => BlocProvider<RepairButtonCubit>(
                create: (context) => RepairButtonCubit(),
                child: AfsServiceRequestAndRepairPage(
                  serviceRequestData: args,
                ),
              ),
            );
          }
          return _errorRoute();
        }
      case AirCraftSelectionPage.routeName:
        return MaterialPageRoute(
          builder: (_) => const AirCraftSelectionPage(),
        );
      case DigitalLopaPage.routeName:
        if (args != null) {
          return PageRouteBuilder(
            pageBuilder: (context, animation, secondaryAnimation) => BlocProvider<RepairListApiBloc>(
              create: (context) => RepairListApiBloc(apiRepository: ApiRepository(apiService: ApiService())),
              child: DigitalLopaPage(
                isUpdate: (args is bool) ? args : false,
                isDirecRepair: (args is ServiceRequestType && args == ServiceRequestType.DIRECT_REPAIR) ? true : false,
                isNotification: (args is ServiceRequestType && args == ServiceRequestType.NOTIFICATION) ? true : false,
              ),
            ),
            transitionsBuilder: (context, animation, secondaryAnimation, child) {
              const begin = Offset(0.0, 1.0);
              const end = Offset.zero;
              const curve = Curves.ease;

              var tween = Tween(begin: begin, end: end).chain(CurveTween(curve: curve));

              return SlideTransition(
                position: animation.drive(tween),
                child: child,
              );
            },
          );
        }

        return _errorRoute();

      case LopaServiceRequestPage.routeName:
        {
          if (args is LopaServiceRequestParameter) {
            return MaterialPageRoute(
              builder: (_) => BlocProvider<RepairButtonCubit>(
                create: (context) => RepairButtonCubit(),
                child: LopaServiceRequestPage(
                  lopaServiceRequestParameter: args,
                ),
              ),
            );
          }
          return _errorRoute();
        }
      case LopaRepairPage.routeName:
        return PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) => BlocProvider<RepairListApiBloc>(
            create: (context) => RepairListApiBloc(apiRepository: ApiRepository(apiService: ApiService())),
            child: const LopaRepairPage(),
          ),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            const begin = Offset(0.0, 1.0);
            const end = Offset.zero;
            const curve = Curves.ease;

            var tween = Tween(begin: begin, end: end).chain(CurveTween(curve: curve));

            return SlideTransition(
              position: animation.drive(tween),
              child: child,
            );
          },
        );
      case LopaAuditPage.routeName:
        return PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) => BlocProvider<RepairListApiBloc>(
            create: (context) => RepairListApiBloc(apiRepository: ApiRepository(apiService: ApiService())),
            child: const LopaAuditPage(),
          ),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            const begin = Offset(0.0, 1.0);
            const end = Offset.zero;
            const curve = Curves.ease;

            var tween = Tween(begin: begin, end: end).chain(CurveTween(curve: curve));

            return SlideTransition(
              position: animation.drive(tween),
              child: child,
            );
          },
        );
      default:
        return _errorRoute();
    }
  }

  static Route<dynamic> _errorRoute() {
    return MaterialPageRoute(builder: (_) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Error'),
        ),
        body: const Center(
          child: Text('Error occur'),
        ),
      );
    });
  }

  void dispose() {
    _partItemBloc.close();
  }
}
