import 'dart:developer';

import 'package:alink/bloc/api/api_bloc.dart';
import 'package:alink/bloc/audit/audit_equipment_cubit.dart';
import 'package:alink/bloc/service/service_request_bloc.dart';
import 'package:alink/util/application_util.dart';
import 'package:alink/util/enums/app_enum.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import '../data/model/barcode_response.dart';
import '../data/repository/api_service.dart';
import '../pages/airport/repair/assign_equipment/assign_equipment_page.dart';

class AuditBarCodeDialog extends StatefulWidget {
  VoidCallback callback;
  bool closeScreen = false;
  AuditBarCodeDialog({Key? key, required this.callback}) : super(key: key);

  @override
  _AuditBarCodeDialogState createState() => _AuditBarCodeDialogState();
}

class _AuditBarCodeDialogState extends State<AuditBarCodeDialog> {
  late BarcodeSearchResult searchType;
  var _okayText = "Okay";
  late TextEditingController _bardcodeCcontroller;

  @override
  void initState() {
    widget.closeScreen = false;
    searchType = BarcodeSearchResult.INITIAL;
    _bardcodeCcontroller = TextEditingController();
    super.initState();
  }

  @override
  void dispose() {
    _bardcodeCcontroller.dispose();
    super.dispose();
  }

  ServiceRequestBloc get serviceRequestBloc => BlocProvider.of<ServiceRequestBloc>(context);
  ApiBloc get apiBloc => BlocProvider.of<ApiBloc>(context);
  AuditEquipmentCubit get auditEquipmentCubit => BlocProvider.of<AuditEquipmentCubit>(context);

  @override
  Widget build(BuildContext ctx) {
    _okayText = AppLocalizations.of(context)!.okay;
    _bardcodeCcontroller.text = '';
    return AlertDialog(
      titlePadding: const EdgeInsets.only(left: 20),
      title: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(padding: const EdgeInsets.only(right: 120, top: 30), child: Text(AppLocalizations.of(context)!.barcode)),
          Container(
            margin: const EdgeInsets.only(
              right: 10,
            ),
            child: GestureDetector(
                onTap: () {
                  //Navigator.pop(context);
                  widget.callback();
                },
                child: const Icon(Icons.close)),
          ),
        ],
      ),
      contentPadding: const EdgeInsets.only(top: 20),
      content: Container(
        padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const SizedBox(
              height: 10,
            ),
            TextField(
              controller: _bardcodeCcontroller,
              decoration: InputDecoration(
                border: const OutlineInputBorder(
                  borderRadius: BorderRadius.all(
                    Radius.circular(5.0),
                  ),
                ),
                prefixIcon: const Icon(FontAwesomeIcons.barcode),
                hintText: AppLocalizations.of(context)!.enterBarcode,
                labelText: AppLocalizations.of(context)!.barcodeNo,
              ),
              keyboardType: TextInputType.text,
            ),
            const SizedBox(
              height: 20,
            ),
            searchType == BarcodeSearchResult.SEARCHING ? _getLinearProgressBar() : Container()
          ],
        ),
      ),
      actions: [
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            ElevatedButton(
              onPressed: () async {
                if (_okayText == AppLocalizations.of(context)!.okay) {
                  if (_bardcodeCcontroller.text.isEmpty) {
                    ApplicationUtil.showSnackBar(context: context, message: AppLocalizations.of(context)!.enterBarcodeNo);
                  } else {
                    auditEquipmentCubit.updateEquipment(_bardcodeCcontroller.text.trim());
                    await _saveBarcodeDataInDatabase(_bardcodeCcontroller.text.trim());
                    //Navigator.pop(ctx);
                  }
                }
              },
              child: Text(_okayText),
            ),
          ],
        )
      ],
    );
  }

  _getLinearProgressBar() => Column(
        children: const [
          LinearProgressIndicator(),
          SizedBox(
            height: 10,
          ),
        ],
      );

  Future<void> _saveBarcodeDataInDatabase(barcodeTag) async {
    //Navigator.of(context).pop();
    final response = await ApiService().getEquipmentDetailByBarcodeNumber(barcodeTag,false);
    if (response is BarcodeResponse) {
      //serviceRequestBloc.add(DeleteAuditFromTable(auditId: AuditEquipmentCubit.auditId!));
      serviceRequestBloc.add(UpdateBarcodeDataInAudit(AuditEquipmentCubit.auditId!, barcodeTag, null, null, null, const []));
      widget.callback();
    } else if (response == ApiResponse.BAR_CODE_NOT_FOUND) {
      ApplicationUtil.showWarningAlertDialog(
        context,
        title: AppLocalizations.of(context)!.barcodeNotfound,
        desc: 'This barcode ($barcodeTag) is not mapped to any equipment. Do you want to assign this barcode to new equipment?',
        positiveLabel: AppLocalizations.of(context)!.assign,
        negativeLabel: AppLocalizations.of(context)!.cancel,
        onPositiveClickListener: () async {
          log("Scanned barcode and saving in database tag->$barcodeTag");
          Navigator.of(context).pop();
          //print(AuditEquipmentCubit.auditLocation!['LOCATION_ID']);
          final assignedConfirmation = await Navigator.pushNamed(context, AssignEquipmentPage.routeName,
              arguments: ServiceType(
                  type: ServiceRequestType.ASSIGN_EQUIPMENT, barcodeNumber: barcodeTag, fromAudit: true, defaultLocationId: AuditEquipmentCubit.auditLocation!['LOCATION_ID']));
          if (assignedConfirmation == 204) {
            //serviceRequestBloc.add(UpdateBarcodeDataInAudit(AuditEquipmentCubit.auditId!, barcodeTag, null, null, null, const []));
            _saveBarcodeDataInDatabase(barcodeTag);
            log("Scanned barcode and saving in database tag->$barcodeTag");
          }
        },
        onNegativeClickListener: () {},
      );
    } else {
      ApplicationUtil.showWarningAlertDialog(context,
          title: AppLocalizations.of(context)!.error, desc: AppLocalizations.of(context)!.errorOccurWhileFetching, negativeLabel: AppLocalizations.of(context)!.okay);
    }
  }
}
