import 'package:alink/util/app_color.dart';
import 'package:flutter/material.dart';

class AppButton {
  static Widget getAcceptGreenButton(
      {required VoidCallback onTap,
      required String label,
      required Color color}) {
    return ElevatedButton(
      style: ButtonStyle(
        padding: MaterialStateProperty.all(
          const EdgeInsets.symmetric(vertical: 14, horizontal: 20),
        ),
        backgroundColor: MaterialStateProperty.all(color),
        shape: MaterialStateProperty.all<RoundedRectangleBorder>(
          RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15.0),
          ),
        ),
      ),
      onPressed: onTap,
      child: Text(
        label,
        style: const TextStyle(color: Colors.white, fontSize: 16),
      ),
    );
  }

  static ElevatedButton getConfirmTopGreenButton(
      {required VoidCallback? onTap, required String label}) {
    return ElevatedButton(
      style: ButtonStyle(
        padding: MaterialStateProperty.all(
          const EdgeInsets.symmetric(vertical: 0, horizontal: 8),
        ),
        backgroundColor: MaterialStateProperty.all(AppColor.greenSentColor),
        shape: MaterialStateProperty.all<RoundedRectangleBorder>(
          RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(5.0),
          ),
        ),
      ),
      onPressed: onTap,
      child: Text(
        label,
        style: const TextStyle(color: Colors.white, fontSize: 14),
      ),
    );
  }
}

/*class AcceptGreenButton extends ElevatedButton {
  @override
  // TODO: implement style
  ButtonStyle get style => ButtonStyle(
        padding: MaterialStateProperty.all(
          EdgeInsets.symmetric(vertical: 12, horizontal: 20),
        ),
        backgroundColor: MaterialStateProperty.all(AppColor.greenSentColor),
        shape: MaterialStateProperty.all<RoundedRectangleBorder>(
          RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10.0),
          ),
        ),
      );
}*/
