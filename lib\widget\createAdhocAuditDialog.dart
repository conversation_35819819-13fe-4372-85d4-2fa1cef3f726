import 'package:alink/bloc/audit_bloc/audit_bloc.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import '../bloc/api/api_bloc.dart';
import '../data/model/audit_location.dart';
import '../pages/auth/login_page.dart';
import '../util/app_color.dart';
import '../util/application_util.dart';

class CreateAdhocAuditDialog extends StatefulWidget {
  final AuditBloc auditBloc;
  final ApiBloc apiBLoc;
  final BuildContext context;
  final String selectedLocationId;

  const CreateAdhocAuditDialog({Key? key, required this.auditBloc, required this.context, required this.apiBLoc, required this.selectedLocationId}) : super(key: key);

  @override
  State<CreateAdhocAuditDialog> createState() => _CreateAdhocAuditDialogState();
}

class _CreateAdhocAuditDialogState extends State<CreateAdhocAuditDialog> {
  int radioValue = InMemoryAudiData.selectedSegmentValueInMemory!;
  late TextEditingController _auditCommentController;
  int? selectedSegmentValue = 0;

  @override
  void initState() {
    _auditCommentController = TextEditingController();
    if (InMemoryAudiData.selectedSegmentValueInMemory != null) {
      if (InMemoryAudiData.selectedSegmentValueInMemory! == 1 || InMemoryAudiData.selectedSegmentValueInMemory == 0) {
        selectedSegmentValue == 0;
        radioValue = 0;
      } else {
        selectedSegmentValue = 1;
        radioValue = 1;
      }
    } else {
      selectedSegmentValue = 0;
      radioValue = 1;
    }
    super.initState();
  }

  @override
  void dispose() {
    _auditCommentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    AlertDialog alert = AlertDialog(
      title: Text(AppLocalizations.of(context)!.createAdhocAudit, style: TextStyle(fontSize: 20, color: Theme.of(context).primaryColor, fontWeight: FontWeight.bold)),
      content: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          /*Text(
            AppLocalizations.of(context)!.provideCommentForService,
            style: const TextStyle(fontSize: 16),
          ),
          const SizedBox(
            height: 10,
          ),*/
          RichText(
            textAlign: TextAlign.start,
            text: TextSpan(style: const TextStyle(fontSize: 18), children: <TextSpan>[
              const TextSpan(
                text: '* ',
                style: TextStyle(color: AppColor.redColor, fontWeight: FontWeight.bold),
              ),
              TextSpan(text: "Select Service Type", style: TextStyle(color: Theme.of(context).primaryColor, fontWeight: FontWeight.bold)),
            ]),
          ),
          const SizedBox(
            height: 5,
          ),
          buildRadioButtons(radioValue),
          const SizedBox(
            height: 10,
          ),
          RichText(
            textAlign: TextAlign.start,
            text: TextSpan(style: const TextStyle(fontSize: 18), children: <TextSpan>[
              const TextSpan(
                text: '* ',
                style: TextStyle(color: AppColor.redColor, fontWeight: FontWeight.bold),
              ),
              TextSpan(text: AppLocalizations.of(context)!.addComment, style: TextStyle(color: Theme.of(context).primaryColor, fontWeight: FontWeight.bold)),
            ]),
          ),
          const SizedBox(
            height: 5,
          ),
          TextField(
            controller: _auditCommentController,
            decoration: InputDecoration(
              border: const OutlineInputBorder(
                borderRadius: BorderRadius.all(
                  Radius.circular(5.0),
                ),
              ),
              hintText: AppLocalizations.of(context)!.creatingAnService,
            ),
            minLines: 2,
            maxLines: 3,
          ),
        ],
      ),
      actions: [
        BlocConsumer<ApiBloc, ApiState>(
          listener: (context, state) {
            if (state is NewAuditCreated) {
              Navigator.of(context).pop("refresh");
              if (selectedSegmentValue==0) {
                ApplicationUtil.showSnackBar(context: context, message: AppLocalizations.of(context)!.auditCreateSuccessfully);
              }  else{
                ApplicationUtil.showSnackBar(context: context, message: AppLocalizations.of(context)!.taskCreateSuccessfully);
              }
              setState(() {});
              widget.auditBloc.add(FetchAuditList(
                  widget.selectedLocationId, null, _getSelectedValue(InMemoryAudiData.selectedSegmentValueInMemory), InMemoryAudiData.selectedAuditDescriptionInMemory,
                  refresh: true));
            } else if (state is ErrorCreatingNewAudit) {
              //Navigator.of(context).pop();
              if (state.errorMessage == ApiResponse.INVALID_AUTH) {
                Navigator.pushNamedAndRemoveUntil(context, LoginPage.routeName, (route) => false, arguments: true);
              } else {
                //Navigator.of(context).pop();
                state.errorMessage.replaceAll('"', '');
                ApplicationUtil.showWarningAlertDialog(context,
                    title: AppLocalizations.of(context)!.error, desc: state.errorMessage, negativeLabel: AppLocalizations.of(context)!.okay, onNegativeClickListener: () {
                  //Navigator.of(context).pop();
                  Navigator.of(context).pop();
                });
              }
            }
          },
          builder: (context, state) {
            if (state is CreatingNewAudit) {
              return Container(
                margin: const EdgeInsets.symmetric(horizontal: 20),
                width: 25,
                height: 25,
                child: const CircularProgressIndicator(),
              );
            }
            return ElevatedButton(
              style: const ButtonStyle(),
              child: Text(AppLocalizations.of(context)!.create),
              onPressed: () {
                FocusManager.instance.primaryFocus?.unfocus();
                if (_auditCommentController.text.isEmpty) {
                  ApplicationUtil.showSnackBar(context: context, message: AppLocalizations.of(context)!.commentIsRequired);
                } else {
                  widget.apiBLoc.add(CreateNewAudit(locationId: widget.selectedLocationId, comment: _auditCommentController.text.trim(), serviceType: radioValue.toString()));
                }
              },
            );
          },
        ),
        ElevatedButton(
          child: Text(AppLocalizations.of(context)!.cancel),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
      ],
    );
    return alert;
  }

  _getSelectedValue(int? selectedValue) {
    switch (selectedValue) {
      case 0:
        return "ALL";
      case 1:
        return "AUDIT";
      case 2:
        return "TASK";
      default:
        return "ALL";
    }
  }

  Widget buildRadioButtons(serviceTypeInt) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 5),
      child: Container(
        width: double.maxFinite,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          border: Border.all(color: Theme.of(context).primaryColor),
        ),
        child: CupertinoSlidingSegmentedControl<int>(
          thumbColor: Theme.of(context).primaryColor,
          groupValue: selectedSegmentValue,
          children: {
            0: buildSegment("Audits", 0, selectedSegmentValue!),
            1: buildSegment("Tasks", 1, selectedSegmentValue!),
          },
          onValueChanged: (value) {
            setState(() {
              selectedSegmentValue = value;
              radioValue = selectedSegmentValue!;
              print(selectedSegmentValue);
            });
          },
        ),
      ),
    );
  }

  Widget buildSegment(String text, int index, int groupValue) {
    return Container(
      width: double.maxFinite,
      alignment: Alignment.center,
      child: Text(
        text,
        style: TextStyle(fontSize: 18, color: index == groupValue ? Colors.white : Theme.of(context).primaryColor),
      ),
    );
  }
}
