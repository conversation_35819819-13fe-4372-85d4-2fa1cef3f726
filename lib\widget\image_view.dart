import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view_gallery.dart';

import '../data/model/filter_data.dart';
import '../data/model/repair_service_request.dart';
import '../data/model/service_request_detail.dart';
import '../util/application_util.dart';

class ImagePageView extends StatefulWidget {
  final ServiceRequestDetail serviceRequest;
  const ImagePageView({Key? key, required this.serviceRequest}) : super(key: key);

  @override
  State<ImagePageView> createState() => _ImagePageViewState();
}

class _ImagePageViewState extends State<ImagePageView> {
  late int currentIndex = 0;

  void onPageChanged(int index) {
    setState(() {
      currentIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    List imageList = [];
    List<Document>? base64MapList = widget.serviceRequest.document;
    for (var element in base64MapList!) {
      imageList.add(element.dOCUMENTBLOB);
    }
    return Scaffold(
      body: Container(
        child: _imageViewbuilder(imageList),
      ),
      floatingActionButton: ApplicationUtil.getBackButton(
        context,
        onBackPressed: () {
          InMemoryFilterData.clear();
          Navigator.pop(context);
        },
      ),
    );
  }

  _imageViewbuilder(List<dynamic> imageList) {
    return Stack(
      alignment: Alignment.bottomCenter,
      children: [
        PhotoViewGallery.builder(
            backgroundDecoration: const BoxDecoration(color: Colors.white),
            itemCount: imageList.length,
            onPageChanged: onPageChanged,
            //enableRotation: true,
            scrollPhysics: const BouncingScrollPhysics(),
            builder: (_, index) {
              return PhotoViewGalleryPageOptions(
                imageProvider: MemoryImage(
                  base64Decode(imageList[index]),
                ),
                initialScale: 1.0,
                minScale: 1.0 * (0.8 + index / 10),
                maxScale: 1.0 * 4.1,
                heroAttributes: PhotoViewHeroAttributes(tag: index),
              );
            }),
        Container(
          padding: const EdgeInsets.all(20.0),
          child: Text(
            "${currentIndex + 1} of ${imageList.length}",
            style: const TextStyle(
              color: Colors.black,
              fontSize: 18.0,
              decoration: null,
            ),
          ),
        )
      ],
    );
  }
}
