import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

class ScanBarCodeButton {
  static Widget getScanBarcodeButton(
    BuildContext context, {
    required VoidCallback onTap,
    required String label,
  }) {
    return Container(
      alignment: Alignment.bottomCenter,
      padding: const EdgeInsets.symmetric(vertical: 18),
      child: ElevatedButton.icon(
        onPressed: onTap,
        icon: const FaIcon(FontAwesomeIcons.barcode),
        label: Container(
          padding: const EdgeInsets.symmetric(vertical: 18),
          child: Text(
            label,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ),
      ),
    );
  }
}
