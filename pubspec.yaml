name: alink
description: A new Flutter application.

# The following line prevents the package from being accidentally published to
# pub.dev using `pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 1.0.5+1

environment:
  sdk: ">=3.0.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  drift: ^2.19.0
  analyzer: 6.3.0
  sqlite3_flutter_libs: ^0.5.11+1
  path: ^1.8.1
  path_provider: ^2.0.2
  flutter_bloc: ^7.1.0
  # onboard screen
  introduction_screen: ^3.1.12
  # Flutter Permission.
  permission_handler: ^11.3.0
  intl: ^0.19.0
  connectivity_plus: ^2.3.6+1
  #file_picker: ^3.0.3
  crop_image: ^1.0.13
  flutter_image_compress: ^1.1.1
  #image_editor
  image_painter:
#    path: ..\image_annotation\
    git:
      url: https://intranet.indience.in:8443/afs/image-annotation.git
      ref: main
#      ref: downgraded_version
  font_awesome_flutter: ^10.5.0
  shared_preferences: ^2.0.15
  crypto: ^3.0.2
  #logger: ^1.4.0
  local_auth: ^2.1.2
  get_it: 7.2.0
  equatable: ^2.0.3
  package_info_plus: ^4.0.2
  flutter_email_sender: ^5.1.0
  firebase_core: ^3.1.1
  firebase_crashlytics: ^4.0.2
  vibration: ^3.1.3
  location: ^5.0.3
  camera: ^0.10.0+1
  webviewx_plus: ^0.5.1+1
  qr_code_scanner: ^1.0.1
  uuid: ^4.2.2
  photo_view: ^0.14.0
  flutter_svg: ^2.0.7
  #for toast messages(used for success message in barcode scanning)
  fluttertoast: ^8.2.2
  provider:

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.5
  device_info_plus: ^9.0.2
  firebase_analytics: ^11.1.0
  url_launcher: ^6.3.0
  store_redirect: ^2.0.2
  geolocator: ^10.0.0
  #  cupertino_http: ^1.3.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_driver:
    sdk: flutter
  drift_dev: ^2.19.0
  build_runner: ^2.0.6
  flutter_launcher_icons: ^0.9.2
  flutter_lints: ^1.0.0
  permission_handler_android: ^12.0.13

flutter_icons:
  android: "launcher_icon"
  ios: true
  remove_alpha_ios: true
  image_path: "assets/alink.png"

dependency_overrides:
  font_awesome_flutter:
    path: ./font_awesome_flutter-master
  image_picker: ^1.0.0
  intl: ^0.19.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspeccom.lyokone.location.FlutterLocatio

# The following section is specific to Flutter.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  generate: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/
    - assets/images/
    - assets/barcode/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: helvetica
      fonts:
        - asset: assets/fonts/helvetica.ttf
        - asset: assets/fonts/helvetica-bold.ttf
        - asset: assets/fonts/helvetica-light.ttf
  #        style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
